.pbc-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  z-index: 9999;
  display: none;
  align-items: center;
  justify-content: center;
  will-change: opacity;
  backdrop-filter: blur(2px);
}
.pbc-loading-overlay.show {
  display: flex;
}
.pbc-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #0073aa;
  border-radius: 50%;
  animation: pbc-spin 1s linear infinite;
}
@keyframes pbc-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.pbc-price-updated {
  animation: pbc-price-highlight 1s ease-in-out;
  will-change: background-color;
}
@keyframes pbc-price-highlight {
  0% {
    background-color: transparent;
  }
  50% {
    background-color: #fff3cd;
  }
  100% {
    background-color: transparent;
  }
}
.pbc-product-loading {
  opacity: 0.7;
  transition: opacity 0.2s ease;
}
.pbc-product-loaded {
  opacity: 1;
}
.pbc-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 4px;
  color: #fff;
  font-weight: 500;
  z-index: 10000;
  display: none;
  max-width: 300px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
.pbc-notification-success {
  background-color: #28a745;
  border-left: 4px solid #1e7e34;
}
.pbc-notification-error {
  background-color: #dc3545;
  border-left: 4px solid #c82333;
}
.pbc-notification-info {
  background-color: #17a2b8;
  border-left: 4px solid #138496;
}
.pbc-notification-warning {
  background-color: #ffc107;
  color: #212529;
  border-left: 4px solid #e0a800;
}
.pbc-country-selector {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fff;
  font-size: 14px;
  min-width: 150px;
}
.pbc-country-selector:focus {
  outline: none;
  border-color: #0073aa;
  box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.2);
}
.price.pbc-country-price {
  position: relative;
}
.price.pbc-country-price::after {
  content: attr(data-country);
  font-size: 0.8em;
  color: #666;
  margin-left: 5px;
  font-weight: normal;
}
.woocommerce-checkout .pbc-price-updating {
  opacity: 0.6;
  transition: opacity 0.3s ease;
}
.woocommerce-checkout .pbc-price-updating::after {
  content: " (updating...)";
  font-size: 0.9em;
  color: #666;
  font-style: italic;
}
.woocommerce-cart .cart_item.pbc-updating {
  background-color: #f8f9fa;
  transition: background-color 0.3s ease;
}
.single_variation .price.pbc-price-updated {
  border: 2px solid #0073aa;
  padding: 5px;
  border-radius: 3px;
  transition: all 0.3s ease;
}
@media (max-width: 768px) {
  .pbc-notification {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
  .pbc-spinner {
    width: 30px;
    height: 30px;
    border-width: 3px;
  }
  .pbc-country-selector {
    width: 100%;
    margin-bottom: 10px;
  }
}
@media (prefers-contrast: high) {
  .pbc-notification {
    border: 2px solid currentColor;
  }
  .pbc-price-updated {
    border: 2px solid #000;
  }
}
@media (prefers-reduced-motion: reduce) {
  .pbc-spinner {
    animation: none;
    border-top-color: transparent;
  }
  .pbc-price-updated {
    animation: none;
    background-color: #fff3cd;
  }
  .pbc-notification {
    transition: none;
  }
}
@media print {
  .pbc-loading-overlay,
  .pbc-notification {
    display: none !important;
  }
}
.pbc-country-selector:focus-visible {
  outline: 2px solid #0073aa;
  outline-offset: 2px;
}
.pbc-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
.pbc-checkout-notification {
  background: #d1ecf1;
  border: 1px solid #bee5eb;
  border-left: 4px solid #17a2b8;
  color: #0c5460;
  padding: 12px 15px;
  margin: 15px 0;
  border-radius: 4px;
  font-size: 14px;
}
.pbc-checkout-notification::before {
  content: "ℹ ";
  font-weight: bold;
  margin-right: 5px;
}
.woocommerce-checkout.pbc-updating {
  position: relative;
}
.woocommerce-checkout.pbc-updating::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  z-index: 100;
  pointer-events: none;
}
.checkout-review-order-table.pbc-price-updating {
  opacity: 0.6;
  transition: opacity 0.3s ease;
}
.pbc-updating-message {
  font-size: 0.9em;
  color: #666;
  font-style: italic;
  animation: pbc-pulse 1.5s ease-in-out infinite;
}
@keyframes pbc-pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
.order-total .pbc-price-change {
  display: inline-block;
  padding: 2px 6px;
  background: #28a745;
  color: white;
  border-radius: 3px;
  font-size: 0.8em;
  margin-left: 5px;
  animation: pbc-highlight-fade 3s ease-out;
}
@keyframes pbc-highlight-fade {
  0% {
    background-color: #28a745;
  }
  100% {
    background-color: transparent;
    color: inherit;
  }
}
