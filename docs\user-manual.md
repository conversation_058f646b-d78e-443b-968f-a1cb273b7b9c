# Price by Country for WooCommerce - User Manual

## Table of Contents

1. [Getting Started](#getting-started)
2. [Admin Interface Overview](#admin-interface-overview)
3. [Setting Up Pricing Rules](#setting-up-pricing-rules)
4. [Country Detection Configuration](#country-detection-configuration)
5. [Product-Specific Pricing](#product-specific-pricing)
6. [Category-Based Pricing](#category-based-pricing)
7. [Global Pricing Rules](#global-pricing-rules)
8. [Import and Export](#import-and-export)
9. [Performance and Caching](#performance-and-caching)
10. [Troubleshooting](#troubleshooting)
11. [Frequently Asked Questions](#frequently-asked-questions)

## Getting Started

### What is Price by Country?

Price by Country for WooCommerce allows you to set different prices for your products based on your customers' location. This powerful plugin automatically detects where your customers are located and displays the appropriate pricing, helping you optimize your sales strategy for different markets.

### Key Features

- **Automatic Country Detection**: Uses IP address, billing address, or shipping address
- **Flexible Pricing Rules**: Set fixed amounts or percentage adjustments
- **Multiple Rule Levels**: Global, category, and product-specific pricing
- **Performance Optimized**: Built-in caching for fast page loads
- **Easy Management**: Intuitive admin interface for all settings
- **Import/Export**: Bulk manage pricing rules with CSV files

### Quick Start Guide

1. **Install and Activate** the plugin (see Installation Guide)
2. **Run the Setup Wizard** to configure basic settings
3. **Create Your First Pricing Rule** for a test product
4. **Test the Functionality** by changing your location
5. **Expand Your Rules** to cover all products and countries

## Admin Interface Overview

### Main Navigation

After installing the plugin, you'll find new menu items in your WordPress admin:

#### WooCommerce Settings Integration

- **Location**: `WooCommerce → Settings → Price by Country`
- **Purpose**: Configure global plugin settings and country detection

#### Dedicated Plugin Dashboard

- **Location**: `WooCommerce → Price by Country`
- **Purpose**: Manage all pricing rules and view analytics

#### Product-Level Settings

- **Location**: `Products → Edit Product → Price by Country Tab`
- **Purpose**: Set specific pricing for individual products

### Dashboard Overview

The main dashboard provides:

- **Quick Stats**: Overview of active rules and countries
- **Recent Activity**: Latest pricing rule changes
- **Performance Metrics**: Cache hit rates and page load impact
- **Quick Actions**: Common tasks like adding rules or importing data

## Setting Up Pricing Rules

### Understanding Rule Types

The plugin supports three types of pricing rules:

#### 1. Global Rules

- Apply to all products unless overridden
- Set default pricing for different countries
- Useful for store-wide adjustments

#### 2. Category Rules

- Apply to all products in a specific category
- Override global rules for that category
- Perfect for product-line specific pricing

#### 3. Product Rules

- Apply to individual products only
- Override both global and category rules
- Maximum control for specific items

### Rule Priority System

When multiple rules could apply to a product, the plugin follows this priority:

1. **Product-specific rules** (highest priority)
2. **Category rules** (medium priority)
3. **Global rules** (lowest priority)

### Adjustment Types

#### Fixed Amount Adjustments

- Add or subtract a specific dollar amount
- Example: +$5.00 for Canada, -$2.00 for Mexico
- Best for: Products with consistent markup needs

#### Percentage Adjustments

- Increase or decrease by a percentage
- Example: +15% for UK, -10% for developing markets
- Best for: Maintaining profit margins across different markets

## Country Detection Configuration

### Detection Methods

#### Automatic Detection (Recommended)

- Uses multiple methods in priority order
- Default priority: Shipping Address → Billing Address → IP Address
- Most accurate for returning customers

#### IP Address Only

- Uses visitor's IP address via geolocation services
- Works for first-time visitors
- May be less accurate with VPNs

#### Billing Address Only

- Uses customer's billing country
- Requires customer to enter billing information
- Most accurate but requires customer input

#### Shipping Address Only

- Uses customer's shipping country
- Good for physical products
- May not work for digital products

### Configuring Detection Settings

1. **Navigate to Settings**:

   ```
   WooCommerce → Settings → Price by Country → Detection
   ```

2. **Choose Detection Method**:

   - Select your preferred method from the dropdown
   - Configure priority order for automatic detection

3. **Set Default Country**:

   - Choose fallback country when detection fails
   - Usually your store's base country

4. **Configure Caching**:
   - Set how long to cache detection results
   - Balance between accuracy and performance

### Testing Country Detection

#### Using Browser Developer Tools

1. Open browser developer tools (F12)
2. Go to Network tab → Conditions
3. Override geolocation to test different countries
4. Refresh your store to see price changes

#### Using VPN Services

1. Connect to VPN server in target country
2. Clear browser cache and cookies
3. Visit your store in incognito/private mode
4. Verify prices display correctly

## Product-Specific Pricing

### Setting Up Product Pricing

#### Step 1: Edit Product

1. Go to `Products → All Products`
2. Click "Edit" on the product you want to configure
3. Look for the "Price by Country" tab or section

#### Step 2: Configure Pricing Rules

1. **Enable Country Pricing**: Check the "Enable price by country" box
2. **Choose Inheritance**: Decide if this product should inherit from category/global rules
3. **Add Country Rules**: Click "Add Country Rule" to create specific pricing

#### Step 3: Set Pricing Details

For each country rule:

- **Country**: Select the target country from dropdown
- **Adjustment Type**: Choose "Fixed Amount" or "Percentage"
- **Adjustment Value**: Enter the adjustment amount
- **Status**: Enable or disable the rule

### Example Product Configuration

Let's say you have a $100 product and want to set these prices:

- **United States**: $100.00 (base price)
- **United Kingdom**: $115.00 (+15% for VAT and shipping)
- **Canada**: $95.00 (-5% for competitive pricing)
- **Australia**: $120.00 (+20% for shipping costs)

**Configuration Steps**:

1. Base price: $100.00 in product settings
2. UK rule: Percentage adjustment +15%
3. Canada rule: Fixed adjustment -$5.00
4. Australia rule: Percentage adjustment +20%

### Bulk Product Configuration

#### Using Category Inheritance

1. Set up category-level rules first
2. Enable "Inherit from category" on products
3. Override specific products as needed

#### Using Import/Export

1. Export current product pricing
2. Modify the CSV file with new rules
3. Import the updated file

## Category-Based Pricing

### Setting Up Category Pricing

#### Step 1: Access Category Settings

1. Go to `Products → Categories`
2. Click "Edit" on the category you want to configure
3. Scroll to the "Price by Country" section

#### Step 2: Configure Category Rules

1. **Enable Category Pricing**: Check the enable box
2. **Set Default Behavior**: Choose how products inherit these rules
3. **Add Country Rules**: Create rules for different countries

### Category Rule Examples

#### Electronics Category

- **Base Country (US)**: No adjustment
- **Europe**: +20% (for VAT and compliance costs)
- **Asia**: -10% (for competitive pricing)
- **Canada**: +5% (for currency fluctuation)

#### Clothing Category

- **Base Country (US)**: No adjustment
- **UK**: +15% (for sizing differences and shipping)
- **Australia**: +25% (for seasonal differences)
- **Mexico**: -15% (for market positioning)

### Managing Category Inheritance

#### Override Settings

Products can be set to:

- **Inherit completely**: Use all category rules
- **Inherit with exceptions**: Use category rules but override specific countries
- **No inheritance**: Use only product-specific rules

#### Bulk Category Updates

1. Select multiple categories
2. Use "Bulk Actions" → "Update Pricing Rules"
3. Apply changes to all selected categories

## Global Pricing Rules

### Understanding Global Rules

Global rules serve as the foundation for your pricing strategy:

- Apply to all products by default
- Can be overridden by category or product rules
- Useful for store-wide adjustments

### Setting Up Global Rules

#### Step 1: Access Global Settings

1. Go to `WooCommerce → Price by Country → Global Rules`
2. Click "Add New Global Rule"

#### Step 2: Configure Global Rule

1. **Country**: Select target country
2. **Adjustment Type**: Fixed amount or percentage
3. **Adjustment Value**: Enter the adjustment
4. **Priority**: Set rule priority (higher numbers = higher priority)
5. **Status**: Enable or disable the rule

### Global Rule Strategies

#### Market-Based Pricing

- **Developed Markets**: +10-20% for higher purchasing power
- **Developing Markets**: -10-30% for affordability
- **Competitive Markets**: Adjust based on local competition

#### Currency-Based Adjustments

- **Strong Currencies**: Slight premium for favorable exchange rates
- **Weak Currencies**: Discounts to maintain affordability
- **Volatile Currencies**: Regular adjustments based on exchange rates

### Managing Global Rules

#### Rule Priority

When multiple global rules could apply:

1. Higher priority numbers take precedence
2. More specific rules override general ones
3. Newer rules can override older ones (if same priority)

#### Bulk Global Updates

1. Select multiple global rules
2. Use bulk actions to enable/disable or modify
3. Export/import for major changes

## Import and Export

### Exporting Pricing Rules

#### Export Options

1. **All Rules**: Export everything for backup
2. **By Type**: Export only global, category, or product rules
3. **By Country**: Export rules for specific countries
4. **By Date Range**: Export recent changes

#### Export Process

1. Go to `WooCommerce → Price by Country → Import/Export`
2. Select "Export" tab
3. Choose export options
4. Click "Download Export File"

### Importing Pricing Rules

#### Import File Format

The CSV file should contain these columns:

- `rule_type`: global, category, or product
- `object_id`: category ID or product ID (empty for global)
- `country_code`: Two-letter country code (US, GB, CA, etc.)
- `adjustment_type`: fixed or percentage
- `adjustment_value`: Numeric value for adjustment
- `is_active`: 1 for active, 0 for inactive

#### Import Process

1. Prepare your CSV file with the correct format
2. Go to `WooCommerce → Price by Country → Import/Export`
3. Select "Import" tab
4. Upload your CSV file
5. Map columns if needed
6. Review preview and confirm import

### Sample Import File

```csv
rule_type,object_id,country_code,adjustment_type,adjustment_value,is_active
global,,GB,percentage,15,1
global,,CA,fixed,-5,1
category,25,AU,percentage,20,1
product,150,DE,percentage,12,1
product,150,FR,percentage,10,1
```

## Performance and Caching

### Understanding Performance Impact

The plugin is designed to be lightweight, but pricing calculations can impact performance:

- **Database queries**: Each pricing rule requires database access
- **Calculation overhead**: Complex rules take more processing time
- **Cache misses**: Uncached calculations slow down page loads

### Caching Configuration

#### Cache Settings

1. Go to `WooCommerce → Settings → Price by Country → Performance`
2. Configure these options:
   - **Enable Caching**: Turn caching on/off
   - **Cache Duration**: How long to cache results
   - **Cache Method**: WordPress transients or object cache
   - **Preload Rules**: Load common rules into cache

#### Cache Types

##### WordPress Transients (Default)

- Built into WordPress
- Stored in database
- Good for most sites

##### Object Cache (Advanced)

- Requires Redis or Memcached
- Faster than transients
- Better for high-traffic sites

### Performance Optimization Tips

#### Rule Organization

- Use fewer, more general rules when possible
- Avoid overlapping rules that require complex calculations
- Regularly clean up unused rules

#### Database Optimization

- Keep your database tables optimized
- Use appropriate indexes (handled automatically)
- Monitor query performance

#### Server-Level Caching

- Use page caching plugins (WP Rocket, W3 Total Cache)
- Implement server-level caching (Varnish, Nginx)
- Use CDN for static assets

## Troubleshooting

### Common Issues and Solutions

#### Issue: Prices Not Changing Based on Country

**Possible Causes**:

- Country detection not working
- No pricing rules configured
- Rules disabled or inactive
- Cache not clearing

**Solutions**:

1. **Check Country Detection**:

   - Test with different IP addresses or VPN
   - Verify detection method in settings
   - Check if default country is set correctly

2. **Verify Pricing Rules**:

   - Ensure rules exist for the test country
   - Check that rules are active/enabled
   - Verify rule priority and inheritance

3. **Clear Caches**:
   - Clear plugin cache
   - Clear WordPress cache
   - Clear browser cache

#### Issue: Admin Interface Not Showing

**Possible Causes**:

- Plugin not activated properly
- User permissions insufficient
- JavaScript conflicts
- Theme compatibility issues

**Solutions**:

1. **Check Plugin Status**:

   - Verify plugin is activated
   - Check for any error messages
   - Try deactivating/reactivating

2. **Check User Permissions**:

   - Ensure user has administrator role
   - Check WooCommerce permissions
   - Test with different user account

3. **Debug JavaScript Issues**:
   - Check browser console for errors
   - Disable other plugins temporarily
   - Switch to default theme for testing

#### Issue: Performance Problems

**Possible Causes**:

- Too many pricing rules
- Inefficient rule configuration
- Caching disabled
- Database performance issues

**Solutions**:

1. **Optimize Rules**:

   - Consolidate similar rules
   - Remove unused rules
   - Use more general rules where possible

2. **Enable Caching**:

   - Turn on plugin caching
   - Increase cache duration
   - Use object cache if available

3. **Database Optimization**:
   - Optimize database tables
   - Check for slow queries
   - Consider database server upgrades

### Debug Mode

#### Enabling Debug Mode

1. Go to `WooCommerce → Settings → Price by Country → Debug`
2. Enable "Debug Mode"
3. Set log level to "Debug" for detailed information
4. Check logs at `WooCommerce → Status → Logs`

#### Reading Debug Logs

Debug logs show:

- Country detection results
- Pricing rule applications
- Cache hit/miss information
- Performance metrics
- Error messages

### Getting Help

#### Before Contacting Support

1. **Check Documentation**: Review this manual and installation guide
2. **Search FAQ**: Look through frequently asked questions
3. **Test in Isolation**: Disable other plugins to test for conflicts
4. **Gather Information**: Collect error messages, screenshots, and system info

#### Support Channels

- **Documentation**: Complete guides and tutorials
- **Support Forum**: Community help and discussions
- **Direct Support**: Premium support with license key
- **Video Tutorials**: Step-by-step visual guides

## Frequently Asked Questions

### General Questions

#### Q: Can I set different prices for different countries?

**A**: Yes, that's the main purpose of this plugin. You can set country-specific pricing using fixed amounts or percentage adjustments at the global, category, or product level.

#### Q: How does the plugin detect a customer's country?

**A**: The plugin can use several methods: IP address geolocation, billing address, shipping address, or a combination of these methods in priority order.

#### Q: Will this plugin slow down my website?

**A**: The plugin is optimized for performance with built-in caching. When properly configured, the performance impact is minimal.

#### Q: Can I use this plugin with other WooCommerce extensions?

**A**: Yes, the plugin is designed to be compatible with most WooCommerce extensions. It integrates with the standard WooCommerce pricing system.

### Pricing and Rules

#### Q: Can I set different prices for product variations?

**A**: Yes, you can set country-specific pricing for each product variation individually.

#### Q: What happens if I don't set a price for a specific country?

**A**: The plugin will fall back to the next available rule: product → category → global → base price.

#### Q: Can I set negative adjustments to offer discounts?

**A**: Yes, you can use negative values for both fixed amounts and percentages to offer discounts for specific countries.

#### Q: How do I handle tax-inclusive vs tax-exclusive pricing?

**A**: The plugin works with WooCommerce's tax system. Set your base prices as tax-exclusive or tax-inclusive, and the country adjustments will be applied accordingly.

### Technical Questions

#### Q: Does this work with caching plugins?

**A**: Yes, the plugin is compatible with most caching plugins. You may need to exclude certain pages from caching or configure cache purging.

#### Q: Can I use this with a CDN?

**A**: Yes, but you may need to configure your CDN to pass through country detection headers or use edge-side includes for dynamic pricing.

#### Q: Is this plugin GDPR compliant?

**A**: The plugin doesn't store personal data beyond what WooCommerce already stores. IP-based country detection is anonymous and temporary.

#### Q: Does this work with WooCommerce subscriptions?

**A**: Yes, the plugin works with subscription products. Country-specific pricing will be applied to subscription fees.

### Setup and Configuration

#### Q: Do I need to set up rules for every country?

**A**: No, you only need to set up rules for countries where you want different pricing. All other countries will use your base prices.

#### Q: Can I import pricing rules from a spreadsheet?

**A**: Yes, the plugin includes import/export functionality that works with CSV files.

#### Q: How do I test the plugin before going live?

**A**: Use the debug mode, test with VPN services, or use browser developer tools to simulate different countries.

#### Q: Can I schedule price changes?

**A**: The plugin doesn't include scheduling features, but you can use WordPress cron jobs or third-party scheduling plugins to activate/deactivate rules.

### Business Questions

#### Q: Can I show prices in different currencies?

**A**: This plugin adjusts prices but doesn't change currencies. For multi-currency support, you'll need a separate currency switcher plugin.

#### Q: How do I handle shipping costs for different countries?

**A**: This plugin only handles product pricing. Use WooCommerce's built-in shipping zones and methods for country-specific shipping costs.

#### Q: Can I restrict products to certain countries?

**A**: This plugin handles pricing only. For country restrictions, you'll need a separate geolocation restriction plugin.

#### Q: How do I handle returns and refunds with different pricing?

**A**: WooCommerce handles returns based on the original purchase price, which will include any country-specific adjustments that were applied.

### Troubleshooting FAQ

#### Q: Why aren't my price changes showing up?

**A**: Check that your rules are active, clear all caches, and verify that country detection is working correctly.

#### Q: The admin interface isn't loading properly. What should I do?

**A**: Check for JavaScript errors in your browser console, try disabling other plugins, and ensure your user has proper permissions.

#### Q: How do I reset all pricing rules?

**A**: You can use the bulk actions in the admin interface to delete all rules, or deactivate and reactivate the plugin to start fresh.

#### Q: The plugin is conflicting with another plugin. How do I resolve this?

**A**: Try deactivating other plugins one by one to identify the conflict, then contact support for assistance with compatibility issues.

---

## Need More Help?

This user manual covers the most common use cases and questions. For additional support:

- **Installation Guide**: Detailed setup instructions
- **Developer Guide**: Technical implementation details
- **API Reference**: Integration with other systems
- **Video Tutorials**: Step-by-step visual guides
- **Support Forum**: Community discussions and help
- **Direct Support**: Premium support for license holders

Remember to keep your plugin updated to the latest version for the best performance and newest features!
