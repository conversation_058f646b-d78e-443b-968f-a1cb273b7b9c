<?php
/**
 * Cache management class for Price by Country
 *
 * @package PriceByCountry
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PBC Cache Manager Class
 */
class PBC_Cache_Manager {

    /**
     * Pricing engine instance
     *
     * @var PBC_Pricing_Engine
     */
    private $pricing_engine;

    /**
     * Country detector instance
     *
     * @var PBC_Country_Detector
     */
    private $country_detector;

    /**
     * Database instance
     *
     * @var PBC_Database
     */
    private $database;

    /**
     * Logger instance
     *
     * @var PBC_Logger
     */
    private $logger;

    /**
     * Constructor
     *
     * @param PBC_Pricing_Engine $pricing_engine Pricing engine instance
     * @param PBC_Country_Detector $country_detector Country detector instance
     * @param PBC_Database $database Database instance
     */
    public function __construct($pricing_engine, $country_detector, $database) {
        $this->pricing_engine = $pricing_engine;
        $this->country_detector = $country_detector;
        $this->database = $database;
        $this->logger = PBC_Logger::get_instance();
    }

    /**
     * Warm up cache for popular products and countries
     *
     * @param int $limit Number of products to warm up
     * @return array Warming results
     */
    public function warm_popular_cache($limit = 50) {
        $start_time = microtime(true);
        $results = [
            'products_processed' => 0,
            'countries_processed' => 0,
            'price_cache_entries' => 0,
            'rule_cache_entries' => 0,
            'detection_cache_entries' => 0,
            'execution_time' => 0
        ];

        try {
            // Get popular products (by sales or views)
            $popular_products = $this->get_popular_products($limit);
            
            // Get active countries with pricing rules
            $active_countries = $this->database->get_countries_in_use();
            
            if (empty($popular_products) || empty($active_countries)) {
                return $results;
            }

            // Warm pricing cache
            $results['price_cache_entries'] = $this->pricing_engine->warm_cache(
                $popular_products, 
                $active_countries
            );

            // Preload pricing rules
            $results['rule_cache_entries'] = $this->pricing_engine->preload_pricing_rules(
                $popular_products, 
                $active_countries
            );

            // Warm detection cache for recent IPs and users
            $results['detection_cache_entries'] = $this->warm_detection_cache();

            $results['products_processed'] = count($popular_products);
            $results['countries_processed'] = count($active_countries);

            // Log cache warming
            $this->logger->log_cache_operation('warm_popular_cache', $results);

        } catch (Exception $e) {
            error_log('PBC Cache Manager: Error warming cache - ' . $e->getMessage());
        }

        $results['execution_time'] = microtime(true) - $start_time;
        return $results;
    }

    /**
     * Warm detection cache for recent visitors
     *
     * @return int Number of cache entries created
     */
    private function warm_detection_cache() {
        // Get recent IP addresses from access logs or analytics
        $recent_ips = $this->get_recent_visitor_ips();
        
        // Get active user IDs
        $active_users = $this->get_active_user_ids();

        return $this->country_detector->warm_detection_cache($recent_ips, $active_users);
    }

    /**
     * Get popular products based on sales or views
     *
     * @param int $limit Number of products to return
     * @return array Array of product IDs
     */
    private function get_popular_products($limit = 50) {
        global $wpdb;

        // Try to get by sales first (if WooCommerce analytics available)
        $popular_by_sales = $wpdb->get_col(
            $wpdb->prepare(
                "SELECT p.ID 
                FROM {$wpdb->posts} p
                LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = 'total_sales'
                WHERE p.post_type = 'product' 
                AND p.post_status = 'publish'
                ORDER BY CAST(COALESCE(pm.meta_value, 0) AS UNSIGNED) DESC
                LIMIT %d",
                $limit
            )
        );

        if (!empty($popular_by_sales)) {
            return array_map('intval', $popular_by_sales);
        }

        // Fallback to recently modified products
        $recent_products = get_posts([
            'post_type' => 'product',
            'post_status' => 'publish',
            'posts_per_page' => $limit,
            'orderby' => 'modified',
            'order' => 'DESC',
            'fields' => 'ids'
        ]);

        return array_map('intval', $recent_products);
    }

    /**
     * Get recent visitor IP addresses
     *
     * @param int $limit Number of IPs to return
     * @return array Array of IP addresses
     */
    private function get_recent_visitor_ips($limit = 100) {
        global $wpdb;

        // Get from country cache table
        $recent_ips = $wpdb->get_col(
            $wpdb->prepare(
                "SELECT DISTINCT ip_address 
                FROM {$this->database->get_country_cache_table()} 
                WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
                ORDER BY created_at DESC
                LIMIT %d",
                $limit
            )
        );

        return $recent_ips ?: [];
    }

    /**
     * Get active user IDs
     *
     * @param int $limit Number of users to return
     * @return array Array of user IDs
     */
    private function get_active_user_ids($limit = 100) {
        global $wpdb;

        // Get users who have been active recently
        $active_users = $wpdb->get_col(
            $wpdb->prepare(
                "SELECT DISTINCT user_id 
                FROM {$wpdb->usermeta} 
                WHERE meta_key = 'last_activity' 
                AND meta_value > %s
                LIMIT %d",
                date('Y-m-d H:i:s', strtotime('-7 days')),
                $limit
            )
        );

        // Fallback to recent customers
        if (empty($active_users)) {
            $active_users = $wpdb->get_col(
                $wpdb->prepare(
                    "SELECT DISTINCT pm.meta_value as user_id
                    FROM {$wpdb->posts} p
                    JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
                    WHERE p.post_type = 'shop_order'
                    AND pm.meta_key = '_customer_user'
                    AND pm.meta_value > 0
                    AND p.post_date > DATE_SUB(NOW(), INTERVAL 30 DAY)
                    ORDER BY p.post_date DESC
                    LIMIT %d",
                    $limit
                )
            );
        }

        return array_map('intval', array_filter($active_users));
    }

    /**
     * Schedule cache warming via WordPress cron
     */
    public function schedule_cache_warming() {
        if (!wp_next_scheduled('pbc_warm_cache')) {
            wp_schedule_event(time(), 'hourly', 'pbc_warm_cache');
        }
    }

    /**
     * Unschedule cache warming
     */
    public function unschedule_cache_warming() {
        wp_clear_scheduled_hook('pbc_warm_cache');
    }

    /**
     * Clear all plugin caches
     *
     * @return array Clearing results
     */
    public function clear_all_caches() {
        $start_time = microtime(true);
        
        // Clear pricing engine caches
        $this->pricing_engine->clear_all_price_cache();
        
        // Clear country detection caches
        $this->country_detector->clear_all_cache();
        
        // Clear database cache entries
        $this->database->cleanup_expired_cache();
        
        $results = [
            'success' => true,
            'execution_time' => microtime(true) - $start_time,
            'timestamp' => current_time('mysql')
        ];

        // Log cache clearing
        $this->logger->log_cache_operation('clear_all_caches', $results);

        return $results;
    }

    /**
     * Get comprehensive cache statistics
     *
     * @return array Cache statistics
     */
    public function get_cache_statistics() {
        $pricing_stats = $this->pricing_engine->get_pricing_stats();
        $detection_stats = $this->country_detector->get_cache_stats();
        $db_stats = $this->database->get_stats();

        return [
            'pricing_cache' => $pricing_stats['cache_entries'],
            'detection_cache' => $detection_stats,
            'database_cache' => [
                'cache_entries' => $db_stats['cache_entries'],
                'expired_cache' => $db_stats['expired_cache']
            ],
            'total_cache_entries' => $pricing_stats['cache_entries']['total'] + $detection_stats['total'],
            'last_updated' => current_time('mysql')
        ];
    }

    /**
     * Optimize cache by removing expired entries
     *
     * @return array Optimization results
     */
    public function optimize_cache() {
        $start_time = microtime(true);
        
        // Clean up expired database cache
        $expired_cleaned = $this->database->cleanup_expired_cache();
        
        // WordPress handles transient cleanup automatically, but we can force it
        delete_expired_transients(true);
        
        $results = [
            'expired_entries_cleaned' => $expired_cleaned,
            'execution_time' => microtime(true) - $start_time,
            'timestamp' => current_time('mysql')
        ];

        // Log cache optimization
        $this->logger->log_cache_operation('optimize_cache', $results);

        return $results;
    }

    /**
     * Preload cache for specific products and countries
     *
     * @param array $product_ids Product IDs to preload
     * @param array $country_codes Country codes to preload
     * @return array Preloading results
     */
    public function preload_specific_cache($product_ids, $country_codes) {
        $start_time = microtime(true);
        
        $results = [
            'price_cache_entries' => 0,
            'rule_cache_entries' => 0,
            'products_processed' => count($product_ids),
            'countries_processed' => count($country_codes),
            'execution_time' => 0
        ];

        if (!empty($product_ids) && !empty($country_codes)) {
            // Preload pricing rules
            $results['rule_cache_entries'] = $this->pricing_engine->preload_pricing_rules(
                $product_ids, 
                $country_codes
            );

            // Warm price cache
            $results['price_cache_entries'] = $this->pricing_engine->warm_cache(
                $product_ids, 
                $country_codes
            );
        }

        $results['execution_time'] = microtime(true) - $start_time;

        // Log preloading
        $this->logger->log_cache_operation('preload_specific_cache', $results);

        return $results;
    }

    /**
     * Get cache hit rate statistics
     *
     * @return array Cache hit rate data
     */
    public function get_cache_hit_rates() {
        // This would require implementing hit/miss tracking in the actual cache methods
        // For now, return placeholder data structure
        return [
            'pricing_cache_hit_rate' => 0,
            'detection_cache_hit_rate' => 0,
            'overall_hit_rate' => 0,
            'total_requests' => 0,
            'cache_hits' => 0,
            'cache_misses' => 0
        ];
    }

    /**
     * Invalidate cache for specific product
     *
     * @param int $product_id Product ID
     * @param string|null $country_code Optional country code
     */
    public function invalidate_product_cache($product_id, $country_code = null) {
        $this->pricing_engine->clear_price_cache($product_id, $country_code);
        $this->pricing_engine->clear_rule_cache($product_id, $country_code);
        
        // Log cache invalidation
        $this->logger->log_cache_operation('invalidate_product_cache', [
            'product_id' => $product_id,
            'country_code' => $country_code,
            'timestamp' => current_time('mysql')
        ]);
    }

    /**
     * Invalidate cache for specific user
     *
     * @param int $user_id User ID
     */
    public function invalidate_user_cache($user_id) {
        $this->country_detector->clear_user_cache($user_id);
        
        // Log cache invalidation
        $this->logger->log_cache_operation('invalidate_user_cache', [
            'user_id' => $user_id,
            'timestamp' => current_time('mysql')
        ]);
    }
}