# Price by Country for WooCommerce - Developer Guide

## Table of Contents

1. [Plugin Architecture](#plugin-architecture)
2. [Core Components](#core-components)
3. [Hooks and Filters](#hooks-and-filters)
4. [Database Schema](#database-schema)
5. [API Integration](#api-integration)
6. [Caching System](#caching-system)
7. [Error Handling](#error-handling)
8. [Extending the Plugin](#extending-the-plugin)

## Plugin Architecture

The Price by Country for WooCommerce plugin follows a modular architecture with clear separation of concerns. The plugin is built using object-oriented PHP and follows WordPress coding standards.

### Directory Structure

```
price-by-country-woocommerce/
├── price-by-country.php          # Main plugin file
├── includes/                     # Core plugin classes
│   ├── class-pbc-core.php       # Main plugin controller
│   ├── class-pbc-pricing-engine.php  # Pricing calculation logic
│   ├── class-pbc-country-detector.php # Country detection service
│   ├── class-pbc-admin.php      # Admin interface controller
│   ├── class-pbc-api.php        # REST API extensions
│   ├── class-pbc-database.php   # Database operations
│   ├── class-pbc-hooks.php      # WooCommerce integration
│   ├── class-pbc-error-handler.php # Error handling
│   ├── class-pbc-logger.php     # Logging system
│   ├── class-pbc-cache-manager.php # Cache management
│   ├── class-pbc-compatibility-checker.php # Compatibility checks
│   └── class-pbc-setup-wizard.php # Setup wizard
├── admin/                        # Admin interface assets
│   ├── css/                     # Admin stylesheets
│   ├── js/                      # Admin JavaScript
│   └── views/                   # Admin template files
├── public/                       # Frontend assets
│   ├── css/                     # Frontend stylesheets
│   └── js/                      # Frontend JavaScript
├── tests/                        # Test suite
│   ├── unit/                    # Unit tests
│   └── integration/             # Integration tests
└── languages/                    # Translation files
```

### Plugin Initialization

The plugin follows a singleton pattern for the main core class:

```php
// Get plugin instance
$pbc = PBC_Core::get_instance();

// Access components
$pricing_engine = $pbc->pricing_engine;
$country_detector = $pbc->country_detector;
$database = $pbc->database;
```

## Core Components

### PBC_Core

The main plugin controller that initializes all components and manages the plugin lifecycle.

**Key Methods:**

- `get_instance()` - Returns singleton instance
- `init()` - Initializes the plugin
- `activate()` - Plugin activation handler
- `deactivate()` - Plugin deactivation handler
- `uninstall()` - Plugin uninstall handler

### PBC_Pricing_Engine

Handles all pricing calculations and rule hierarchy management.

**Key Methods:**

- `get_price_adjustment($product_id, $country_code, $base_price)` - Calculate price adjustment
- `get_country_price($product_id, $country_code)` - Get final country-specific price
- `find_applicable_rule($product_id, $country_code)` - Find applicable pricing rule
- `get_batch_price_adjustments($product_ids, $country_code)` - Batch price calculations

**Rule Hierarchy:**

1. Product-specific rules (highest priority)
2. Category rules (medium priority)
3. Global rules (lowest priority)

### PBC_Country_Detector

Manages country detection using multiple methods with fallback support.

**Key Methods:**

- `detect_country($method)` - Detect customer country
- `get_country_from_ip($ip_address)` - IP-based detection
- `get_country_from_billing($user_id)` - Billing address detection
- `get_country_from_shipping($user_id)` - Shipping address detection

**Detection Priority:**

1. Shipping address (highest priority)
2. Billing address (medium priority)
3. IP address (lowest priority)

### PBC_Database

Handles all database operations including CRUD operations for pricing rules.

**Key Methods:**

- `get_pricing_rule($rule_id)` - Get single pricing rule
- `get_pricing_rules($rule_type, $object_id, $country_code)` - Get multiple rules
- `save_pricing_rule($rule_data)` - Save/update pricing rule
- `delete_pricing_rule($rule_id)` - Delete pricing rule

## Hooks and Filters

### WooCommerce Integration Hooks

The plugin integrates with WooCommerce using the following hooks:

#### Price Modification Hooks

```php
// Modify product prices
add_filter('woocommerce_product_get_price', array($this, 'modify_product_price'), 10, 2);
add_filter('woocommerce_product_get_regular_price', array($this, 'modify_regular_price'), 10, 2);
add_filter('woocommerce_product_get_sale_price', array($this, 'modify_sale_price'), 10, 2);

// Handle variable products
add_filter('woocommerce_variation_prices', array($this, 'modify_variation_prices'), 10, 3);

// Cart and checkout integration
add_action('woocommerce_before_calculate_totals', array($this, 'modify_cart_prices'), 10, 1);
add_action('woocommerce_checkout_update_order_review', array($this, 'handle_address_change'));
```

#### Admin Interface Hooks

```php
// Product edit page
add_action('woocommerce_product_options_pricing', array($this, 'add_product_pricing_fields'));
add_action('woocommerce_process_product_meta', array($this, 'save_product_pricing_fields'));

// Category edit page
add_action('product_cat_add_form_fields', array($this, 'add_category_pricing_fields'));
add_action('product_cat_edit_form_fields', array($this, 'edit_category_pricing_fields'));
add_action('edited_product_cat', array($this, 'save_category_pricing_fields'));
```

### Custom Plugin Hooks

The plugin provides several custom hooks for extensibility:

#### Action Hooks

```php
// Plugin lifecycle
do_action('pbc_loaded');                    // Plugin fully loaded
do_action('pbc_activated');                 // Plugin activated
do_action('pbc_deactivated');               // Plugin deactivated
do_action('pbc_updated', $old_version, $new_version); // Plugin updated

// Pricing events
do_action('pbc_pricing_rule_changed', $rule_type, $object_id, $country_code);
do_action('pbc_price_calculated', $product_id, $country_code, $result);
do_action('pbc_country_detected', $country_code, $method);

// Cache events
do_action('pbc_cache_cleared', $cache_type);
do_action('pbc_cache_warmed', $entries_count);
```

#### Filter Hooks

```php
// Price calculation filters
apply_filters('pbc_price_adjustment', $adjustment, $product_id, $country_code);
apply_filters('pbc_country_detection_result', $country_code, $method);
apply_filters('pbc_applicable_rule', $rule, $product_id, $country_code);

// Admin interface filters
apply_filters('pbc_admin_countries_list', $countries);
apply_filters('pbc_pricing_rule_validation', $validation_result, $rule_data);

// API response filters
apply_filters('pbc_api_product_response', $response_data, $product, $country);
apply_filters('pbc_api_pricing_rule_response', $rule_data, $rule);
```

### Usage Examples

#### Modifying Price Calculations

```php
// Add custom logic to price adjustments
add_filter('pbc_price_adjustment', function($adjustment, $product_id, $country_code) {
    // Apply additional business logic
    if ($country_code === 'US' && $product_id === 123) {
        $adjustment['adjusted_price'] *= 0.9; // Additional 10% discount
    }
    return $adjustment;
}, 10, 3);
```

#### Custom Country Detection

```php
// Add custom country detection method
add_filter('pbc_country_detection_result', function($country_code, $method) {
    if ($method === 'custom') {
        // Implement custom detection logic
        $country_code = my_custom_country_detection();
    }
    return $country_code;
}, 10, 2);
```

## Database Schema

### Pricing Rules Table

```sql
CREATE TABLE wp_pbc_pricing_rules (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    rule_type enum('global','category','product') NOT NULL,
    object_id bigint(20) DEFAULT NULL,
    country_code varchar(2) NOT NULL,
    adjustment_type enum('fixed','percentage') NOT NULL,
    adjustment_value decimal(10,4) NOT NULL,
    is_active tinyint(1) DEFAULT 1,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_rule_lookup (rule_type, object_id, country_code),
    KEY idx_country (country_code),
    KEY idx_active (is_active)
);
```

### Country Cache Table

```sql
CREATE TABLE wp_pbc_country_cache (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    session_id varchar(255) NOT NULL,
    ip_address varchar(45) NOT NULL,
    country_code varchar(2) NOT NULL,
    detection_method enum('ip','billing','shipping') NOT NULL,
    expires_at datetime NOT NULL,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY idx_session (session_id),
    KEY idx_ip (ip_address),
    KEY idx_expires (expires_at)
);
```

### Database Operations

#### Creating Pricing Rules

```php
$rule_data = array(
    'rule_type' => 'product',
    'object_id' => 123,
    'country_code' => 'CA',
    'adjustment_type' => 'percentage',
    'adjustment_value' => -15.0,
    'is_active' => true
);

$rule_id = $pbc->database->save_pricing_rule($rule_data);
```

#### Querying Rules

```php
// Get all rules for a product
$rules = $pbc->database->get_pricing_rules('product', 123);

// Get specific rule
$rule = $pbc->database->get_pricing_rule_by_criteria('product', 123, 'CA');
```

## API Integration

### REST API Extensions

The plugin extends WooCommerce's REST API to include country-based pricing data.

#### Product Endpoints

**Get product with country pricing:**

```
GET /wp-json/wc/v3/products/123?country=CA
```

**Response:**

```json
{
  "id": 123,
  "name": "Sample Product",
  "price": "85.00",
  "regular_price": "100.00",
  "country_pricing": {
    "country_code": "CA",
    "original_price": "100.00",
    "adjusted_price": "85.00",
    "adjustment": {
      "type": "percentage",
      "value": -15.0,
      "rule_source": "product",
      "rule_id": 456
    }
  }
}
```

#### Custom Pricing Rule Endpoints

**Get pricing rules:**

```
GET /wp-json/pbc/v1/pricing-rules
GET /wp-json/pbc/v1/pricing-rules?rule_type=product&object_id=123
```

**Create pricing rule:**

```
POST /wp-json/pbc/v1/pricing-rules
Content-Type: application/json

{
  "rule_type": "product",
  "object_id": 123,
  "country_code": "CA",
  "adjustment_type": "percentage",
  "adjustment_value": -15.0,
  "is_active": true
}
```

**Update pricing rule:**

```
PUT /wp-json/pbc/v1/pricing-rules/456
```

**Delete pricing rule:**

```
DELETE /wp-json/pbc/v1/pricing-rules/456
```

### Authentication

API endpoints require proper authentication:

- **Read operations**: No authentication required for public product data
- **Write operations**: Requires `manage_woocommerce` capability
- **Delete operations**: Requires `manage_options` capability

### Error Handling

API responses include comprehensive error information:

```json
{
  "code": "pbc_invalid_country_code",
  "message": "Country code must be exactly 2 characters",
  "data": {
    "status": 400
  }
}
```

## Caching System

### Cache Types

The plugin implements multiple cache layers:

1. **Price Cache**: Stores calculated prices for product/country combinations
2. **Rules Cache**: Stores applicable rules for product/country combinations
3. **Batch Cache**: Stores batch calculation results
4. **Country Detection Cache**: Stores country detection results per session

### Cache Keys

```php
// Price cache
$cache_key = 'pbc_price_' . $product_id . '_' . $country_code;

// Rules cache
$cache_key = 'pbc_rules_' . $product_id . '_' . $country_code;

// Batch cache
$cache_key = 'pbc_batch_' . md5(implode(',', $product_ids) . '_' . $country_code);
```

### Cache Management

```php
// Clear specific product cache
$pbc->pricing_engine->clear_price_cache($product_id, $country_code);

// Clear all cache
$pbc->pricing_engine->clear_all_price_cache();

// Warm cache for popular products
$pbc->pricing_engine->warm_cache($product_ids, $country_codes);
```

### Cache Invalidation

Cache is automatically invalidated when:

- Pricing rules are modified
- Products are updated
- User addresses change
- Plugin settings change

## Error Handling

### Error Categories

1. **Country Detection Errors**: IP detection failures, invalid country codes
2. **Pricing Calculation Errors**: Invalid rule data, database connection issues
3. **Admin Interface Errors**: Form validation errors, permission issues
4. **API Errors**: Authentication failures, invalid request data

### Error Handler Usage

```php
try {
    $price = $pbc->pricing_engine->get_price_adjustment($product_id, $country_code);
} catch (Exception $e) {
    $fallback_result = $pbc->error_handler->handle_pricing_error($e, [
        'product_id' => $product_id,
        'country_code' => $country_code,
        'function' => __FUNCTION__
    ]);
}
```

### Logging

The plugin includes comprehensive logging:

```php
// Log price calculations
$pbc->logger->log_price_calculation($product_id, $country_code, $result);

// Log country detection
$pbc->logger->log_country_detection($country_code, $method, $confidence);

// Log errors
$pbc->logger->log_error($error_message, $context);
```

## Extending the Plugin

### Creating Custom Detection Methods

```php
class My_Custom_Country_Detector {
    public function __construct() {
        add_filter('pbc_country_detection_result', array($this, 'custom_detection'), 10, 2);
    }

    public function custom_detection($country_code, $method) {
        if ($method === 'my_custom_method') {
            // Implement custom detection logic
            return $this->detect_from_custom_source();
        }
        return $country_code;
    }
}
```

### Adding Custom Pricing Rules

```php
class My_Custom_Pricing_Rules {
    public function __construct() {
        add_filter('pbc_applicable_rule', array($this, 'apply_custom_rules'), 10, 3);
    }

    public function apply_custom_rules($rule, $product_id, $country_code) {
        // Add custom business logic
        if ($this->should_apply_bulk_discount($product_id, $country_code)) {
            $rule->adjustment_value *= 1.1; // Increase discount
        }
        return $rule;
    }
}
```

### Custom Admin Interfaces

```php
class My_Custom_Admin_Panel {
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
    }

    public function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            'Custom Pricing Rules',
            'Custom Pricing',
            'manage_woocommerce',
            'custom-pricing',
            array($this, 'render_admin_page')
        );
    }
}
```

### Performance Considerations

When extending the plugin:

1. **Use caching**: Implement caching for expensive operations
2. **Batch operations**: Process multiple items together when possible
3. **Database optimization**: Use proper indexes and limit queries
4. **Memory management**: Clean up large objects and arrays
5. **Lazy loading**: Load data only when needed

### Testing Extensions

Always test custom extensions:

```php
class Test_My_Extension extends WP_UnitTestCase {
    public function test_custom_detection() {
        $detector = new My_Custom_Country_Detector();
        $result = apply_filters('pbc_country_detection_result', 'US', 'my_custom_method');
        $this->assertEquals('CA', $result);
    }
}
```
