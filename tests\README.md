# Price by Country - Testing Suite

This directory contains comprehensive unit and integration tests for the Price by Country WordPress plugin.

## Test Structure

```
tests/
├── unit/                    # Unit tests for individual classes
│   ├── test-pricing-engine.php
│   ├── test-country-detector.php
│   └── test-database.php
├── integration/             # Integration tests for WooCommerce compatibility
│   ├── test-woocommerce-hooks.php
│   ├── test-checkout-flow.php
│   └── test-rest-api.php
├── bootstrap.php           # Test bootstrap and setup
└── README.md              # This file
```

## Requirements

- PHP 7.4 or higher
- PHPUnit 8.0 or higher
- WordPress Test Suite
- MySQL/MariaDB for test database

## Setup

### 1. Install PHPUnit

```bash
# Via Composer (recommended)
composer require --dev phpunit/phpunit

# Or download PHAR
wget https://phar.phpunit.de/phpunit.phar
chmod +x phpunit.phar
sudo mv phpunit.phar /usr/local/bin/phpunit
```

### 2. Set up WordPress Test Environment

The test runner script will automatically set up the WordPress test environment, but you can also do it manually:

```bash
# Download the install script
curl -s https://raw.githubusercontent.com/wp-cli/scaffold-command/master/templates/install-wp-tests.sh > /tmp/install-wp-tests.sh
chmod +x /tmp/install-wp-tests.sh

# Run the install script
/tmp/install-wp-tests.sh pbc_test root '' localhost latest
```

### 3. Configure Database

Create a test database (the install script will handle this, but you can create it manually):

```sql
CREATE DATABASE pbc_test;
GRANT ALL PRIVILEGES ON pbc_test.* TO 'root'@'localhost';
```

## Running Tests

### Using the Test Runner (Recommended)

```bash
# Run all tests
./bin/run-tests.sh

# Run only unit tests
./bin/run-tests.sh --unit

# Run only integration tests
./bin/run-tests.sh --integration

# Generate code coverage report
./bin/run-tests.sh --coverage

# Verbose output
./bin/run-tests.sh --verbose
```

### Windows Users

```cmd
# Run all tests
bin\run-tests.bat

# Run with options
bin\run-tests.bat --unit --verbose
```

### Direct PHPUnit Commands

```bash
# Run all tests
phpunit

# Run specific test suite
phpunit --testsuite=unit
phpunit --testsuite=integration

# Run specific test file
phpunit tests/unit/test-pricing-engine.php

# Generate coverage report
phpunit --coverage-html tests/coverage/html
```

## Environment Variables

You can customize the test environment using these variables:

- `WP_VERSION`: WordPress version to test against (default: latest)
- `WP_MULTISITE`: Enable multisite testing (default: 0)
- `DB_NAME`: Test database name (default: pbc_test)
- `DB_USER`: Database user (default: root)
- `DB_PASS`: Database password (default: empty)
- `DB_HOST`: Database host (default: localhost)

Example:

```bash
WP_VERSION=5.8 DB_NAME=my_test_db ./bin/run-tests.sh
```

## Test Categories

### Unit Tests

Unit tests focus on testing individual classes and methods in isolation:

- **PBC_Pricing_Engine**: Tests pricing calculations, rule hierarchy, caching
- **PBC_Country_Detector**: Tests country detection methods, validation, caching
- **PBC_Database**: Tests CRUD operations, data sanitization, edge cases

### Integration Tests

Integration tests verify that components work together correctly with WooCommerce:

- **WooCommerce Hooks**: Tests price modification hooks, cart integration
- **Checkout Flow**: Tests complete checkout process with address changes
- **REST API**: Tests API endpoints, authentication, error handling

## Test Data

Tests use the `PBC_Test_Helper` class to create consistent test data:

```php
// Create test product
$product_id = PBC_Test_Helper::create_test_product('100.00');

// Create test pricing rule
$rule_data = PBC_Test_Helper::create_test_pricing_rule([
    'country_code' => 'CA',
    'adjustment_value' => -10.00
]);
```

## Mocking

Tests include comprehensive mocking for:

- WooCommerce classes (WC_Product, WC_Cart, WC_Order)
- WordPress functions (wp_get_post_terms, get_user_meta)
- External services (IP geolocation)

## Coverage Reports

When running with `--coverage`, reports are generated in:

- HTML: `tests/coverage/html/index.html`
- Clover XML: `tests/coverage/clover.xml`
- JUnit XML: `tests/coverage/junit.xml`

## Continuous Integration

The test suite is designed to work with CI/CD systems. Example GitHub Actions workflow:

```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        php: [7.4, 8.0, 8.1]
        wordpress: [5.8, latest]
    steps:
      - uses: actions/checkout@v2
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
      - name: Install dependencies
        run: composer install
      - name: Run tests
        run: ./bin/run-tests.sh --coverage
        env:
          WP_VERSION: ${{ matrix.wordpress }}
```

## Writing New Tests

### Unit Test Example

```php
class Test_My_Class extends WP_UnitTestCase {

    private $instance;

    public function setUp(): void {
        parent::setUp();
        $this->instance = new My_Class();
    }

    public function test_my_method() {
        $result = $this->instance->my_method('input');
        $this->assertEquals('expected', $result);
    }

    public function tearDown(): void {
        // Clean up
        parent::tearDown();
    }
}
```

### Integration Test Example

```php
class Test_My_Integration extends WP_UnitTestCase {

    public function test_woocommerce_integration() {
        // Set up WooCommerce environment
        $product_id = PBC_Test_Helper::create_test_product();

        // Test integration
        $result = apply_filters('woocommerce_product_get_price', 100.00, wc_get_product($product_id));

        $this->assertEquals(90.00, $result);
    }
}
```

## Troubleshooting

### Common Issues

1. **Database connection errors**: Ensure test database exists and credentials are correct
2. **WordPress not found**: Check WP_TESTS_DIR environment variable
3. **Memory limit**: Increase PHP memory limit for large test suites
4. **Timeout issues**: Some integration tests may take longer to run

### Debug Mode

Enable debug mode by setting:

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

### Verbose Output

Use `--verbose` flag to see detailed test execution information.

## Contributing

When adding new features:

1. Write unit tests for new classes/methods
2. Add integration tests for WooCommerce interactions
3. Ensure all tests pass before submitting PR
4. Maintain test coverage above 80%

## Performance

The test suite is optimized for speed:

- Database transactions are used to isolate tests
- Mocking reduces external dependencies
- Parallel test execution is supported
- Caching is tested but not relied upon during tests
