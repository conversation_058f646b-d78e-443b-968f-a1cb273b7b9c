# Price by Country for WooCommerce - Installation Guide

## Table of Contents

1. [System Requirements](#system-requirements)
2. [Pre-Installation Checklist](#pre-installation-checklist)
3. [Installation Methods](#installation-methods)
4. [Initial Setup Wizard](#initial-setup-wizard)
5. [Configuration Options](#configuration-options)
6. [Verification and Testing](#verification-and-testing)
7. [Upgrade and Migration](#upgrade-and-migration)
8. [Troubleshooting Installation](#troubleshooting-installation)
9. [Environment-Specific Instructions](#environment-specific-instructions)

## System Requirements

### Minimum Requirements

- **WordPress**: 5.0 or higher
- **WooCommerce**: 5.0 or higher
- **PHP**: 7.4 or higher
- **MySQL**: 5.6 or higher (or MariaDB 10.1+)
- **Memory**: 128MB minimum (256MB recommended)
- **Disk Space**: 10MB free space

### Recommended Requirements

- **WordPress**: 6.0 or higher
- **WooCommerce**: 7.1 or higher (for full HPOS support)
- **PHP**: 8.0 or higher
- **MySQL**: 8.0 or higher
- **Memory**: 512MB or higher
- **Disk Space**: 50MB free space

### Server Requirements

- **Web Server**: Apache 2.4+ or Nginx 1.18+
- **SSL Certificate**: Required for secure checkout and geolocation
- **Cron Jobs**: WordPress cron or server cron for scheduled tasks
- **File Permissions**: Write access to wp-content directory
- **URL Rewrite**: mod_rewrite (Apache) or equivalent (Nginx)

### PHP Extensions

**Required PHP extensions:**

- `mysqli` or `pdo_mysql` - Database connectivity
- `curl` - API communications and geolocation services
- `json` - Data processing and API responses
- `mbstring` - String handling for international characters
- `openssl` - Secure communications

**Optional but recommended:**

- `zip` - For import/export functionality
- `gd` or `imagick` - Image processing (if using custom admin interfaces)
- `intl` - Enhanced internationalization support

### WordPress Features

- **Multisite**: Supported (network activation available)
- **HPOS**: Full compatibility with WooCommerce High-Performance Order Storage
- **REST API**: Required for external integrations
- **Caching**: Compatible with most caching plugins

## Pre-Installation Checklist

Before installing the plugin, ensure you have:

### ✅ Environment Verification

1. **Backup your site**: Create a full backup of your WordPress site and database
2. **Test environment**: Consider installing on a staging site first
3. **WooCommerce setup**: Ensure WooCommerce is properly configured and working
4. **Admin access**: Verify you have administrator privileges
5. **Plugin conflicts**: Temporarily deactivate other pricing plugins to avoid conflicts

### ✅ System Check

Run the compatibility checker by uploading the plugin files and visiting:

```
/wp-admin/admin.php?page=pbc-compatibility-check
```

### ✅ Required Information

Gather the following information before installation:

- List of countries you want to support
- Your preferred country detection method
- Base currency and pricing strategy
- Any existing pricing rules or data to import

## Installation Methods

### Method 1: WordPress Admin Dashboard (Recommended)

1. **Download the plugin**:

   - Download the latest version from your purchase confirmation email
   - Save the `price-by-country-woocommerce.zip` file to your computer

2. **Upload via WordPress Admin**:

   ```
   WordPress Admin → Plugins → Add New → Upload Plugin
   ```

   - Click "Choose File" and select the downloaded ZIP file
   - Click "Install Now"
   - Wait for the installation to complete

3. **Activate the plugin**:
   - Click "Activate Plugin" after installation
   - Or go to `Plugins → Installed Plugins` and activate "Price by Country for WooCommerce"

### Method 2: FTP/SFTP Upload

1. **Extract the plugin**:

   - Unzip the `price-by-country-woocommerce.zip` file
   - You should see a folder named `price-by-country-woocommerce`

2. **Upload via FTP**:

   ```bash
   # Connect to your server via FTP/SFTP
   # Navigate to your WordPress installation
   cd /path/to/wordpress/wp-content/plugins/

   # Upload the plugin folder
   # Ensure the folder structure is:
   # wp-content/plugins/price-by-country-woocommerce/
   ```

3. **Set permissions**:

   ```bash
   # Set appropriate file permissions
   find price-by-country-woocommerce/ -type f -exec chmod 644 {} \;
   find price-by-country-woocommerce/ -type d -exec chmod 755 {} \;
   ```

4. **Activate via WordPress Admin**:
   - Go to `Plugins → Installed Plugins`
   - Find "Price by Country for WooCommerce"
   - Click "Activate"

### Method 3: WP-CLI Installation

For developers and advanced users:

```bash
# Navigate to WordPress root directory
cd /path/to/wordpress/

# Upload and extract plugin
wp plugin install /path/to/price-by-country-woocommerce.zip

# Activate the plugin
wp plugin activate price-by-country-woocommerce

# Verify installation
wp plugin list --status=active | grep price-by-country
```

### Method 4: Composer Installation (Development)

For development environments using Composer:

```bash
# Add to composer.json (if available as a package)
composer require orbitaddons/price-by-country-woocommerce

# Or install from local path
composer config repositories.pbc path /path/to/plugin
composer require orbitaddons/price-by-country-woocommerce:dev-main
```

## Initial Setup Wizard

After activation, the setup wizard will automatically launch. If it doesn't appear, you can access it manually:

### Accessing the Setup Wizard

- **Automatic**: Redirected after plugin activation
- **Manual**: Go to `Dashboard → Price by Country Setup`
- **Direct URL**: `/wp-admin/index.php?page=pbc-setup`

### Setup Wizard Steps

#### Step 1: Welcome

- Introduction to the plugin features
- Links to documentation and support
- Option to skip setup (not recommended)

#### Step 2: Country Detection Configuration

**Detection Method Options:**

- **Automatic (Recommended)**: Uses shipping → billing → IP priority
- **IP Address Only**: Uses visitor's IP address via WooCommerce geolocation
- **Billing Address Only**: Uses customer's billing country
- **Shipping Address Only**: Uses customer's shipping country

**Configuration Settings:**

```php
// Example configuration
$detection_settings = array(
    'method' => 'auto',           // auto, ip, billing, shipping
    'priority' => array(          // Priority order for auto mode
        'shipping',
        'billing',
        'ip'
    ),
    'default_country' => 'US',    // Fallback country
    'cache_duration' => 3600,     // Cache duration in seconds
    'enable_debug' => false       // Debug logging
);
```

#### Step 3: Sample Rules Creation

**Sample Rule Options:**

- **Create samples**: Generates example pricing rules for common countries
- **Rule type**: Choose between percentage or fixed amount adjustments
- **Countries included**: US (base), UK (+15%), EU (+10%), Canada (-5%), Australia (+20%)

#### Step 4: Completion

- Summary of configured settings
- Links to next steps and documentation
- Dashboard and settings access

### Manual Setup (Skip Wizard)

If you prefer to configure manually:

1. **Go to Settings**:

   ```
   WooCommerce → Settings → Price by Country
   ```

2. **Configure Basic Settings**:

   - Country detection method
   - Default country
   - Cache settings
   - Debug options

3. **Create Initial Rules**:
   - Navigate to the pricing dashboard
   - Add global, category, or product-specific rules
   - Test with different countries

## Configuration Options

### Global Settings

Access via `WooCommerce → Settings → Price by Country`:

#### Country Detection Settings

| Setting            | Description                         | Default               | Options                     |
| ------------------ | ----------------------------------- | --------------------- | --------------------------- |
| Detection Method   | How to detect customer country      | Auto                  | Auto, IP, Billing, Shipping |
| Detection Priority | Order of detection methods          | Shipping, Billing, IP | Customizable array          |
| Default Country    | Fallback when detection fails       | US                    | Any valid country code      |
| Cache Duration     | How long to cache detection results | 1 hour                | 30min, 1hr, 2hr, 24hr       |
| Enable Geolocation | Use WooCommerce geolocation service | Yes                   | Yes/No                      |

#### Performance Settings

| Setting        | Description                | Default    | Options                  |
| -------------- | -------------------------- | ---------- | ------------------------ |
| Enable Caching | Cache pricing calculations | Yes        | Yes/No                   |
| Cache Method   | Caching mechanism          | Transients | Transients, Object Cache |
| Cache Duration | Pricing cache lifetime     | 1 hour     | 15min, 30min, 1hr, 6hr   |
| Preload Rules  | Load rules on page load    | No         | Yes/No                   |

#### Debug Settings

| Setting              | Description           | Default | Options                     |
| -------------------- | --------------------- | ------- | --------------------------- |
| Enable Debug Logging | Log plugin activities | No      | Yes/No                      |
| Log Level            | Detail level of logs  | Error   | Error, Warning, Info, Debug |
| Log Retention        | How long to keep logs | 7 days  | 1, 7, 14, 30 days           |

### Product-Level Configuration

For each product, you can configure:

#### Individual Product Settings

1. **Edit Product**:

   ```
   Products → All Products → [Select Product] → Edit
   ```

2. **Price by Country Section**:
   - Located in the "General" or "Advanced" tab
   - Country-specific pricing rules
   - Inheritance settings

#### Configuration Options:

```php
// Product-level pricing structure
$product_pricing = array(
    'inherit_from' => 'none',        // none, category, global
    'rules' => array(
        'US' => array(
            'type' => 'base',        // base, fixed, percentage
            'value' => 0             // Adjustment value
        ),
        'GB' => array(
            'type' => 'percentage',
            'value' => 15            // 15% increase
        ),
        'CA' => array(
            'type' => 'fixed',
            'value' => -5            // $5 decrease
        )
    ),
    'active' => true                 // Enable/disable for this product
);
```

### Category-Level Configuration

Configure pricing for entire product categories:

#### Category Settings

1. **Edit Category**:

   ```
   Products → Categories → [Select Category] → Edit
   ```

2. **Price by Country Section**:
   - Bulk pricing rules for all products in category
   - Inheritance options
   - Override permissions

### Global Pricing Rules

Set store-wide pricing defaults:

#### Global Configuration

1. **Access Global Settings**:

   ```
   WooCommerce → Price by Country → Global Rules
   ```

2. **Rule Configuration**:
   - Default pricing for all countries
   - Fallback rules when specific rules don't exist
   - Bulk operations for multiple countries

## Verification and Testing

### Post-Installation Verification

#### 1. Plugin Status Check

```php
// Check if plugin is active and functioning
if (class_exists('PBC_Core')) {
    echo "Plugin is active and loaded correctly";

    // Check core components
    $core = PBC_Core::get_instance();
    if ($core->pricing_engine && $core->country_detector) {
        echo "All core components loaded successfully";
    }
}
```

#### 2. Database Tables Verification

Check that required tables were created:

```sql
-- Check for pricing rules table
SHOW TABLES LIKE 'wp_pbc_pricing_rules';

-- Check for country cache table
SHOW TABLES LIKE 'wp_pbc_country_cache';

-- Verify table structure
DESCRIBE wp_pbc_pricing_rules;
```

#### 3. WooCommerce Integration Test

1. **Frontend Price Display**:

   - Visit a product page
   - Check if prices display correctly
   - Test with different countries (use VPN or browser tools)

2. **Admin Interface**:

   - Edit a product
   - Verify "Price by Country" section appears
   - Test saving pricing rules

3. **Checkout Process**:
   - Add products to cart
   - Change billing/shipping address
   - Verify prices update automatically

### Testing Different Scenarios

#### Country Detection Testing

```javascript
// Browser console testing
// Simulate different countries
localStorage.setItem("pbc_test_country", "GB");
location.reload();

// Check detected country
console.log("Detected country:", window.pbc_detected_country);
```

#### Pricing Calculation Testing

Create test scenarios for:

1. **Product without specific rules** (should use global/default)
2. **Product with specific country rules** (should use product rules)
3. **Category inheritance** (should use category rules when product inherits)
4. **Multiple rule conflicts** (should follow priority: product > category > global)

#### Performance Testing

Monitor performance impact:

```php
// Add to functions.php for testing
add_action('wp_footer', function() {
    if (current_user_can('administrator')) {
        global $wpdb;
        echo "<!-- PBC Queries: " . $wpdb->num_queries . " -->";
        echo "<!-- Memory Usage: " . memory_get_peak_usage(true) / 1024 / 1024 . "MB -->";
    }
});
```

## Upgrade and Migration

### Upgrading from Previous Versions

#### Automatic Updates

1. **Backup First**: Always backup before upgrading
2. **Check Compatibility**: Review changelog for breaking changes
3. **Update Process**:
   ```
   Plugins → Installed Plugins → Price by Country → Update
   ```

#### Manual Update Process

1. **Deactivate Current Version**:

   ```
   Plugins → Installed Plugins → Price by Country → Deactivate
   ```

2. **Backup Settings**:

   ```php
   // Export current settings
   $settings = get_option('pbc_settings');
   $rules = get_option('pbc_pricing_rules');

   // Save to file or database backup
   ```

3. **Remove Old Files**:

   ```bash
   # Remove old plugin files (keep backups)
   rm -rf wp-content/plugins/price-by-country-woocommerce/
   ```

4. **Install New Version**: Follow installation methods above

5. **Verify Migration**: Check that settings and rules are preserved

### Database Migration

The plugin handles database migrations automatically, but you can monitor the process:

#### Migration Process

```php
// Check current database version
$db_version = get_option('pbc_db_version', '1.0.0');

// Check if migration is needed
if (version_compare($db_version, PBC_DB_VERSION, '<')) {
    // Migration will run automatically
    // Check logs for migration status
}
```

#### Manual Migration (if needed)

```sql
-- Example migration from v1.0 to v2.0
-- Add new columns to existing tables
ALTER TABLE wp_pbc_pricing_rules
ADD COLUMN rule_priority INT DEFAULT 0 AFTER adjustment_value;

-- Update existing data
UPDATE wp_pbc_pricing_rules
SET rule_priority = 1
WHERE rule_type = 'product';
```

### Migrating from Other Pricing Plugins

#### Data Import Process

1. **Export from Previous Plugin**:

   - Use the previous plugin's export feature
   - Or extract data directly from database

2. **Format Conversion**:

   ```php
   // Example conversion script
   function convert_pricing_data($old_data) {
       $new_format = array();

       foreach ($old_data as $rule) {
           $new_format[] = array(
               'rule_type' => 'product',
               'object_id' => $rule['product_id'],
               'country_code' => $rule['country'],
               'adjustment_type' => $rule['type'],
               'adjustment_value' => $rule['value'],
               'is_active' => 1
           );
       }

       return $new_format;
   }
   ```

3. **Import via Admin Interface**:
   ```
   WooCommerce → Price by Country → Import/Export → Import Rules
   ```

#### Common Migration Scenarios

**From WooCommerce Currency Switcher:**

- Export currency-based rules
- Convert to country-based rules
- Map currencies to countries

**From Country-Based Pricing plugins:**

- Direct data migration possible
- May need rule format conversion
- Test thoroughly after migration

### Rollback Procedures

If you need to rollback to a previous version:

#### Quick Rollback

1. **Deactivate Current Version**
2. **Restore from Backup**:

   ```bash
   # Restore plugin files
   cp -r /backup/price-by-country-woocommerce/ wp-content/plugins/

   # Restore database (if needed)
   mysql -u username -p database_name < backup.sql
   ```

3. **Reactivate Plugin**
4. **Verify Functionality**

#### Database Rollback

```sql
-- Rollback database changes (example)
-- Remove new columns added in recent version
ALTER TABLE wp_pbc_pricing_rules DROP COLUMN rule_priority;

-- Restore previous option values
UPDATE wp_options
SET option_value = 'old_value'
WHERE option_name = 'pbc_settings';
```

## Troubleshooting Installation

### Common Installation Issues

#### Issue 1: Plugin Activation Fails

**Symptoms:**

- "Plugin could not be activated" error
- White screen after activation
- Fatal PHP errors

**Solutions:**

1. **Check PHP Version**:

   ```php
   <?php
   echo "PHP Version: " . PHP_VERSION;
   if (version_compare(PHP_VERSION, '7.4', '<')) {
       echo "ERROR: PHP 7.4+ required";
   }
   ?>
   ```

2. **Check Memory Limit**:

   ```php
   // Add to wp-config.php
   ini_set('memory_limit', '256M');
   define('WP_MEMORY_LIMIT', '256M');
   ```

3. **Enable Debug Mode**:

   ```php
   // Add to wp-config.php
   define('WP_DEBUG', true);
   define('WP_DEBUG_LOG', true);
   define('WP_DEBUG_DISPLAY', false);
   ```

4. **Check Error Logs**:

   ```bash
   # Check WordPress error log
   tail -f wp-content/debug.log

   # Check server error log
   tail -f /var/log/apache2/error.log
   ```

#### Issue 2: Database Tables Not Created

**Symptoms:**

- Plugin activates but doesn't function
- "Table doesn't exist" errors
- Missing admin interfaces

**Solutions:**

1. **Manual Table Creation**:

   ```sql
   -- Run the table creation SQL manually
   CREATE TABLE wp_pbc_pricing_rules (
       id bigint(20) NOT NULL AUTO_INCREMENT,
       rule_type enum('global','category','product') NOT NULL,
       object_id bigint(20) DEFAULT NULL,
       country_code varchar(2) NOT NULL,
       adjustment_type enum('fixed','percentage') NOT NULL,
       adjustment_value decimal(10,4) NOT NULL,
       is_active tinyint(1) DEFAULT 1,
       created_at datetime DEFAULT CURRENT_TIMESTAMP,
       updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
       PRIMARY KEY (id),
       KEY idx_rule_lookup (rule_type, object_id, country_code),
       KEY idx_country (country_code),
       KEY idx_active (is_active)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
   ```

2. **Check Database Permissions**:

   ```sql
   -- Verify user has CREATE privileges
   SHOW GRANTS FOR 'wp_user'@'localhost';
   ```

3. **Force Database Update**:
   ```php
   // Add to functions.php temporarily
   add_action('init', function() {
       if (current_user_can('administrator')) {
           $core = PBC_Core::get_instance();
           $core->database->create_tables();
       }
   });
   ```

#### Issue 3: WooCommerce Integration Problems

**Symptoms:**

- Prices don't change based on country
- Admin sections missing
- Checkout errors

**Solutions:**

1. **Verify WooCommerce Version**:

   ```php
   if (defined('WC_VERSION')) {
       echo "WooCommerce Version: " . WC_VERSION;
       if (version_compare(WC_VERSION, '5.0', '<')) {
           echo "ERROR: WooCommerce 5.0+ required";
       }
   } else {
       echo "ERROR: WooCommerce not detected";
   }
   ```

2. **Check Hook Integration**:

   ```php
   // Test if hooks are working
   add_action('woocommerce_product_get_price', function($price, $product) {
       error_log("Price hook called for product: " . $product->get_id());
       return $price;
   }, 10, 2);
   ```

3. **Clear All Caches**:
   - WooCommerce transients
   - WordPress object cache
   - Plugin caches
   - Server-level caches

### Performance Issues

#### Issue: Slow Page Loading

**Diagnosis:**

```php
// Add performance monitoring
add_action('wp_footer', function() {
    if (current_user_can('administrator')) {
        $queries = get_num_queries();
        $memory = memory_get_peak_usage(true) / 1024 / 1024;
        echo "<!-- Queries: {$queries}, Memory: {$memory}MB -->";
    }
});
```

**Solutions:**

1. Enable caching in plugin settings
2. Optimize database queries
3. Use object caching (Redis/Memcached)
4. Implement lazy loading

#### Issue: High Database Load

**Solutions:**

1. **Add Database Indexes**:

   ```sql
   -- Add missing indexes
   ALTER TABLE wp_pbc_pricing_rules
   ADD INDEX idx_country_active (country_code, is_active);
   ```

2. **Optimize Queries**:
   ```php
   // Use prepared statements and proper caching
   $rules = wp_cache_get("pbc_rules_{$country}", 'pbc_pricing');
   if (false === $rules) {
       // Query database
       wp_cache_set("pbc_rules_{$country}", $rules, 'pbc_pricing', 3600);
   }
   ```

### Getting Help

#### Support Channels

1. **Documentation**: Check the complete documentation first
2. **Support Forum**: Visit the plugin support forum
3. **Direct Support**: Contact support with your license key
4. **Community**: Join the user community for tips and tricks

#### Information to Provide

When seeking support, include:

- WordPress version
- WooCommerce version
- PHP version
- Plugin version
- Error messages (full text)
- Steps to reproduce the issue
- Screenshots (if applicable)

#### Debug Information

Generate debug information:

```php
// Add to functions.php temporarily
add_action('wp_footer', function() {
    if (current_user_can('administrator') && isset($_GET['pbc_debug'])) {
        $debug_info = array(
            'wp_version' => get_bloginfo('version'),
            'wc_version' => defined('WC_VERSION') ? WC_VERSION : 'Not installed',
            'php_version' => PHP_VERSION,
            'plugin_version' => defined('PBC_VERSION') ? PBC_VERSION : 'Unknown',
            'active_plugins' => get_option('active_plugins'),
            'theme' => get_template()
        );

        echo '<pre style="display:none;" id="pbc-debug">';
        echo json_encode($debug_info, JSON_PRETTY_PRINT);
        echo '</pre>';
    }
});
```

## Environment-Specific Instructions

### Development Environment

#### Local Development Setup

1. **Use Local Development Tools**:

   - Local by Flywheel
   - XAMPP/WAMP/MAMP
   - Docker with WordPress

2. **Enable Debug Mode**:

   ```php
   // wp-config.php
   define('WP_DEBUG', true);
   define('WP_DEBUG_LOG', true);
   define('WP_DEBUG_DISPLAY', true);
   define('SCRIPT_DEBUG', true);
   ```

3. **Development-Specific Settings**:
   ```php
   // Enable all logging
   $pbc_settings = array(
       'debug_mode' => true,
       'log_level' => 'debug',
       'cache_duration' => 60, // Short cache for testing
       'enable_test_mode' => true
   );
   update_option('pbc_settings', $pbc_settings);
   ```

### Staging Environment

#### Staging Setup Best Practices

1. **Mirror Production**:

   - Same PHP/WordPress/WooCommerce versions
   - Similar server configuration
   - Copy of production database

2. **Testing Configuration**:

   ```php
   // Staging-specific settings
   define('PBC_STAGING_MODE', true);
   define('PBC_TEST_COUNTRIES', 'US,GB,CA,AU,DE');
   ```

3. **Validation Steps**:
   - Test all pricing scenarios
   - Verify country detection
   - Check admin interfaces
   - Test import/export functionality

### Production Environment

#### Production Deployment

1. **Pre-Deployment Checklist**:

   - [ ] Full site backup completed
   - [ ] Staging tests passed
   - [ ] Performance benchmarks acceptable
   - [ ] Security scan completed
   - [ ] Documentation updated

2. **Deployment Process**:

   ```bash
   # 1. Enable maintenance mode
   wp maintenance-mode activate

   # 2. Backup current installation
   wp db export backup-$(date +%Y%m%d).sql

   # 3. Install/update plugin
   wp plugin install price-by-country-woocommerce.zip --force

   # 4. Activate plugin
   wp plugin activate price-by-country-woocommerce

   # 5. Run any necessary migrations
   wp pbc migrate --dry-run
   wp pbc migrate

   # 6. Clear all caches
   wp cache flush

   # 7. Disable maintenance mode
   wp maintenance-mode deactivate
   ```

3. **Post-Deployment Verification**:
   - Check site functionality
   - Verify pricing calculations
   - Monitor error logs
   - Test checkout process

#### Production Optimization

1. **Caching Configuration**:

   ```php
   // Optimize for production
   $production_settings = array(
       'enable_caching' => true,
       'cache_method' => 'object_cache', // If available
       'cache_duration' => 3600,
       'preload_rules' => true,
       'debug_mode' => false,
       'log_level' => 'error'
   );
   ```

2. **Database Optimization**:

   ```sql
   -- Optimize tables
   OPTIMIZE TABLE wp_pbc_pricing_rules;
   OPTIMIZE TABLE wp_pbc_country_cache;

   -- Analyze query performance
   EXPLAIN SELECT * FROM wp_pbc_pricing_rules
   WHERE rule_type = 'product' AND country_code = 'US';
   ```

### Multisite Environment

#### Network Installation

1. **Network Activation**:

   ```
   Network Admin → Plugins → Price by Country → Network Activate
   ```

2. **Site-Specific Configuration**:

   - Each site can have independent settings
   - Global network settings available
   - Centralized rule management option

3. **Network Settings**:
   ```php
   // Network-wide settings
   $network_settings = array(
       'allow_site_override' => true,
       'shared_rules' => false,
       'centralized_logging' => true
   );
   update_site_option('pbc_network_settings', $network_settings);
   ```

This completes the comprehensive installation and configuration guide. The guide covers all aspects from system requirements through production deployment, with specific instructions for different environments and comprehensive troubleshooting information.

### H

igh-Traffic Environment

#### Optimization for High Traffic

1. **Server-Level Caching**:

   ```nginx
   # Nginx configuration example
   location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
       expires 1y;
       add_header Cache-Control "public, immutable";
   }

   # Cache API responses
   location /wp-json/wc/v3/pbc/ {
       proxy_cache_valid 200 5m;
       proxy_cache_key "$scheme$request_method$host$request_uri";
   }
   ```

2. **Database Connection Pooling**:

   ```php
   // Use persistent connections
   define('DB_PERSISTENT_CONNECTION', true);

   // Connection pooling for high traffic
   $db_config = array(
       'host' => 'localhost',
       'user' => 'wp_user',
       'password' => 'password',
       'database' => 'wordpress',
       'pool_size' => 20,
       'max_connections' => 100
   );
   ```

3. **Load Balancing Considerations**:
   - Session affinity for country detection
   - Shared cache layer (Redis/Memcached)
   - Database read replicas for pricing queries

#### Performance Monitoring

```php
// Add performance monitoring
add_action('wp_footer', function() {
    if (defined('PBC_PERFORMANCE_MONITORING') && PBC_PERFORMANCE_MONITORING) {
        $metrics = array(
            'queries' => get_num_queries(),
            'memory' => memory_get_peak_usage(true),
            'time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'],
            'cache_hits' => wp_cache_get_stats()
        );

        // Log to monitoring service
        error_log('PBC Performance: ' . json_encode($metrics));
    }
});
```

## Configuration Screenshots and Examples

### Admin Interface Screenshots

_Note: In a real implementation, you would include actual screenshots here. For this documentation, I'm providing descriptions of what the screenshots would show._

#### Screenshot 1: Plugin Settings Page

**Location**: `WooCommerce → Settings → Price by Country`
**Shows**:

- Country detection method dropdown
- Default country selection
- Cache duration settings
- Debug options toggle

#### Screenshot 2: Product Edit Page

**Location**: `Products → Edit Product → Price by Country Tab`
**Shows**:

- Country selection dropdown
- Price adjustment fields (fixed/percentage)
- Inheritance options
- Preview of calculated prices

#### Screenshot 3: Global Rules Dashboard

**Location**: `WooCommerce → Price by Country → Global Rules`
**Shows**:

- Table of all global pricing rules
- Add new rule form
- Bulk actions dropdown
- Search and filter options

#### Screenshot 4: Setup Wizard

**Location**: Appears after plugin activation
**Shows**:

- Welcome screen with setup steps
- Country detection configuration
- Sample rules creation options
- Completion summary

### Configuration Examples

#### Example 1: Basic Global Pricing Setup

```php
// Configuration for a US-based store with international shipping
$global_rules = array(
    array(
        'country_code' => 'US',
        'adjustment_type' => 'base',
        'adjustment_value' => 0,
        'description' => 'Base pricing for US customers'
    ),
    array(
        'country_code' => 'CA',
        'adjustment_type' => 'percentage',
        'adjustment_value' => -5,
        'description' => '5% discount for Canadian customers'
    ),
    array(
        'country_code' => 'GB',
        'adjustment_type' => 'percentage',
        'adjustment_value' => 15,
        'description' => '15% increase for UK customers (VAT included)'
    ),
    array(
        'country_code' => 'AU',
        'adjustment_type' => 'fixed',
        'adjustment_value' => 10,
        'description' => '$10 increase for Australian customers'
    )
);
```

#### Example 2: Category-Specific Pricing

```php
// Electronics category with different international pricing
$electronics_rules = array(
    'category_id' => 15, // Electronics category ID
    'rules' => array(
        'US' => array('type' => 'base', 'value' => 0),
        'EU' => array('type' => 'percentage', 'value' => 20), // EU VAT
        'JP' => array('type' => 'percentage', 'value' => 25), // Import duties
        'BR' => array('type' => 'percentage', 'value' => 30)  // High import taxes
    ),
    'inheritance' => 'allow_product_override'
);
```

#### Example 3: Product-Level Overrides

```php
// Premium product with custom pricing strategy
$premium_product_rules = array(
    'product_id' => 123,
    'inherit_from' => 'none', // Don't inherit from category/global
    'rules' => array(
        'US' => array('type' => 'base', 'value' => 0),
        'GB' => array('type' => 'fixed', 'value' => 50),    // Fixed premium
        'DE' => array('type' => 'fixed', 'value' => 45),    // Slightly lower
        'FR' => array('type' => 'fixed', 'value' => 48),    // Market-specific
        'default' => array('type' => 'percentage', 'value' => 20) // All others
    )
);
```

## Advanced Configuration

### Custom Country Detection

For advanced users who need custom country detection logic:

```php
// Add custom country detection method
add_filter('pbc_country_detection_methods', function($methods) {
    $methods['custom'] = array(
        'name' => 'Custom Detection',
        'callback' => 'my_custom_country_detection',
        'priority' => 1
    );
    return $methods;
});

function my_custom_country_detection() {
    // Custom logic here
    // Example: Use CloudFlare country header
    if (isset($_SERVER['HTTP_CF_IPCOUNTRY'])) {
        return $_SERVER['HTTP_CF_IPCOUNTRY'];
    }

    // Example: Use custom API
    $ip = $_SERVER['REMOTE_ADDR'];
    $response = wp_remote_get("https://api.example.com/country/{$ip}");
    if (!is_wp_error($response)) {
        $data = json_decode(wp_remote_retrieve_body($response), true);
        return $data['country_code'] ?? null;
    }

    return null; // Fallback to next method
}
```

### Custom Pricing Logic

```php
// Add custom pricing calculation
add_filter('pbc_calculate_price', function($price, $product, $country, $rule) {
    // Custom pricing logic
    if ($country === 'VIP_COUNTRY') {
        // Special VIP pricing
        return $price * 0.8; // 20% discount
    }

    // Bulk pricing based on quantity in cart
    $cart_quantity = WC()->cart->get_cart_item_quantities()[$product->get_id()] ?? 1;
    if ($cart_quantity >= 10) {
        return $price * 0.9; // 10% bulk discount
    }

    return $price; // Use default calculation
}, 10, 4);
```

### Integration with Third-Party Services

#### Currency Conversion Integration

```php
// Integrate with currency conversion service
add_filter('pbc_price_adjustment', function($adjustment, $country, $base_price) {
    // Get country's primary currency
    $country_currency = get_country_currency($country);
    $base_currency = get_woocommerce_currency();

    if ($country_currency !== $base_currency) {
        // Convert using live exchange rates
        $exchange_rate = get_exchange_rate($base_currency, $country_currency);
        $adjustment = $adjustment * $exchange_rate;
    }

    return $adjustment;
}, 10, 3);
```

#### Tax Integration

```php
// Integrate with tax calculation services
add_filter('pbc_final_price', function($price, $product, $country) {
    // Calculate country-specific taxes
    $tax_rate = get_country_tax_rate($country);
    $tax_inclusive_price = $price * (1 + $tax_rate);

    return $tax_inclusive_price;
}, 10, 3);
```

## Security Considerations

### Data Protection

1. **Sanitize All Inputs**:

   ```php
   // Always sanitize country codes
   $country = sanitize_text_field($_POST['country']);
   if (!preg_match('/^[A-Z]{2}$/', $country)) {
       wp_die('Invalid country code');
   }
   ```

2. **Validate Pricing Data**:

   ```php
   // Validate price adjustments
   $adjustment = floatval($_POST['adjustment']);
   if ($adjustment < -100 || $adjustment > 1000) {
       wp_die('Invalid price adjustment');
   }
   ```

3. **Secure API Endpoints**:
   ```php
   // Add authentication to custom endpoints
   add_action('rest_api_init', function() {
       register_rest_route('pbc/v1', '/pricing', array(
           'methods' => 'GET',
           'callback' => 'get_pricing_data',
           'permission_callback' => function() {
               return current_user_can('manage_woocommerce');
           }
       ));
   });
   ```

### Privacy Compliance

#### GDPR Compliance

```php
// Add privacy policy content
add_action('admin_init', function() {
    if (function_exists('wp_add_privacy_policy_content')) {
        wp_add_privacy_policy_content(
            'Price by Country',
            'This site uses country detection to show appropriate pricing. We may collect and store your IP address and billing/shipping country for this purpose.'
        );
    }
});

// Handle data export requests
add_filter('wp_privacy_personal_data_exporters', function($exporters) {
    $exporters['pbc'] = array(
        'exporter_friendly_name' => 'Price by Country Data',
        'callback' => 'pbc_export_user_data'
    );
    return $exporters;
});

function pbc_export_user_data($email_address) {
    // Export user's pricing data
    $data_to_export = array();
    // ... implementation
    return array(
        'data' => $data_to_export,
        'done' => true
    );
}
```

## Maintenance and Updates

### Regular Maintenance Tasks

#### Weekly Tasks

1. **Check Error Logs**:

   ```bash
   # Check for plugin-related errors
   grep "PBC\|price-by-country" /var/log/apache2/error.log
   tail -100 wp-content/debug.log | grep -i "pbc"
   ```

2. **Monitor Performance**:

   ```sql
   -- Check database performance
   SELECT COUNT(*) as total_rules FROM wp_pbc_pricing_rules;
   SELECT COUNT(*) as active_rules FROM wp_pbc_pricing_rules WHERE is_active = 1;

   -- Check cache hit rates
   SELECT * FROM wp_pbc_country_cache WHERE expires_at > NOW();
   ```

3. **Validate Pricing Rules**:
   ```php
   // Check for conflicting rules
   $conflicts = check_pricing_rule_conflicts();
   if (!empty($conflicts)) {
       // Send admin notification
       wp_mail(get_option('admin_email'), 'Pricing Rule Conflicts', $conflicts);
   }
   ```

#### Monthly Tasks

1. **Database Cleanup**:

   ```sql
   -- Clean expired cache entries
   DELETE FROM wp_pbc_country_cache WHERE expires_at < NOW();

   -- Archive old log entries
   DELETE FROM wp_pbc_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
   ```

2. **Performance Review**:

   - Analyze slow query logs
   - Review cache hit rates
   - Check memory usage patterns

3. **Security Audit**:
   - Review user permissions
   - Check for unauthorized rule changes
   - Validate API access logs

### Update Procedures

#### Before Updating

1. **Create Full Backup**:

   ```bash
   # Database backup
   mysqldump -u username -p database_name > pbc_backup_$(date +%Y%m%d).sql

   # File backup
   tar -czf pbc_files_backup_$(date +%Y%m%d).tar.gz wp-content/plugins/price-by-country-woocommerce/
   ```

2. **Test on Staging**:

   - Deploy update to staging environment
   - Run full test suite
   - Verify all functionality

3. **Prepare Rollback Plan**:
   - Document current version
   - Prepare rollback scripts
   - Notify team of update window

#### Update Process

1. **Enable Maintenance Mode**:

   ```php
   // Add to wp-config.php
   define('WP_MAINTENANCE_MODE', true);
   ```

2. **Perform Update**:

   ```bash
   # Via WP-CLI
   wp plugin update price-by-country-woocommerce

   # Or manual upload
   # Upload new files via FTP/SFTP
   ```

3. **Run Post-Update Tasks**:

   ```php
   // Clear all caches
   wp_cache_flush();

   // Run database migrations if needed
   do_action('pbc_run_migrations');

   // Verify functionality
   $test_results = run_pbc_tests();
   ```

4. **Disable Maintenance Mode**:
   ```php
   // Remove from wp-config.php
   // define('WP_MAINTENANCE_MODE', true);
   ```

## Conclusion

This installation and configuration guide provides comprehensive instructions for setting up the Price by Country for WooCommerce plugin in various environments. Key takeaways:

### Quick Start Summary

1. **Verify Requirements**: Ensure WordPress 5.0+, WooCommerce 5.0+, PHP 7.4+
2. **Install Plugin**: Use WordPress admin, FTP, or WP-CLI
3. **Run Setup Wizard**: Configure country detection and create initial rules
4. **Test Functionality**: Verify pricing works correctly across different countries
5. **Monitor Performance**: Keep an eye on site performance and error logs

### Best Practices

- Always backup before installation or updates
- Test thoroughly on staging before production deployment
- Monitor performance impact and optimize as needed
- Keep the plugin updated for security and compatibility
- Document your specific configuration for team reference

### Support Resources

- **Documentation**: Complete feature documentation available
- **Support Forum**: Community support and troubleshooting
- **Direct Support**: Premium support with license key
- **Updates**: Automatic update notifications and changelog

For additional help or advanced configuration needs, consult the technical documentation or contact support with your specific requirements.
