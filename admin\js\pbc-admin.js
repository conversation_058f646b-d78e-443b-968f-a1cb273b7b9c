/**
 * Admin JavaScript for Price by Country plugin
 */

(function ($) {
  "use strict";

  // Initialize when document is ready
  $(document).ready(function () {
    PBC_Admin.init();
  });

  /**
   * Main admin object
   */
  var PBC_Admin = {
    /**
     * Initialize admin functionality
     */
    init: function () {
      this.initProductPricing();
      this.initCategoryPricing();
      this.initGlobalSettings();
    },

    /**
     * Initialize product-level pricing interface
     */
    initProductPricing: function () {
      var self = this;

      // Add new rule button
      $("#pbc-add-product-rule").on("click", function (e) {
        e.preventDefault();
        self.addProductRule();
      });

      // Remove rule buttons (delegated event)
      $(document).on("click", ".pbc-remove-rule", function (e) {
        e.preventDefault();
        self.removeProductRule($(this));
      });

      // Country selection change (delegated event)
      $(document).on("change", ".pbc-country-select", function () {
        self.validateProductRule($(this).closest(".pbc-rule-row"));
      });

      // Adjustment value change (delegated event)
      $(document).on("input", ".pbc-adjustment-value", function () {
        self.validateProductRule($(this).closest(".pbc-rule-row"));
      });

      // Form submission validation
      $("form#post").on("submit", function () {
        return self.validateAllProductRules();
      });
    },

    /**
     * Add new product pricing rule
     */
    addProductRule: function () {
      var template = $("#pbc-product-rule-template").html();
      var container = $("#pbc-product-rules-container");

      // Create new rule element
      var newRule = $(template);

      // Clear any pre-filled values from template
      newRule.find("select, input").val("");
      newRule.find('input[type="checkbox"]').prop("checked", true);
      newRule.find('input[name="pbc_rule_id[]"]').val("");

      // Add to container
      container.append(newRule);

      // Focus on country select
      newRule.find(".pbc-country-select").focus();
    },

    /**
     * Remove product pricing rule
     */
    removeProductRule: function ($button) {
      var $row = $button.closest(".pbc-rule-row");
      var ruleId = $row.data("rule-id");

      // Confirm deletion
      if (!confirm(pbc_admin.strings.confirm_delete)) {
        return;
      }

      // If this is an existing rule (has ID), mark for deletion
      if (ruleId) {
        this.deleteProductRuleAjax(ruleId, $row);
      } else {
        // Just remove from DOM for new rules
        $row.fadeOut(300, function () {
          $(this).remove();
        });
      }
    },

    /**
     * Delete product rule via AJAX
     */
    deleteProductRuleAjax: function (ruleId, $row) {
      var self = this;

      $row.addClass("pbc-loading");

      $.ajax({
        url: pbc_admin.ajax_url,
        type: "POST",
        data: {
          action: "pbc_delete_product_rule",
          rule_id: ruleId,
          nonce: pbc_admin.nonce,
        },
        success: function (response) {
          if (response.success) {
            $row.fadeOut(300, function () {
              $(this).remove();
            });
          } else {
            alert(response.data || pbc_admin.strings.error);
            $row.removeClass("pbc-loading");
          }
        },
        error: function () {
          alert(pbc_admin.strings.error);
          $row.removeClass("pbc-loading");
        },
      });
    },

    /**
     * Validate a single product rule
     */
    validateProductRule: function ($row) {
      var isValid = true;
      var $countrySelect = $row.find(".pbc-country-select");
      var $adjustmentValue = $row.find(".pbc-adjustment-value");

      // Reset validation styles
      $countrySelect.removeClass("error");
      $adjustmentValue.removeClass("error");

      // Validate country selection
      if (!$countrySelect.val()) {
        $countrySelect.addClass("error");
        isValid = false;
      }

      // Validate adjustment value
      var value = parseFloat($adjustmentValue.val());
      if (isNaN(value) || value === 0) {
        $adjustmentValue.addClass("error");
        isValid = false;
      }

      return isValid;
    },

    /**
     * Validate all product rules before form submission
     */
    validateAllProductRules: function () {
      var self = this;
      var allValid = true;
      var countries = [];

      $(".pbc-rule-row").each(function () {
        var $row = $(this);
        var isValid = self.validateProductRule($row);

        if (!isValid) {
          allValid = false;
        }

        // Check for duplicate countries
        var country = $row.find(".pbc-country-select").val();
        if (country && countries.indexOf(country) !== -1) {
          $row.find(".pbc-country-select").addClass("error");
          allValid = false;
        }
        if (country) {
          countries.push(country);
        }
      });

      if (!allValid) {
        alert("Please fix the highlighted errors before saving.");
      }

      return allValid;
    },

    /**
     * Initialize category-level pricing interface
     */
    initCategoryPricing: function () {
      var self = this;

      // Add category rule button
      $("#pbc-add-category-rule").on("click", function (e) {
        e.preventDefault();
        self.addCategoryRule();
      });

      // Bulk apply button
      $("#pbc-bulk-apply").on("click", function (e) {
        e.preventDefault();
        self.bulkApplyCategoryRules();
      });

      // Remove category rule buttons (delegated event)
      $(document).on("click", ".pbc-remove-category-rule", function (e) {
        e.preventDefault();
        self.removeCategoryRule($(this));
      });
    },

    /**
     * Add new category pricing rule
     */
    addCategoryRule: function () {
      var template = $("#pbc-category-rule-template").html();
      var container = $("#pbc-category-rules-container");

      var newRule = $(template);
      newRule.find("select, input").val("");
      newRule.find('input[type="checkbox"]').prop("checked", true);
      newRule.find('input[name="pbc_category_rule_id[]"]').val("");

      container.append(newRule);
      newRule.find(".pbc-country-select").focus();
    },

    /**
     * Remove category pricing rule
     */
    removeCategoryRule: function ($button) {
      var $row = $button.closest(".pbc-rule-row");

      if (!confirm(pbc_admin.strings.confirm_delete)) {
        return;
      }

      $row.fadeOut(300, function () {
        $(this).remove();
      });
    },

    /**
     * Bulk apply category rules to products
     */
    bulkApplyCategoryRules: function () {
      var categoryId = $("#pbc-category-id").val();
      var applyToExisting = $("#pbc-apply-to-existing").is(":checked");

      if (
        !confirm("Apply these pricing rules to all products in this category?")
      ) {
        return;
      }

      var $button = $("#pbc-bulk-apply");
      $button.prop("disabled", true).text(pbc_admin.strings.saving);

      $.ajax({
        url: pbc_admin.ajax_url,
        type: "POST",
        data: {
          action: "pbc_bulk_apply_category_rules",
          category_id: categoryId,
          apply_to_existing: applyToExisting ? 1 : 0,
          nonce: pbc_admin.nonce,
        },
        success: function (response) {
          if (response.success) {
            alert(
              "Rules applied successfully to " +
                response.data.count +
                " products."
            );
          } else {
            alert(response.data || pbc_admin.strings.error);
          }
        },
        error: function () {
          alert(pbc_admin.strings.error);
        },
        complete: function () {
          $button.prop("disabled", false).text("Apply to Products");
        },
      });
    },

    /**
     * Initialize global settings interface
     */
    initGlobalSettings: function () {
      var self = this;

      // Add global rule button
      $("#pbc-add-global-rule").on("click", function (e) {
        e.preventDefault();
        self.addGlobalRule();
      });

      // Save global settings button
      $("#pbc-save-global-settings").on("click", function (e) {
        e.preventDefault();
        self.saveGlobalSettings();
      });

      // Remove global rule buttons (delegated event)
      $(document).on("click", ".pbc-remove-global-rule", function (e) {
        e.preventDefault();
        self.removeGlobalRule($(this));
      });

      // Import/Export functionality
      $("#pbc-export-rules").on("click", function (e) {
        e.preventDefault();
        self.exportRules();
      });

      $("#pbc-import-rules").on("click", function (e) {
        e.preventDefault();
        self.importRules();
      });
    },

    /**
     * Add new global pricing rule
     */
    addGlobalRule: function () {
      var template = $("#pbc-global-rule-template").html();
      var container = $("#pbc-global-rules-container");

      var newRule = $(template);
      newRule.find("select, input").val("");
      newRule.find('input[type="checkbox"]').prop("checked", true);
      newRule.find('input[name="pbc_global_rule_id[]"]').val("");

      container.append(newRule);
      newRule.find(".pbc-country-select").focus();
    },

    /**
     * Remove global pricing rule
     */
    removeGlobalRule: function ($button) {
      var $row = $button.closest(".pbc-rule-row");

      if (!confirm(pbc_admin.strings.confirm_delete)) {
        return;
      }

      $row.fadeOut(300, function () {
        $(this).remove();
      });
    },

    /**
     * Save global settings via AJAX
     */
    saveGlobalSettings: function () {
      var $button = $("#pbc-save-global-settings");
      $button.prop("disabled", true).text(pbc_admin.strings.saving);

      var formData = $("#pbc-global-settings-form").serialize();
      formData += "&action=pbc_save_global_settings&nonce=" + pbc_admin.nonce;

      $.ajax({
        url: pbc_admin.ajax_url,
        type: "POST",
        data: formData,
        success: function (response) {
          if (response.success) {
            self.showMessage("Settings saved successfully!", "success");
          } else {
            self.showMessage(response.data || pbc_admin.strings.error, "error");
          }
        },
        error: function () {
          self.showMessage(pbc_admin.strings.error, "error");
        },
        complete: function () {
          $button.prop("disabled", false).text("Save Settings");
        },
      });
    },

    /**
     * Export pricing rules
     */
    exportRules: function () {
      window.location.href =
        pbc_admin.ajax_url +
        "?action=pbc_export_rules&nonce=" +
        pbc_admin.nonce;
    },

    /**
     * Import pricing rules
     */
    importRules: function () {
      $("#pbc-import-file").click();
    },

    /**
     * Show admin message
     */
    showMessage: function (message, type) {
      var $message = $(
        '<div class="pbc-message ' + type + '">' + message + "</div>"
      );
      $(".pbc-global-settings").prepend($message);

      setTimeout(function () {
        $message.fadeOut(300, function () {
          $(this).remove();
        });
      }, 3000);
    },

    /**
     * Utility function to get form data as object
     */
    getFormData: function ($form) {
      var data = {};
      $form.serializeArray().forEach(function (item) {
        if (data[item.name]) {
          if (!Array.isArray(data[item.name])) {
            data[item.name] = [data[item.name]];
          }
          data[item.name].push(item.value);
        } else {
          data[item.name] = item.value;
        }
      });
      return data;
    },
  };

  // Make PBC_Admin globally available
  window.PBC_Admin = PBC_Admin;
})(jQuery);

// Add CSS for validation errors
jQuery(document).ready(function ($) {
  $("<style>")
    .prop("type", "text/css")
    .html(
      `
            .pbc-rule-row select.error,
            .pbc-rule-row input.error {
                border-color: #dc3232 !important;
                box-shadow: 0 0 2px rgba(220, 50, 50, 0.8) !important;
            }
        `
    )
    .appendTo("head");
});
/**
 * Dashboard functionality
 */
jQuery(document).ready(function ($) {
  // Initialize dashboard if we're on the dashboard page
  if ($(".pbc-rules-table").length > 0) {
    PBC_Dashboard.init();
  }
});

var PBC_Dashboard = {
  /**
   * Initialize dashboard functionality
   */
  init: function () {
    this.initTableActions();
    this.initBulkActions();
    this.initModal();
    this.initQuickActions();
    this.initConflicts();
  },

  /**
   * Initialize table row actions
   */
  initTableActions: function () {
    var self = this;

    // Select all checkbox
    $("#pbc-select-all").on("change", function () {
      $('input[name="rule_ids[]"]').prop("checked", $(this).is(":checked"));
    });

    // Individual checkboxes
    $(document).on("change", 'input[name="rule_ids[]"]', function () {
      var totalCheckboxes = $('input[name="rule_ids[]"]').length;
      var checkedCheckboxes = $('input[name="rule_ids[]"]:checked').length;

      $("#pbc-select-all").prop(
        "checked",
        totalCheckboxes === checkedCheckboxes
      );
    });

    // Edit rule action
    $(document).on("click", ".pbc-edit-rule", function (e) {
      e.preventDefault();
      var ruleId = $(this).data("rule-id");
      self.editRule(ruleId);
    });

    // Toggle rule status
    $(document).on("click", ".pbc-toggle-rule", function (e) {
      e.preventDefault();
      var ruleId = $(this).data("rule-id");
      var currentStatus = $(this).data("status");
      var newStatus = currentStatus == 1 ? 0 : 1;
      self.toggleRuleStatus(ruleId, newStatus, $(this));
    });

    // Delete rule action
    $(document).on("click", ".pbc-delete-rule", function (e) {
      e.preventDefault();
      var ruleId = $(this).data("rule-id");
      self.deleteRule(ruleId, $(this).closest("tr"));
    });
  },

  /**
   * Initialize bulk actions
   */
  initBulkActions: function () {
    var self = this;

    $("#pbc-apply-bulk").on("click", function (e) {
      e.preventDefault();

      var action = $("#pbc-bulk-action").val();
      var selectedRules = $('input[name="rule_ids[]"]:checked')
        .map(function () {
          return $(this).val();
        })
        .get();

      if (!action) {
        alert("Please select a bulk action.");
        return;
      }

      if (selectedRules.length === 0) {
        alert("Please select at least one rule.");
        return;
      }

      if (
        !confirm(
          "Are you sure you want to " +
            action +
            " " +
            selectedRules.length +
            " rule(s)?"
        )
      ) {
        return;
      }

      self.performBulkAction(action, selectedRules);
    });
  },

  /**
   * Initialize modal functionality
   */
  initModal: function () {
    var self = this;

    // Close modal buttons
    $(document).on("click", ".pbc-modal-close", function () {
      self.closeModal();
    });

    // Close modal when clicking outside
    $(document).on("click", ".pbc-modal", function (e) {
      if (e.target === this) {
        self.closeModal();
      }
    });

    // Save rule edit
    $("#pbc-save-rule-edit").on("click", function (e) {
      e.preventDefault();
      self.saveRuleEdit();
    });
  },

  /**
   * Initialize quick actions
   */
  initQuickActions: function () {
    var self = this;

    // Cleanup cache
    $("#pbc-cleanup-cache").on("click", function (e) {
      e.preventDefault();
      self.cleanupCache();
    });

    // Export filtered rules
    $("#pbc-export-filtered").on("click", function (e) {
      e.preventDefault();
      self.exportFilteredRules();
    });

    // Detect conflicts
    $("#pbc-detect-conflicts").on("click", function (e) {
      e.preventDefault();
      self.detectConflicts();
    });
  },

  /**
   * Initialize conflicts functionality
   */
  initConflicts: function () {
    $("#pbc-show-conflicts").on("click", function (e) {
      e.preventDefault();
      $("#pbc-conflicts-details").toggle();
    });
  },

  /**
   * Edit rule
   */
  editRule: function (ruleId) {
    var self = this;

    // Get rule data
    $.ajax({
      url: pbc_admin.ajax_url,
      type: "POST",
      data: {
        action: "pbc_get_rule_data",
        rule_id: ruleId,
        nonce: pbc_admin.nonce,
      },
      success: function (response) {
        if (response.success) {
          self.populateEditModal(response.data);
          self.showModal("#pbc-edit-rule-modal");
        } else {
          alert(response.data || "Failed to load rule data");
        }
      },
      error: function () {
        alert("Error loading rule data");
      },
    });
  },

  /**
   * Populate edit modal with rule data
   */
  populateEditModal: function (rule) {
    $("#edit-rule-id").val(rule.id);
    $("#edit-country-code").val(rule.country_code);
    $("#edit-adjustment-type").val(rule.adjustment_type);
    $("#edit-adjustment-value").val(rule.adjustment_value);
    $("#edit-is-active").prop("checked", rule.is_active == 1);
  },

  /**
   * Save rule edit
   */
  saveRuleEdit: function () {
    var self = this;
    var formData = $("#pbc-edit-rule-form").serialize();

    $("#pbc-save-rule-edit").prop("disabled", true).text("Saving...");

    $.ajax({
      url: pbc_admin.ajax_url,
      type: "POST",
      data: formData + "&action=pbc_edit_rule&nonce=" + pbc_admin.nonce,
      success: function (response) {
        if (response.success) {
          self.closeModal();
          location.reload(); // Reload to show updated data
        } else {
          alert(response.data || "Failed to save rule");
        }
      },
      error: function () {
        alert("Error saving rule");
      },
      complete: function () {
        $("#pbc-save-rule-edit").prop("disabled", false).text("Save Changes");
      },
    });
  },

  /**
   * Toggle rule status
   */
  toggleRuleStatus: function (ruleId, newStatus, $element) {
    var self = this;
    var $row = $element.closest("tr");

    $row.addClass("pbc-loading");

    $.ajax({
      url: pbc_admin.ajax_url,
      type: "POST",
      data: {
        action: "pbc_toggle_rule_status",
        rule_id: ruleId,
        status: newStatus,
        nonce: pbc_admin.nonce,
      },
      success: function (response) {
        if (response.success) {
          // Update the UI
          var $statusCell = $row.find(".column-status .pbc-status");
          var $toggleLink = $row.find(".pbc-toggle-rule");

          if (newStatus == 1) {
            $statusCell
              .removeClass("pbc-status-inactive")
              .addClass("pbc-status-active")
              .text("Active");
            $toggleLink.text("Deactivate").data("status", 1);
          } else {
            $statusCell
              .removeClass("pbc-status-active")
              .addClass("pbc-status-inactive")
              .text("Inactive");
            $toggleLink.text("Activate").data("status", 0);
          }

          $row.addClass("pbc-highlight");
          setTimeout(function () {
            $row.removeClass("pbc-highlight");
          }, 2000);
        } else {
          alert(response.data || "Failed to update rule status");
        }
      },
      error: function () {
        alert("Error updating rule status");
      },
      complete: function () {
        $row.removeClass("pbc-loading");
      },
    });
  },

  /**
   * Delete rule
   */
  deleteRule: function (ruleId, $row) {
    var self = this;

    if (!confirm("Are you sure you want to delete this pricing rule?")) {
      return;
    }

    $row.addClass("pbc-loading");

    $.ajax({
      url: pbc_admin.ajax_url,
      type: "POST",
      data: {
        action: "pbc_delete_rule",
        rule_id: ruleId,
        nonce: pbc_admin.nonce,
      },
      success: function (response) {
        if (response.success) {
          $row.fadeOut(300, function () {
            $(this).remove();
          });
        } else {
          alert(response.data || "Failed to delete rule");
          $row.removeClass("pbc-loading");
        }
      },
      error: function () {
        alert("Error deleting rule");
        $row.removeClass("pbc-loading");
      },
    });
  },

  /**
   * Perform bulk action
   */
  performBulkAction: function (action, ruleIds) {
    var self = this;
    var $button = $("#pbc-apply-bulk");

    $button.prop("disabled", true).text("Processing...");

    $.ajax({
      url: pbc_admin.ajax_url,
      type: "POST",
      data: {
        action: "pbc_bulk_action_rules",
        bulk_action: action,
        rule_ids: ruleIds,
        nonce: pbc_admin.nonce,
      },
      success: function (response) {
        if (response.success) {
          alert(response.data.message);
          location.reload(); // Reload to show updated data
        } else {
          alert(response.data || "Bulk action failed");
        }
      },
      error: function () {
        alert("Error performing bulk action");
      },
      complete: function () {
        $button.prop("disabled", false).text("Apply");
      },
    });
  },

  /**
   * Cleanup cache
   */
  cleanupCache: function () {
    var $button = $("#pbc-cleanup-cache");

    $button.prop("disabled", true).text("Cleaning...");

    $.ajax({
      url: pbc_admin.ajax_url,
      type: "POST",
      data: {
        action: "pbc_cleanup_cache",
        nonce: pbc_admin.nonce,
      },
      success: function (response) {
        if (response.success) {
          alert(response.data.message);
        } else {
          alert(response.data || "Cache cleanup failed");
        }
      },
      error: function () {
        alert("Error cleaning cache");
      },
      complete: function () {
        $button.prop("disabled", false).text("Clean Up Cache");
      },
    });
  },

  /**
   * Export filtered rules
   */
  exportFilteredRules: function () {
    // Get current filter parameters
    var params = new URLSearchParams(window.location.search);
    params.set("action", "pbc_export_rules");
    params.set("nonce", pbc_admin.nonce);

    window.location.href = pbc_admin.ajax_url + "?" + params.toString();
  },

  /**
   * Detect conflicts
   */
  detectConflicts: function () {
    var $button = $("#pbc-detect-conflicts");

    $button.prop("disabled", true).text("Detecting...");

    $.ajax({
      url: pbc_admin.ajax_url,
      type: "POST",
      data: {
        action: "pbc_detect_conflicts",
        nonce: pbc_admin.nonce,
      },
      success: function (response) {
        if (response.success) {
          alert(response.data.message);
          if (response.data.count > 0) {
            location.reload(); // Reload to show conflicts
          }
        } else {
          alert(response.data || "Conflict detection failed");
        }
      },
      error: function () {
        alert("Error detecting conflicts");
      },
      complete: function () {
        $button.prop("disabled", false).text("Detect Conflicts");
      },
    });
  },

  /**
   * Show modal
   */
  showModal: function (modalSelector) {
    $(modalSelector).show().addClass("pbc-fade-in");
    $("body").addClass("modal-open");
  },

  /**
   * Close modal
   */
  closeModal: function () {
    $(".pbc-modal").hide().removeClass("pbc-fade-in");
    $("body").removeClass("modal-open");
  },
};

// Make PBC_Dashboard globally available
window.PBC_Dashboard = PBC_Dashboard;
/**
 * Enhanced Country Detection Settings functionality
 */
jQuery(document).ready(function ($) {
  // Initialize country detection settings if we're on that page
  if ($("#pbc-country-detection-form").length > 0) {
    PBC_CountryDetection.init();
  }
});

var PBC_CountryDetection = {
  /**
   * Initialize country detection functionality
   */
  init: function () {
    this.initMethodToggle();
    this.initPriorityControls();
    this.initTestingTools();
    this.initSettingsActions();
    this.initLogViewer();
    this.initServiceSettings();
    this.initAccuracySlider();
    this.initFormValidation();
    this.initAutoSave();
    this.addLoadingStates();
    this.initTooltips();
  },

  /**
   * Enhanced detection method toggle with animations
   */
  initMethodToggle: function () {
    var self = this;

    $("#pbc_detection_method").on("change", function () {
      var method = $(this).val();
      var $prioritySettings = $("#pbc-priority-settings");

      if (method === "auto") {
        $prioritySettings.slideDown(300, function () {
          self.updateMethodIndicators();
        });
      } else {
        $prioritySettings.slideUp(300, function () {
          self.updateMethodIndicators();
        });
      }

      // Update method indicator
      self.updateMethodIndicators();
    });

    // Initialize on page load
    this.updateMethodIndicators();
  },

  /**
   * Update method indicators
   */
  updateMethodIndicators: function () {
    var method = $("#pbc_detection_method").val();
    var $indicator = $(".pbc-current-method-indicator");

    if ($indicator.length === 0) {
      $indicator = $('<div class="pbc-current-method-indicator"></div>');
      $("#pbc_detection_method").after($indicator);
    }

    var indicatorClass = "pbc-method-indicator " + method;
    var methodText = {
      auto: "Automatic Detection",
      ip: "IP Address Only",
      billing: "Billing Address Only",
      shipping: "Shipping Address Only",
    };

    $indicator.html(
      '<span class="' + indicatorClass + '">' + methodText[method] + "</span>"
    );
  },

  /**
   * Enhanced priority controls with drag and drop
   */
  initPriorityControls: function () {
    var self = this;

    // Make priority list sortable
    if ($.fn.sortable) {
      $(".pbc-priority-list").sortable({
        items: ".pbc-priority-item",
        handle: ".pbc-priority-item",
        placeholder: "pbc-priority-placeholder",
        start: function (e, ui) {
          ui.item.addClass("moving");
        },
        stop: function (e, ui) {
          ui.item.removeClass("moving");
          self.updatePriorityNumbers();
          self.showSaveNotification("Priority order updated");
        },
      });
    }

    // Move up button with animation
    $(document).on("click", ".pbc-move-up", function (e) {
      e.preventDefault();
      var $item = $(this).closest(".pbc-priority-item");
      var $prev = $item.prev(".pbc-priority-item");

      if ($prev.length) {
        $item.addClass("moving");
        $item.insertBefore($prev);
        setTimeout(function () {
          $item.removeClass("moving");
          self.updatePriorityNumbers();
          self.showSaveNotification("Priority order updated");
        }, 200);
      }
    });

    // Move down button with animation
    $(document).on("click", ".pbc-move-down", function (e) {
      e.preventDefault();
      var $item = $(this).closest(".pbc-priority-item");
      var $next = $item.next(".pbc-priority-item");

      if ($next.length) {
        $item.addClass("moving");
        $item.insertAfter($next);
        setTimeout(function () {
          $item.removeClass("moving");
          self.updatePriorityNumbers();
          self.showSaveNotification("Priority order updated");
        }, 200);
      }
    });
  },

  /**
   * Enhanced priority numbers update with animations
   */
  updatePriorityNumbers: function () {
    $(".pbc-priority-item").each(function (index) {
      var $number = $(this).find(".pbc-priority-number");
      var newNumber = index + 1;

      if ($number.text() != newNumber) {
        $number.fadeOut(150, function () {
          $(this).text(newNumber).fadeIn(150);
        });
      }

      // Update button states with smooth transitions
      var $moveUp = $(this).find(".pbc-move-up");
      var $moveDown = $(this).find(".pbc-move-down");
      var totalItems = $(".pbc-priority-item").length;

      $moveUp.prop("disabled", index === 0);
      $moveDown.prop("disabled", index === totalItems - 1);

      // Update hidden input values
      $(this)
        .find('input[name="pbc_detection_priority[]"]')
        .val($(this).data("method"));
    });
  },

  /**
   * Enhanced testing tools with better UX
   */
  initTestingTools: function () {
    var self = this;

    // Test IP detection with enhanced validation
    $("#pbc_test_ip_detection").on("click", function (e) {
      e.preventDefault();
      var testIp = $("#pbc_test_ip").val().trim();

      if (!testIp) {
        self.showValidationError(
          "#pbc_test_ip",
          "Please enter an IP address to test."
        );
        return;
      }

      if (!self.isValidIP(testIp)) {
        self.showValidationError(
          "#pbc_test_ip",
          "Please enter a valid IP address."
        );
        return;
      }

      self.testIpDetection(testIp);
    });

    // Test user detection with enhanced validation
    $("#pbc_test_user_detection").on("click", function (e) {
      e.preventDefault();
      var userId = $("#pbc_test_user_id").val().trim();

      if (!userId) {
        self.showValidationError(
          "#pbc_test_user_id",
          "Please enter a user ID to test."
        );
        return;
      }

      if (isNaN(userId) || parseInt(userId) <= 0) {
        self.showValidationError(
          "#pbc_test_user_id",
          "Please enter a valid user ID."
        );
        return;
      }

      self.testUserDetection(userId);
    });

    // Clear validation errors on input
    $("#pbc_test_ip, #pbc_test_user_id").on("input", function () {
      self.clearValidationError(this);
    });

    // Add sample IP suggestions
    this.addIPSuggestions();
  },

  /**
   * Validate IP address format
   */
  isValidIP: function (ip) {
    var ipRegex =
      /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipRegex.test(ip);
  },

  /**
   * Show validation error
   */
  showValidationError: function (selector, message) {
    var $input = $(selector);
    $input.addClass("error");

    var $error = $input.siblings(".validation-error");
    if ($error.length === 0) {
      $error = $('<div class="validation-error"></div>');
      $input.after($error);
    }

    $error.text(message).slideDown(200);
  },

  /**
   * Clear validation error
   */
  clearValidationError: function (input) {
    var $input = $(input);
    $input.removeClass("error");
    $input.siblings(".validation-error").slideUp(200, function () {
      $(this).remove();
    });
  },

  /**
   * Add IP address suggestions
   */
  addIPSuggestions: function () {
    var suggestions = [
      { ip: "*******", label: "Google DNS (US)" },
      { ip: "*******", label: "Cloudflare (US)" },
      { ip: "**************", label: "OpenDNS (US)" },
      { ip: "*********", label: "Yandex (Russia)" },
      { ip: "***************", label: "114DNS (China)" },
    ];

    var $container = $('<div class="pbc-ip-suggestions"></div>');
    var $label = $("<small>Quick test IPs:</small>");
    $container.append($label);

    suggestions.forEach(function (suggestion) {
      var $link = $(
        '<a href="#" class="pbc-ip-suggestion" data-ip="' +
          suggestion.ip +
          '">' +
          suggestion.ip +
          " (" +
          suggestion.label +
          ")</a>"
      );
      $container.append($link);
    });

    $("#pbc_test_ip").after($container);

    // Handle suggestion clicks
    $(document).on("click", ".pbc-ip-suggestion", function (e) {
      e.preventDefault();
      var ip = $(this).data("ip");
      $("#pbc_test_ip").val(ip).focus();
    });
  },

  /**
   * Enhanced IP detection test with better feedback
   */
  testIpDetection: function (testIp) {
    var self = this;
    var $button = $("#pbc_test_ip_detection");
    var $results = $("#pbc_test_results");
    var $output = $("#pbc_test_output");

    // Add loading state
    $button.addClass("loading").prop("disabled", true);
    $results.removeClass("show");

    // Clear previous results
    $output.empty();

    $.ajax({
      url: pbc_admin.ajax_url,
      type: "POST",
      data: {
        action: "pbc_test_ip_detection",
        test_ip: testIp,
        nonce: pbc_admin.nonce,
      },
      success: function (response) {
        if (response.success) {
          var data = response.data;
          var resultHtml = '<div class="pbc-test-success">';
          resultHtml += "<h5>Detection Successful</h5>";
          resultHtml += '<div class="test-result-grid">';
          resultHtml +=
            '<div class="result-item"><strong>IP Address:</strong> ' +
            testIp +
            "</div>";
          resultHtml +=
            '<div class="result-item"><strong>Country:</strong> ' +
            data.country_name +
            " (" +
            data.country_code +
            ")</div>";
          resultHtml +=
            '<div class="result-item"><strong>Detection Method:</strong> <span class="pbc-method-indicator ' +
            data.detection_method +
            '">' +
            data.detection_method +
            "</span></div>";

          if (data.confidence) {
            resultHtml +=
              '<div class="result-item"><strong>Confidence:</strong> ' +
              (data.confidence * 100).toFixed(1) +
              "%</div>";
          }

          if (data.response_time) {
            resultHtml +=
              '<div class="result-item"><strong>Response Time:</strong> ' +
              data.response_time +
              "ms</div>";
          }

          resultHtml += "</div></div>";
          $output.html(resultHtml);
        } else {
          $output.html(
            '<div class="pbc-test-error">' +
              "<h5>Detection Failed</h5>" +
              "<p>" +
              (response.data || "Unknown error occurred") +
              "</p>" +
              "</div>"
          );
        }

        $results.addClass("show");
        self.scrollToResults();
      },
      error: function (xhr, status, error) {
        $output.html(
          '<div class="pbc-test-error">' +
            "<h5>Network Error</h5>" +
            "<p>Failed to connect to the detection service. Please check your connection and try again.</p>" +
            "<small>Error: " +
            error +
            "</small>" +
            "</div>"
        );
        $results.addClass("show");
        self.scrollToResults();
      },
      complete: function () {
        $button.removeClass("loading").prop("disabled", false);
      },
    });
  },

  /**
   * Test user detection
   */
  testUserDetection: function (userId) {
    var $button = $("#pbc_test_user_detection");
    var $results = $("#pbc_test_results");
    var $output = $("#pbc_test_output");

    $button.prop("disabled", true).text("Testing...");

    $.ajax({
      url: pbc_admin.ajax_url,
      type: "POST",
      data: {
        action: "pbc_test_user_detection",
        user_id: userId,
        nonce: pbc_admin.nonce,
      },
      success: function (response) {
        if (response.success) {
          var resultsHtml = '<div class="pbc-test-success">';
          resultsHtml +=
            "<strong>Success:</strong> " + response.data.message + "<br>";

          response.data.results.forEach(function (result) {
            resultsHtml +=
              "<strong>" +
              result.method.charAt(0).toUpperCase() +
              result.method.slice(1) +
              ":</strong> ";
            resultsHtml +=
              result.country_name + " (" + result.country_code + ")<br>";
          });

          resultsHtml += "</div>";
          $output.html(resultsHtml);
        } else {
          $output.html(
            '<div class="pbc-test-error">' +
              "<strong>Error:</strong> " +
              (response.data || "Detection failed") +
              "</div>"
          );
        }
        $results.show();
      },
      error: function () {
        $output.html(
          '<div class="pbc-test-error">' +
            "<strong>Error:</strong> Network error during testing" +
            "</div>"
        );
        $results.show();
      },
      complete: function () {
        $button.prop("disabled", false).text("Test User Detection");
      },
    });
  },

  /**
   * Initialize settings actions
   */
  initSettingsActions: function () {
    var self = this;

    // Save settings
    $("#pbc-save-detection-settings").on("click", function (e) {
      e.preventDefault();
      self.saveDetectionSettings();
    });

    // Reset to defaults
    $("#pbc-reset-detection-settings").on("click", function (e) {
      e.preventDefault();
      if (
        confirm(
          "Are you sure you want to reset all detection settings to defaults?"
        )
      ) {
        self.resetDetectionSettings();
      }
    });
  },

  /**
   * Save detection settings
   */
  saveDetectionSettings: function () {
    var $button = $("#pbc-save-detection-settings");
    $button.prop("disabled", true).text("Saving...");

    var formData = $("#pbc-country-detection-form").serialize();
    formData += "&action=pbc_save_detection_settings&nonce=" + pbc_admin.nonce;

    $.ajax({
      url: pbc_admin.ajax_url,
      type: "POST",
      data: formData,
      success: function (response) {
        if (response.success) {
          PBC_Admin.showMessage(
            "Detection settings saved successfully!",
            "success"
          );
        } else {
          PBC_Admin.showMessage(
            response.data || "Failed to save settings",
            "error"
          );
        }
      },
      error: function () {
        PBC_Admin.showMessage("Network error while saving settings", "error");
      },
      complete: function () {
        $button.prop("disabled", false).text("Save Detection Settings");
      },
    });
  },

  /**
   * Reset detection settings to defaults
   */
  resetDetectionSettings: function () {
    // Reset form fields to default values
    $("#pbc_detection_method").val("auto");
    $("#pbc_cache_duration").val("3600");
    $("#pbc_fallback_country").val("US");
    $("#pbc_enable_logging").prop("checked", false);
    $("#pbc_ip_detection_service").val("woocommerce");
    $("#pbc_detection_accuracy_threshold").val("0.8");

    // Reset priority order
    var defaultPriority = ["shipping", "billing", "ip"];
    var $priorityList = $(".pbc-priority-list");

    $priorityList.empty();
    defaultPriority.forEach(function (method, index) {
      var labels = {
        shipping: "Shipping Address",
        billing: "Billing Address",
        ip: "IP Address",
      };

      var itemHtml =
        '<div class="pbc-priority-item" data-method="' +
        method +
        '">' +
        '<span class="pbc-priority-number">' +
        (index + 1) +
        "</span>" +
        '<span class="pbc-priority-label">' +
        labels[method] +
        "</span>" +
        '<div class="pbc-priority-controls">' +
        '<button type="button" class="button pbc-move-up"' +
        (index === 0 ? " disabled" : "") +
        ">↑</button>" +
        '<button type="button" class="button pbc-move-down"' +
        (index === defaultPriority.length - 1 ? " disabled" : "") +
        ">↓</button>" +
        "</div>" +
        '<input type="hidden" name="pbc_detection_priority[]" value="' +
        method +
        '" />' +
        "</div>";

      $priorityList.append(itemHtml);
    });

    // Show priority settings
    $("#pbc-priority-settings").show();

    PBC_Admin.showMessage("Settings reset to defaults", "success");
  },

  /**
   * Initialize log viewer
   */
  initLogViewer: function () {
    var self = this;

    // View logs
    $("#pbc_view_detection_logs").on("click", function (e) {
      e.preventDefault();
      self.viewDetectionLogs();
    });

    // Clear logs
    $("#pbc_clear_detection_logs").on("click", function (e) {
      e.preventDefault();
      if (confirm("Are you sure you want to clear all detection logs?")) {
        self.clearDetectionLogs();
      }
    });
  },

  /**
   * View detection logs
   */
  viewDetectionLogs: function () {
    var $modal = $("#pbc-detection-log-modal");
    var $content = $("#pbc-detection-log-content");

    $content.html("<p>Loading logs...</p>");
    PBC_Dashboard.showModal("#pbc-detection-log-modal");

    $.ajax({
      url: pbc_admin.ajax_url,
      type: "POST",
      data: {
        action: "pbc_view_detection_logs",
        nonce: pbc_admin.nonce,
      },
      success: function (response) {
        if (response.success) {
          var logsHtml = '<div class="pbc-logs-container">';

          if (response.data.logs.length === 0) {
            logsHtml += "<p>No detection logs found.</p>";
          } else {
            logsHtml += '<table class="wp-list-table widefat fixed striped">';
            logsHtml += "<thead><tr>";
            logsHtml += "<th>Timestamp</th>";
            logsHtml += "<th>IP Address</th>";
            logsHtml += "<th>Method</th>";
            logsHtml += "<th>Country</th>";
            logsHtml += "<th>Status</th>";
            logsHtml += "<th>Message</th>";
            logsHtml += "</tr></thead><tbody>";

            response.data.logs.forEach(function (log) {
              logsHtml += "<tr>";
              logsHtml += "<td>" + log.timestamp + "</td>";
              logsHtml += "<td>" + log.ip_address + "</td>";
              logsHtml += "<td>" + log.method + "</td>";
              logsHtml += "<td>" + log.country + "</td>";
              logsHtml +=
                "<td>" +
                (log.success
                  ? '<span style="color: green;">Success</span>'
                  : '<span style="color: red;">Failed</span>') +
                "</td>";
              logsHtml += "<td>" + log.message + "</td>";
              logsHtml += "</tr>";
            });

            logsHtml += "</tbody></table>";
          }

          logsHtml += "</div>";
          $content.html(logsHtml);
        } else {
          $content.html(
            '<p class="error">Failed to load logs: ' +
              (response.data || "Unknown error") +
              "</p>"
          );
        }
      },
      error: function () {
        $content.html('<p class="error">Network error while loading logs</p>');
      },
    });
  },

  /**
   * Clear detection logs
   */
  clearDetectionLogs: function () {
    var $button = $("#pbc_clear_detection_logs");
    $button.prop("disabled", true).text("Clearing...");

    $.ajax({
      url: pbc_admin.ajax_url,
      type: "POST",
      data: {
        action: "pbc_clear_detection_logs",
        nonce: pbc_admin.nonce,
      },
      success: function (response) {
        if (response.success) {
          PBC_Admin.showMessage(response.data.message, "success");
        } else {
          PBC_Admin.showMessage(
            response.data || "Failed to clear logs",
            "error"
          );
        }
      },
      error: function () {
        PBC_Admin.showMessage("Network error while clearing logs", "error");
      },
      complete: function () {
        $button.prop("disabled", false).text("Clear Logs");
      },
    });
  },
};

// Make PBC_CountryDetection globally available
window.PBC_CountryDetection = PBC_CountryDetection;
// ========================================
// IMPORT/EXPORT FUNCTIONALITY
// ========================================

/**
 * Initialize import/export functionality
 */
function initImportExport() {
  // Export functionality
  $("#pbc-export-now").on("click", handleExportNow);
  $("#pbc-schedule-export").on("click", showScheduleModal);
  $("#pbc-save-schedule").on("click", saveScheduledExport);

  // Import functionality
  $("#pbc-preview-import").on("click", handlePreviewImport);
  $("#pbc-execute-import").on("click", handleExecuteImport);
  $("#import_file").on("change", handleFileSelection);
  $("#pbc-download-template").on("click", downloadTemplate);

  // Rollback functionality
  $(".pbc-rollback-btn").on("click", handleRollback);

  // Modal functionality
  $(".pbc-modal-close").on("click", closeModal);
  $(window).on("click", function (e) {
    if ($(e.target).hasClass("pbc-modal")) {
      closeModal();
    }
  });
}

/**
 * Handle export now button click
 */
function handleExportNow() {
  const $button = $(this);
  const $form = $("#pbc-export-form");

  $button
    .prop("disabled", true)
    .text(pbc_admin.strings.saving || "Exporting...");

  const formData = {
    action: "pbc_export_rules",
    nonce: pbc_admin.nonce,
    rule_type: $form.find('[name="rule_type"]').val(),
    country_code: $form.find('[name="country_code"]').val(),
    is_active: $form.find('[name="is_active"]').val(),
    date_from: $form.find('[name="date_from"]').val(),
    date_to: $form.find('[name="date_to"]').val(),
  };

  $.post(pbc_admin.ajax_url, formData)
    .done(function (response) {
      if (response.success) {
        showNotice("success", response.data.message);

        // Add download link
        if (response.data.file_url) {
          const downloadLink = $("<a>")
            .attr("href", response.data.file_url)
            .attr("download", "")
            .text("Download Export File")
            .addClass("button button-primary")
            .css("margin-left", "10px");

          $button.after(downloadLink);

          // Auto-download
          window.location.href = response.data.file_url;
        }

        // Refresh export files list
        refreshExportFilesList();
      } else {
        showNotice("error", response.data || "Export failed");
      }
    })
    .fail(function () {
      showNotice("error", "Export request failed");
    })
    .always(function () {
      $button.prop("disabled", false).text("Export Now");
    });
}

/**
 * Show schedule export modal
 */
function showScheduleModal() {
  $("#pbc-schedule-export-modal").show();
}

/**
 * Save scheduled export
 */
function saveScheduledExport() {
  const $modal = $("#pbc-schedule-export-modal");
  const $form = $("#pbc-schedule-form");
  const $exportForm = $("#pbc-export-form");

  const formData = {
    action: "pbc_schedule_export",
    nonce: pbc_admin.nonce,
    frequency: $form.find('[name="frequency"]').val(),
    email_recipients: $form.find('[name="email_recipients"]').val(),
    retention_days: $form.find('[name="retention_days"]').val(),
    rule_type: $exportForm.find('[name="rule_type"]').val(),
    country_code: $exportForm.find('[name="country_code"]').val(),
    is_active: $exportForm.find('[name="is_active"]').val(),
  };

  $.post(pbc_admin.ajax_url, formData)
    .done(function (response) {
      if (response.success) {
        showNotice("success", response.data);
        $modal.hide();
      } else {
        showNotice("error", response.data || "Failed to schedule export");
      }
    })
    .fail(function () {
      showNotice("error", "Schedule request failed");
    });
}

/**
 * Handle file selection for import
 */
function handleFileSelection() {
  const file = this.files[0];
  if (file) {
    if (file.type !== "text/csv" && !file.name.endsWith(".csv")) {
      showNotice("error", "Please select a CSV file");
      $(this).val("");
      return;
    }

    $("#pbc-preview-import").prop("disabled", false);
  } else {
    $("#pbc-preview-import").prop("disabled", true);
    $("#pbc-execute-import").prop("disabled", true);
  }
}

/**
 * Handle preview import
 */
function handlePreviewImport() {
  const $button = $(this);
  const $form = $("#pbc-import-form");
  const $results = $("#pbc-import-results");

  if (!$form.find("#import_file")[0].files[0]) {
    showNotice("error", "Please select a file to import");
    return;
  }

  $button.prop("disabled", true).text("Previewing...");

  const formData = new FormData();
  formData.append("action", "pbc_preview_import");
  formData.append("nonce", pbc_admin.nonce);
  formData.append("import_file", $form.find("#import_file")[0].files[0]);
  formData.append(
    "conflict_resolution",
    $form.find('[name="conflict_resolution"]').val()
  );

  $.ajax({
    url: pbc_admin.ajax_url,
    type: "POST",
    data: formData,
    processData: false,
    contentType: false,
  })
    .done(function (response) {
      if (response.success) {
        displayImportPreview(response.data);
        $("#pbc-execute-import").prop("disabled", false);
        $results.show();
      } else {
        showNotice("error", response.data || "Preview failed");
      }
    })
    .fail(function () {
      showNotice("error", "Preview request failed");
    })
    .always(function () {
      $button.prop("disabled", false).text("Preview Import");
    });
}

/**
 * Handle execute import
 */
function handleExecuteImport() {
  const $button = $(this);
  const $form = $("#pbc-import-form");

  if (
    !confirm(
      "Are you sure you want to execute this import? This action cannot be undone without a rollback."
    )
  ) {
    return;
  }

  $button.prop("disabled", true).text("Importing...");

  const formData = new FormData();
  formData.append("action", "pbc_import_rules");
  formData.append("nonce", pbc_admin.nonce);
  formData.append("import_file", $form.find("#import_file")[0].files[0]);
  formData.append(
    "conflict_resolution",
    $form.find('[name="conflict_resolution"]').val()
  );

  if ($form.find("#ignore_errors").is(":checked")) {
    formData.append("ignore_errors", "1");
  }

  if ($form.find("#create_rollback").is(":checked")) {
    formData.append("create_rollback", "1");
  }

  $.ajax({
    url: pbc_admin.ajax_url,
    type: "POST",
    data: formData,
    processData: false,
    contentType: false,
  })
    .done(function (response) {
      if (response.success) {
        displayImportResults(response.data);
        showNotice("success", response.data.message);

        // Reset form
        $form[0].reset();
        $("#pbc-preview-import").prop("disabled", true);
        $("#pbc-execute-import").prop("disabled", true);

        // Refresh rollback points if rollback was created
        if (response.data.rollback_id) {
          refreshRollbackPoints();
        }
      } else {
        showNotice("error", response.data || "Import failed");
      }
    })
    .fail(function () {
      showNotice("error", "Import request failed");
    })
    .always(function () {
      $button.prop("disabled", false).text("Execute Import");
    });
}

/**
 * Display import preview results
 */
function displayImportPreview(data) {
  const $content = $("#pbc-import-results-content");

  let html = '<div class="pbc-preview-summary">';
  html += "<h4>Import Preview Summary</h4>";
  html += "<ul>";
  html += "<li>Total Rows: " + data.total_rows + "</li>";
  html += "<li>Valid Rows: " + data.valid_rows + "</li>";
  html += "<li>Error Rows: " + data.error_rows + "</li>";
  html += "<li>Conflicts: " + data.conflicts + "</li>";
  html += "</ul>";
  html += "</div>";

  if (data.validation_errors && data.validation_errors.length > 0) {
    html += '<div class="pbc-validation-errors">';
    html += "<h4>Validation Errors</h4>";
    html += '<table class="wp-list-table widefat fixed striped">';
    html += "<thead><tr><th>Row</th><th>Errors</th></tr></thead>";
    html += "<tbody>";

    data.validation_errors.forEach(function (error) {
      html += "<tr>";
      html += "<td>" + error.row + "</td>";
      html += "<td>" + error.errors.join(", ") + "</td>";
      html += "</tr>";
    });

    html += "</tbody></table>";
    html += "</div>";
  }

  if (data.conflict_data && data.conflict_data.length > 0) {
    html += '<div class="pbc-conflicts">';
    html += "<h4>Conflicts Found</h4>";
    html += '<table class="wp-list-table widefat fixed striped">';
    html +=
      "<thead><tr><th>Row</th><th>Rule Type</th><th>Country</th><th>Conflict</th></tr></thead>";
    html += "<tbody>";

    data.conflict_data.forEach(function (conflict) {
      html += "<tr>";
      html += "<td>" + conflict.row + "</td>";
      html += "<td>" + conflict.data.rule_type + "</td>";
      html += "<td>" + conflict.data.country_code + "</td>";
      html += "<td>Existing rule found</td>";
      html += "</tr>";
    });

    html += "</tbody></table>";
    html += "</div>";
  }

  $content.html(html);
}

/**
 * Display import results
 */
function displayImportResults(data) {
  const $content = $("#pbc-import-results-content");

  let html = '<div class="pbc-import-summary">';
  html += "<h4>Import Results</h4>";
  html += "<ul>";
  html += "<li>Total Rows: " + data.total_rows + "</li>";
  html += "<li>Created: " + data.created + "</li>";
  html += "<li>Updated: " + data.updated + "</li>";
  html += "<li>Skipped: " + data.skipped + "</li>";
  html += "<li>Errors: " + data.errors + "</li>";
  html += "</ul>";
  html += "</div>";

  if (data.import_errors && data.import_errors.length > 0) {
    html += '<div class="pbc-import-errors">';
    html += "<h4>Import Errors</h4>";
    html += '<table class="wp-list-table widefat fixed striped">';
    html += "<thead><tr><th>Row</th><th>Errors</th></tr></thead>";
    html += "<tbody>";

    data.import_errors.forEach(function (error) {
      html += "<tr>";
      html += "<td>" + error.row + "</td>";
      html += "<td>" + error.errors.join(", ") + "</td>";
      html += "</tr>";
    });

    html += "</tbody></table>";
    html += "</div>";
  }

  $content.html(html);
}

/**
 * Handle rollback button click
 */
function handleRollback() {
  const rollbackId = $(this).data("rollback-id");

  if (
    !confirm(
      "Are you sure you want to rollback to this point? This will replace all current pricing rules."
    )
  ) {
    return;
  }

  const $button = $(this);
  $button.prop("disabled", true).text("Rolling back...");

  $.post(pbc_admin.ajax_url, {
    action: "pbc_rollback_import",
    nonce: pbc_admin.nonce,
    rollback_id: rollbackId,
  })
    .done(function (response) {
      if (response.success) {
        showNotice("success", response.data.message);
        // Refresh the page to show updated data
        setTimeout(function () {
          location.reload();
        }, 2000);
      } else {
        showNotice("error", response.data || "Rollback failed");
      }
    })
    .fail(function () {
      showNotice("error", "Rollback request failed");
    })
    .always(function () {
      $button.prop("disabled", false).text("Rollback");
    });
}

/**
 * Download CSV template
 */
function downloadTemplate() {
  const csvContent =
    "Rule Type,Object ID,Object Name,Country Code,Country Name,Adjustment Type,Adjustment Value,Is Active\n" +
    "product,123,Sample Product,US,United States,percentage,-10,Yes\n" +
    "category,456,Sample Category,CA,Canada,fixed,5,Yes\n" +
    "global,,Global Rule,GB,United Kingdom,percentage,15,Yes";

  const blob = new Blob([csvContent], { type: "text/csv" });
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = "pbc-import-template.csv";
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  window.URL.revokeObjectURL(url);
}

/**
 * Refresh export files list
 */
function refreshExportFilesList() {
  $.post(pbc_admin.ajax_url, {
    action: "pbc_get_export_files",
    nonce: pbc_admin.nonce,
  }).done(function (response) {
    if (response.success) {
      // Update export files table
      // This would require updating the table HTML
      location.reload(); // Simple approach for now
    }
  });
}

/**
 * Refresh rollback points
 */
function refreshRollbackPoints() {
  $.post(pbc_admin.ajax_url, {
    action: "pbc_get_rollback_points",
    nonce: pbc_admin.nonce,
  }).done(function (response) {
    if (response.success) {
      // Update rollback points table
      // This would require updating the table HTML
      location.reload(); // Simple approach for now
    }
  });
}

/**
 * Close modal
 */
function closeModal() {
  $(".pbc-modal").hide();
}

/**
 * Show notice message
 */
function showNotice(type, message) {
  const $notice = $(
    '<div class="notice notice-' +
      type +
      ' is-dismissible"><p>' +
      message +
      "</p></div>"
  );
  $(".wrap h1").after($notice);

  // Auto-dismiss after 5 seconds
  setTimeout(function () {
    $notice.fadeOut();
  }, 5000);
}

// Initialize import/export when document is ready
$(document).ready(function () {
  if ($(".pbc-import-export-container").length > 0) {
    initImportExport();
  }
});
  /**
   * Enhanced user detection test
   */
  testUserDetection: function (userId) {
    var self = this;
    var $button = $("#pbc_test_user_detection");
    var $results = $("#pbc_test_results");
    var $output = $("#pbc_test_output");

    $button.addClass("loading").prop("disabled", true);
    $results.removeClass("show");
    $output.empty();

    $.ajax({
      url: pbc_admin.ajax_url,
      type: "POST",
      data: {
        action: "pbc_test_user_detection",
        user_id: userId,
        nonce: pbc_admin.nonce,
      },
      success: function (response) {
        if (response.success) {
          var data = response.data;
          var resultHtml = '<div class="pbc-test-success">';
          resultHtml += '<h5>User Detection Results</h5>';
          resultHtml += '<div class="test-result-grid">';
          resultHtml += '<div class="result-item"><strong>User ID:</strong> ' + userId + '</div>';
          
          if (data.results && data.results.length > 0) {
            data.results.forEach(function(result) {
              resultHtml += '<div class="result-item">';
              resultHtml += '<strong>' + result.method + ':</strong> ';
              if (result.country_code) {
                resultHtml += result.country_name + ' (' + result.country_code + ')';
              } else {
                resultHtml += 'Not available';
              }
              resultHtml += '</div>';
            });
          }
          
          resultHtml += '</div></div>';
          $output.html(resultHtml);
        } else {
          $output.html(
            '<div class="pbc-test-error">' +
              '<h5>Detection Failed</h5>' +
              '<p>' + (response.data || "User not found or no detection data available") + '</p>' +
              '</div>'
          );
        }
        
        $results.addClass("show");
        self.scrollToResults();
      },
      error: function (xhr, status, error) {
        $output.html(
          '<div class="pbc-test-error">' +
            '<h5>Network Error</h5>' +
            '<p>Failed to test user detection. Please try again.</p>' +
            '<small>Error: ' + error + '</small>' +
            '</div>'
        );
        $results.addClass("show");
        self.scrollToResults();
      },
      complete: function () {
        $button.removeClass("loading").prop("disabled", false);
      },
    });
  },

  /**
   * Scroll to test results
   */
  scrollToResults: function() {
    var $results = $("#pbc_test_results");
    if ($results.length) {
      $('html, body').animate({
        scrollTop: $results.offset().top - 100
      }, 500);
    }
  },

  /**
   * Initialize service-specific settings
   */
  initServiceSettings: function() {
    var self = this;
    
    $("#pbc_ip_detection_service").on("change", function() {
      var service = $(this).val();
      self.toggleServiceSettings(service);
    });
    
    // Initialize on page load
    this.toggleServiceSettings($("#pbc_ip_detection_service").val());
  },

  /**
   * Toggle service-specific settings
   */
  toggleServiceSettings: function(service) {
    $(".pbc-service-settings").removeClass("active").slideUp(200);
    
    var $serviceSettings = $("#pbc-service-" + service);
    if ($serviceSettings.length) {
      setTimeout(function() {
        $serviceSettings.addClass("active").slideDown(300);
      }, 250);
    }
  },

  /**
   * Initialize accuracy slider
   */
  initAccuracySlider: function() {
    var $slider = $("#pbc_detection_accuracy_threshold");
    var $value = $(".pbc-accuracy-value");
    
    if ($slider.length && $value.length) {
      $slider.on("input", function() {
        var value = parseFloat($(this).val());
        $value.text((value * 100).toFixed(0) + "%");
        
        // Update color based on value
        if (value >= 0.8) {
          $value.css("color", "#28a745");
        } else if (value >= 0.6) {
          $value.css("color", "#ffc107");
        } else {
          $value.css("color", "#dc3545");
        }
      });
      
      // Initialize display
      $slider.trigger("input");
    }
  },

  /**
   * Initialize form validation
   */
  initFormValidation: function() {
    var self = this;
    
    $("#pbc-country-detection-form").on("submit", function(e) {
      var isValid = self.validateForm();
      if (!isValid) {
        e.preventDefault();
        self.showNotification("Please fix the validation errors before saving.", "error");
      }
    });
  },

  /**
   * Validate form fields
   */
  validateForm: function() {
    var isValid = true;
    
    // Validate cache duration
    var cacheDuration = parseInt($("#pbc_cache_duration").val());
    if (isNaN(cacheDuration) || cacheDuration < 300 || cacheDuration > 86400) {
      this.showValidationError("#pbc_cache_duration", "Cache duration must be between 300 and 86400 seconds.");
      isValid = false;
    }
    
    // Validate accuracy threshold
    var accuracy = parseFloat($("#pbc_detection_accuracy_threshold").val());
    if (isNaN(accuracy) || accuracy < 0.1 || accuracy > 1.0) {
      this.showValidationError("#pbc_detection_accuracy_threshold", "Accuracy threshold must be between 0.1 and 1.0.");
      isValid = false;
    }
    
    return isValid;
  },

  /**
   * Initialize auto-save functionality
   */
  initAutoSave: function() {
    var self = this;
    var autoSaveTimeout;
    
    // Auto-save on form changes (debounced)
    $("#pbc-country-detection-form").on("change", "input, select", function() {
      clearTimeout(autoSaveTimeout);
      autoSaveTimeout = setTimeout(function() {
        self.autoSave();
      }, 2000);
    });
  },

  /**
   * Auto-save form data
   */
  autoSave: function() {
    var self = this;
    var formData = $("#pbc-country-detection-form").serialize();
    
    $.ajax({
      url: pbc_admin.ajax_url,
      type: "POST",
      data: formData + "&action=pbc_auto_save_detection_settings&nonce=" + pbc_admin.nonce,
      success: function(response) {
        if (response.success) {
          self.showNotification("Settings auto-saved", "success", 2000);
        }
      },
      error: function() {
        // Silently fail for auto-save
      }
    });
  },

  /**
   * Add loading states to buttons
   */
  addLoadingStates: function() {
    // Add loading state to save button
    $("#pbc-country-detection-form").on("submit", function() {
      var $submitButton = $(this).find('input[type="submit"]');
      $submitButton.addClass("loading");
    });
  },

  /**
   * Initialize tooltips
   */
  initTooltips: function() {
    // Add tooltips to complex settings
    $('[data-tooltip]').each(function() {
      var $element = $(this);
      var tooltip = $element.data('tooltip');
      
      $element.on('mouseenter', function() {
        var $tooltip = $('<div class="pbc-tooltip-popup">' + tooltip + '</div>');
        $('body').append($tooltip);
        
        var offset = $element.offset();
        $tooltip.css({
          top: offset.top - $tooltip.outerHeight() - 10,
          left: offset.left + ($element.outerWidth() / 2) - ($tooltip.outerWidth() / 2)
        }).fadeIn(200);
      });
      
      $element.on('mouseleave', function() {
        $('.pbc-tooltip-popup').fadeOut(200, function() {
          $(this).remove();
        });
      });
    });
  },

  /**
   * Show notification message
   */
  showNotification: function(message, type, duration) {
    type = type || 'info';
    duration = duration || 5000;
    
    var $notification = $('<div class="pbc-notification pbc-notification-' + type + '">' + message + '</div>');
    $('body').append($notification);
    
    $notification.fadeIn(300);
    
    setTimeout(function() {
      $notification.fadeOut(300, function() {
        $(this).remove();
      });
    }, duration);
  },

  /**
   * Show save notification
   */
  showSaveNotification: function(message) {
    this.showNotification(message, 'success', 3000);
  },

  /**
   * Initialize settings actions (existing method enhanced)
   */
  initSettingsActions: function() {
    var self = this;
    
    // Enhanced save settings
    $("#pbc-save-detection-settings").on("click", function(e) {
      e.preventDefault();
      self.saveDetectionSettings();
    });
    
    // Reset to defaults
    $("#pbc-reset-detection-settings").on("click", function(e) {
      e.preventDefault();
      if (confirm("Are you sure you want to reset all detection settings to defaults?")) {
        self.resetDetectionSettings();
      }
    });
  },

  /**
   * Save detection settings with enhanced feedback
   */
  saveDetectionSettings: function() {
    var self = this;
    var $button = $("#pbc-save-detection-settings");
    var formData = $("#pbc-country-detection-form").serialize();
    
    if (!this.validateForm()) {
      return;
    }
    
    $button.addClass("loading").prop("disabled", true);
    
    $.ajax({
      url: pbc_admin.ajax_url,
      type: "POST",
      data: formData + "&action=pbc_save_detection_settings&nonce=" + pbc_admin.nonce,
      success: function(response) {
        if (response.success) {
          self.showNotification("Detection settings saved successfully!", "success");
          
          // Update any dynamic elements
          self.updateMethodIndicators();
        } else {
          self.showNotification(response.data || "Failed to save settings", "error");
        }
      },
      error: function() {
        self.showNotification("Network error while saving settings", "error");
      },
      complete: function() {
        $button.removeClass("loading").prop("disabled", false);
      }
    });
  },

  /**
   * Initialize log viewer (existing method enhanced)
   */
  initLogViewer: function() {
    var self = this;
    
    // View detection logs
    $("#pbc_view_detection_logs").on("click", function(e) {
      e.preventDefault();
      self.viewDetectionLogs();
    });
    
    // Clear logs
    $("#pbc_clear_logs").on("click", function(e) {
      e.preventDefault();
      if (confirm("Are you sure you want to clear all detection logs?")) {
        self.clearDetectionLogs();
      }
    });
    
    // Auto-refresh logs if viewer is open
    if ($("#pbc-logs-container").is(":visible")) {
      setInterval(function() {
        self.refreshLogs();
      }, 30000); // Refresh every 30 seconds
    }
  },

  /**
   * View detection logs with enhanced display
   */
  viewDetectionLogs: function() {
    var self = this;
    var $button = $("#pbc_view_detection_logs");
    var $container = $("#pbc-logs-container");
    
    $button.addClass("loading").prop("disabled", true);
    
    $.ajax({
      url: pbc_admin.ajax_url,
      type: "POST",
      data: {
        action: "pbc_view_detection_logs",
        nonce: pbc_admin.nonce
      },
      success: function(response) {
        if (response.success) {
          $container.html(response.data.html).slideDown(300);
          self.showNotification("Logs loaded successfully", "success", 2000);
        } else {
          self.showNotification(response.data || "Failed to load logs", "error");
        }
      },
      error: function() {
        self.showNotification("Network error while loading logs", "error");
      },
      complete: function() {
        $button.removeClass("loading").prop("disabled", false);
      }
    });
  },

  /**
   * Refresh logs silently
   */
  refreshLogs: function() {
    var $container = $("#pbc-logs-container");
    if (!$container.is(":visible")) return;
    
    $.ajax({
      url: pbc_admin.ajax_url,
      type: "POST",
      data: {
        action: "pbc_view_detection_logs",
        nonce: pbc_admin.nonce
      },
      success: function(response) {
        if (response.success) {
          $container.html(response.data.html);
        }
      }
    });
  }
};

// Make PBC_CountryDetection globally available
window.PBC_CountryDetection = PBC_CountryDetection;