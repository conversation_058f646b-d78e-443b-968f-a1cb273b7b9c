<?php
/**
 * Simple HPOS compatibility test without WordPress dependency
 */

echo "Price by Country - WooCommerce Compatibility Test\n";
echo "================================================\n\n";

// Test 1: Check PHP version
echo "1. PHP Version Check:\n";
echo "   Current PHP version: " . PHP_VERSION . "\n";
echo "   Required PHP version: 7.4+\n";
if (version_compare(PHP_VERSION, '7.4', '>=')) {
    echo "   ✓ PHP version is compatible\n";
} else {
    echo "   ✗ PHP version is too old\n";
}

// Test 2: Check if plugin files exist
echo "\n2. Plugin Files Check:\n";
$required_files = array(
    'price-by-country.php',
    'includes/class-pbc-core.php',
    'includes/class-pbc-compatibility-checker.php',
    'includes/class-pbc-pricing-engine.php',
    'includes/class-pbc-country-detector.php',
    'includes/class-pbc-admin.php',
    'includes/class-pbc-api.php',
    'includes/class-pbc-hooks.php',
    'includes/class-pbc-database.php'
);

$missing_files = array();
foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "   ✓ {$file}\n";
    } else {
        echo "   ✗ {$file} (missing)\n";
        $missing_files[] = $file;
    }
}

// Test 3: Check plugin header
echo "\n3. Plugin Header Check:\n";
if (file_exists('price-by-country.php')) {
    $plugin_content = file_get_contents('price-by-country.php');
    
    // Check for required headers
    $headers = array(
        'Plugin Name' => 'Plugin Name:',
        'Version' => 'Version:',
        'WC requires at least' => 'WC requires at least:',
        'WC tested up to' => 'WC tested up to:',
        'Requires PHP' => 'Requires PHP:'
    );
    
    foreach ($headers as $name => $pattern) {
        if (strpos($plugin_content, $pattern) !== false) {
            echo "   ✓ {$name} header found\n";
        } else {
            echo "   ✗ {$name} header missing\n";
        }
    }
    
    // Check for HPOS compatibility declaration
    if (strpos($plugin_content, 'before_woocommerce_init') !== false) {
        echo "   ✓ HPOS compatibility declaration found\n";
    } else {
        echo "   ✗ HPOS compatibility declaration missing\n";
    }
} else {
    echo "   ✗ Main plugin file not found\n";
}

// Test 4: Check class structure
echo "\n4. Class Structure Check:\n";
if (file_exists('includes/class-pbc-compatibility-checker.php')) {
    $checker_content = file_get_contents('includes/class-pbc-compatibility-checker.php');
    
    $methods = array(
        'test_wordpress_compatibility',
        'test_woocommerce_compatibility', 
        'test_php_compatibility',
        'test_hpos_compatibility',
        'is_compatible',
        'get_compatibility_issues'
    );
    
    foreach ($methods as $method) {
        if (strpos($checker_content, "function {$method}") !== false) {
            echo "   ✓ {$method} method found\n";
        } else {
            echo "   ✗ {$method} method missing\n";
        }
    }
} else {
    echo "   ✗ Compatibility checker class not found\n";
}

// Test 5: Check constants and version requirements
echo "\n5. Version Requirements Check:\n";
if (file_exists('price-by-country.php')) {
    $plugin_content = file_get_contents('price-by-country.php');
    
    // Extract version requirements
    preg_match('/\* Requires at least: (.+)/', $plugin_content, $wp_matches);
    preg_match('/\* Requires PHP: (.+)/', $plugin_content, $php_matches);
    preg_match('/\* WC requires at least: (.+)/', $plugin_content, $wc_matches);
    preg_match('/\* WC tested up to: (.+)/', $plugin_content, $wc_tested_matches);
    
    if (!empty($wp_matches[1])) {
        echo "   WordPress requirement: {$wp_matches[1]}\n";
    }
    if (!empty($php_matches[1])) {
        echo "   PHP requirement: {$php_matches[1]}\n";
    }
    if (!empty($wc_matches[1])) {
        echo "   WooCommerce requirement: {$wc_matches[1]}\n";
    }
    if (!empty($wc_tested_matches[1])) {
        echo "   WooCommerce tested up to: {$wc_tested_matches[1]}\n";
    }
}

// Test 6: Check for activation/deactivation hooks
echo "\n6. Plugin Hooks Check:\n";
if (file_exists('price-by-country.php')) {
    $plugin_content = file_get_contents('price-by-country.php');
    
    $hooks = array(
        'register_activation_hook',
        'register_deactivation_hook', 
        'register_uninstall_hook'
    );
    
    foreach ($hooks as $hook) {
        if (strpos($plugin_content, $hook) !== false) {
            echo "   ✓ {$hook} found\n";
        } else {
            echo "   ✗ {$hook} missing\n";
        }
    }
}

// Summary
echo "\n================================================\n";
echo "COMPATIBILITY TEST SUMMARY\n";
echo "================================================\n";

$issues = 0;
if (version_compare(PHP_VERSION, '7.4', '<')) {
    $issues++;
}
if (!empty($missing_files)) {
    $issues += count($missing_files);
}

if ($issues === 0) {
    echo "✓ All basic compatibility checks passed!\n";
    echo "✓ Plugin structure is correct\n";
    echo "✓ Required files are present\n";
    echo "✓ PHP version is compatible\n";
    echo "✓ HPOS compatibility is declared\n";
    echo "\nThe plugin appears to be ready for WooCommerce integration.\n";
} else {
    echo "✗ Found {$issues} compatibility issues\n";
    echo "Please fix the issues above before proceeding.\n";
}

echo "\nNote: This test only checks basic file structure and PHP compatibility.\n";
echo "Full WooCommerce compatibility testing requires a WordPress environment.\n";