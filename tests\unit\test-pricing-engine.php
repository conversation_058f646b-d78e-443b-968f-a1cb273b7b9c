<?php
/**
 * Unit tests for PBC_Pricing_Engine class
 *
 * @package PriceByCountry
 */

class Test_PBC_Pricing_Engine extends WP_UnitTestCase {

    /**
     * Pricing engine instance
     *
     * @var PBC_Pricing_Engine
     */
    private $pricing_engine;

    /**
     * Mock database instance
     *
     * @var PHPUnit\Framework\MockObject\MockObject
     */
    private $mock_database;

    /**
     * Mock country detector instance
     *
     * @var PHPUnit\Framework\MockObject\MockObject
     */
    private $mock_country_detector;

    /**
     * Test product ID
     *
     * @var int
     */
    private $test_product_id;

    /**
     * Set up test environment
     */
    public function setUp(): void {
        parent::setUp();

        // Create mock dependencies
        $this->mock_database = $this->createMock(PBC_Database::class);
        $this->mock_country_detector = $this->createMock(PBC_Country_Detector::class);

        // Create pricing engine instance with mocked dependencies
        $this->pricing_engine = new PBC_Pricing_Engine($this->mock_database, $this->mock_country_detector);

        // Create test product
        $this->test_product_id = $this->factory->post->create([
            'post_type' => 'product',
            'post_status' => 'publish'
        ]);

        // Set product regular price
        update_post_meta($this->test_product_id, '_regular_price', '100.00');
        update_post_meta($this->test_product_id, '_price', '100.00');
    }

    /**
     * Test price adjustment with no rules (should return original price)
     */
    public function test_get_price_adjustment_no_rules() {
        // Mock country detector to return US
        $this->mock_country_detector->method('detect_country')->willReturn('US');
        $this->mock_country_detector->method('is_valid_country_code')->willReturn(true);

        // Mock database to return no rules
        $this->mock_database->method('get_pricing_rule_by_criteria')->willReturn(null);

        $result = $this->pricing_engine->get_price_adjustment($this->test_product_id, 'US', 100.00);

        $this->assertEquals(100.00, $result['original_price']);
        $this->assertEquals(100.00, $result['adjusted_price']);
        $this->assertEquals(0, $result['adjustment_amount']);
        $this->assertEquals('none', $result['adjustment_type']);
        $this->assertEquals('US', $result['country_code']);
        $this->assertEquals('none', $result['rule_source']);
    }

    /**
     * Test price adjustment with fixed amount rule
     */
    public function test_get_price_adjustment_fixed_amount() {
        // Mock country detector
        $this->mock_country_detector->method('detect_country')->willReturn('CA');
        $this->mock_country_detector->method('is_valid_country_code')->willReturn(true);

        // Create mock rule for fixed discount
        $mock_rule = (object) [
            'id' => 1,
            'rule_type' => 'product',
            'object_id' => $this->test_product_id,
            'country_code' => 'CA',
            'adjustment_type' => 'fixed',
            'adjustment_value' => -10.00,
            'is_active' => 1
        ];

        $this->mock_database->method('get_pricing_rule_by_criteria')->willReturn($mock_rule);

        $result = $this->pricing_engine->get_price_adjustment($this->test_product_id, 'CA', 100.00);

        $this->assertEquals(100.00, $result['original_price']);
        $this->assertEquals(90.00, $result['adjusted_price']);
        $this->assertEquals(-10.00, $result['adjustment_amount']);
        $this->assertEquals('fixed', $result['adjustment_type']);
        $this->assertEquals('CA', $result['country_code']);
        $this->assertEquals('product', $result['rule_source']);
    }

    /**
     * Test price adjustment with percentage rule
     */
    public function test_get_price_adjustment_percentage() {
        // Mock country detector
        $this->mock_country_detector->method('detect_country')->willReturn('EU');
        $this->mock_country_detector->method('is_valid_country_code')->willReturn(true);

        // Create mock rule for percentage increase
        $mock_rule = (object) [
            'id' => 2,
            'rule_type' => 'product',
            'object_id' => $this->test_product_id,
            'country_code' => 'EU',
            'adjustment_type' => 'percentage',
            'adjustment_value' => 20.00,
            'is_active' => 1
        ];

        $this->mock_database->method('get_pricing_rule_by_criteria')->willReturn($mock_rule);

        $result = $this->pricing_engine->get_price_adjustment($this->test_product_id, 'EU', 100.00);

        $this->assertEquals(100.00, $result['original_price']);
        $this->assertEquals(120.00, $result['adjusted_price']);
        $this->assertEquals(20.00, $result['adjustment_amount']);
        $this->assertEquals('percentage', $result['adjustment_type']);
        $this->assertEquals('EU', $result['country_code']);
        $this->assertEquals('product', $result['rule_source']);
    }

    /**
     * Test rule hierarchy (product > category > global)
     */
    public function test_rule_hierarchy_product_priority() {
        // Mock country detector
        $this->mock_country_detector->method('detect_country')->willReturn('UK');
        $this->mock_country_detector->method('is_valid_country_code')->willReturn(true);

        // Create mock product rule (highest priority)
        $product_rule = (object) [
            'id' => 1,
            'rule_type' => 'product',
            'object_id' => $this->test_product_id,
            'country_code' => 'UK',
            'adjustment_type' => 'fixed',
            'adjustment_value' => -5.00,
            'is_active' => 1
        ];

        // Mock database to return product rule first
        $this->mock_database->method('get_pricing_rule_by_criteria')
            ->willReturnMap([
                ['product', $this->test_product_id, 'UK', $product_rule],
                ['category', 1, 'UK', null],
                ['global', null, 'UK', null]
            ]);

        $result = $this->pricing_engine->get_price_adjustment($this->test_product_id, 'UK', 100.00);

        $this->assertEquals('product', $result['rule_source']);
        $this->assertEquals(95.00, $result['adjusted_price']);
    }

    /**
     * Test rule hierarchy fallback to category
     */
    public function test_rule_hierarchy_category_fallback() {
        // Mock country detector
        $this->mock_country_detector->method('detect_country')->willReturn('AU');
        $this->mock_country_detector->method('is_valid_country_code')->willReturn(true);

        // Create mock category rule
        $category_rule = (object) [
            'id' => 2,
            'rule_type' => 'category',
            'object_id' => 1,
            'country_code' => 'AU',
            'adjustment_type' => 'percentage',
            'adjustment_value' => 15.00,
            'is_active' => 1
        ];

        // Mock database to return no product rule, but category rule
        $this->mock_database->method('get_pricing_rule_by_criteria')
            ->willReturnMap([
                ['product', $this->test_product_id, 'AU', null],
                ['category', 1, 'AU', $category_rule],
                ['global', null, 'AU', null]
            ]);

        // Mock category retrieval
        $this->mock_wp_get_post_terms([1]);

        $result = $this->pricing_engine->get_price_adjustment($this->test_product_id, 'AU', 100.00);

        $this->assertEquals('category', $result['rule_source']);
        $this->assertEquals(115.00, $result['adjusted_price']);
    }

    /**
     * Test rule hierarchy fallback to global
     */
    public function test_rule_hierarchy_global_fallback() {
        // Mock country detector
        $this->mock_country_detector->method('detect_country')->willReturn('JP');
        $this->mock_country_detector->method('is_valid_country_code')->willReturn(true);

        // Create mock global rule
        $global_rule = (object) [
            'id' => 3,
            'rule_type' => 'global',
            'object_id' => null,
            'country_code' => 'JP',
            'adjustment_type' => 'fixed',
            'adjustment_value' => 25.00,
            'is_active' => 1
        ];

        // Mock database to return no product or category rules, but global rule
        $this->mock_database->method('get_pricing_rule_by_criteria')
            ->willReturnMap([
                ['product', $this->test_product_id, 'JP', null],
                ['category', 1, 'JP', null],
                ['global', null, 'JP', $global_rule]
            ]);

        // Mock empty categories
        $this->mock_wp_get_post_terms([]);

        $result = $this->pricing_engine->get_price_adjustment($this->test_product_id, 'JP', 100.00);

        $this->assertEquals('global', $result['rule_source']);
        $this->assertEquals(125.00, $result['adjusted_price']);
    }

    /**
     * Test invalid product ID handling
     */
    public function test_invalid_product_id() {
        $result = $this->pricing_engine->get_price_adjustment(0, 'US', 100.00);

        // Should return error result with original price
        $this->assertArrayHasKey('error', $result);
        $this->assertEquals(100.00, $result['adjusted_price']);
    }

    /**
     * Test invalid country code handling
     */
    public function test_invalid_country_code() {
        $this->mock_country_detector->method('is_valid_country_code')->willReturn(false);

        $result = $this->pricing_engine->get_price_adjustment($this->test_product_id, 'INVALID', 100.00);

        $this->assertEquals('none', $result['rule_source']);
        $this->assertEquals(100.00, $result['adjusted_price']);
    }

    /**
     * Test batch price adjustments
     */
    public function test_batch_price_adjustments() {
        $product_ids = [$this->test_product_id, $this->test_product_id + 1];

        // Mock country detector
        $this->mock_country_detector->method('detect_country')->willReturn('US');
        $this->mock_country_detector->method('is_valid_country_code')->willReturn(true);

        // Mock database to return no rules
        $this->mock_database->method('get_pricing_rule_by_criteria')->willReturn(null);

        $results = $this->pricing_engine->get_batch_price_adjustments($product_ids, 'US');

        $this->assertCount(2, $results);
        $this->assertArrayHasKey($this->test_product_id, $results);
        $this->assertArrayHasKey($this->test_product_id + 1, $results);
    }

    /**
     * Test has country pricing check
     */
    public function test_has_country_pricing() {
        // Mock country detector
        $this->mock_country_detector->method('detect_country')->willReturn('CA');

        // Mock rule exists
        $mock_rule = (object) [
            'id' => 1,
            'rule_type' => 'product',
            'adjustment_type' => 'fixed',
            'adjustment_value' => -10.00
        ];

        $this->mock_database->method('get_pricing_rule_by_criteria')->willReturn($mock_rule);

        $has_pricing = $this->pricing_engine->has_country_pricing($this->test_product_id, 'CA');
        $this->assertTrue($has_pricing);

        // Test no pricing
        $this->mock_database->method('get_pricing_rule_by_criteria')->willReturn(null);
        $has_pricing = $this->pricing_engine->has_country_pricing($this->test_product_id, 'US');
        $this->assertFalse($has_pricing);
    }

    /**
     * Test pricing rule validation
     */
    public function test_validate_pricing_rule() {
        // Mock country detector for validation
        $this->mock_country_detector->method('is_valid_country_code')->willReturn(true);

        // Valid rule data
        $valid_rule = [
            'rule_type' => 'product',
            'object_id' => 1,
            'country_code' => 'US',
            'adjustment_type' => 'fixed',
            'adjustment_value' => 10.00
        ];

        $validation = $this->pricing_engine->validate_pricing_rule($valid_rule);
        $this->assertTrue($validation['valid']);
        $this->assertEmpty($validation['errors']);

        // Invalid rule data (missing required field)
        $invalid_rule = [
            'rule_type' => 'product',
            'country_code' => 'US',
            'adjustment_type' => 'fixed'
            // Missing adjustment_value
        ];

        $validation = $this->pricing_engine->validate_pricing_rule($invalid_rule);
        $this->assertFalse($validation['valid']);
        $this->assertNotEmpty($validation['errors']);
    }

    /**
     * Test edge case: negative price after adjustment
     */
    public function test_negative_price_prevention() {
        // Mock country detector
        $this->mock_country_detector->method('detect_country')->willReturn('US');
        $this->mock_country_detector->method('is_valid_country_code')->willReturn(true);

        // Create rule that would make price negative
        $mock_rule = (object) [
            'id' => 1,
            'rule_type' => 'product',
            'object_id' => $this->test_product_id,
            'country_code' => 'US',
            'adjustment_type' => 'fixed',
            'adjustment_value' => -150.00, // More than base price
            'is_active' => 1
        ];

        $this->mock_database->method('get_pricing_rule_by_criteria')->willReturn($mock_rule);

        $result = $this->pricing_engine->get_price_adjustment($this->test_product_id, 'US', 100.00);

        // Price should not go below 0
        $this->assertEquals(0.00, $result['adjusted_price']);
    }

    /**
     * Test extreme percentage adjustments
     */
    public function test_extreme_percentage_adjustments() {
        // Mock country detector
        $this->mock_country_detector->method('detect_country')->willReturn('US');
        $this->mock_country_detector->method('is_valid_country_code')->willReturn(true);

        // Test large percentage increase
        $mock_rule = (object) [
            'id' => 1,
            'rule_type' => 'product',
            'object_id' => $this->test_product_id,
            'country_code' => 'US',
            'adjustment_type' => 'percentage',
            'adjustment_value' => 500.00, // 500% increase
            'is_active' => 1
        ];

        $this->mock_database->method('get_pricing_rule_by_criteria')->willReturn($mock_rule);

        $result = $this->pricing_engine->get_price_adjustment($this->test_product_id, 'US', 100.00);

        $this->assertEquals(600.00, $result['adjusted_price']); // 100 + (100 * 5)
    }

    /**
     * Helper method to mock wp_get_post_terms
     */
    private function mock_wp_get_post_terms($category_ids) {
        // This would typically be done with a WordPress testing framework
        // For now, we'll assume the method exists and returns the expected data
        if (!function_exists('wp_get_post_terms')) {
            function wp_get_post_terms($post_id, $taxonomy, $args = []) {
                return $GLOBALS['mock_category_ids'] ?? [];
            }
        }
        $GLOBALS['mock_category_ids'] = $category_ids;
    }

    /**
     * Clean up after tests
     */
    public function tearDown(): void {
        // Clean up test data
        wp_delete_post($this->test_product_id, true);
        unset($GLOBALS['mock_category_ids']);
        parent::tearDown();
    }
}