<?php
/**
 * Plugin Name: Price by Country for WooCommerce
 * Plugin URI: https://orbitaddons.com/price-by-country-woocommerce
 * Description: Set different prices for products based on customer's country location with support for product, category, and global pricing rules.
 * Version: 1.0.0
 * Author: OrbitAddons
 * Author URI: https://orbitaddons.com
 * Text Domain: price-by-country
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 8.7
 * Woo: 8.7.0
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('PBC_VERSION', '1.0.0');
define('PBC_PLUGIN_FILE', __FILE__);
define('PBC_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('PBC_PLUGIN_URL', plugin_dir_url(__FILE__));
define('PBC_PLUGIN_BASENAME', plugin_basename(__FILE__));

// Check if WooCommerce is active
if (!pbc_is_woocommerce_active()) {
    add_action('admin_notices', 'pbc_woocommerce_missing_notice');
    return;
}

// Check minimum requirements before loading
if (!pbc_check_minimum_requirements()) {
    add_action('admin_notices', 'pbc_requirements_notice');
    return;
}

/**
 * Check if WooCommerce is active
 *
 * @return bool
 */
function pbc_is_woocommerce_active() {
    // Check if WooCommerce is active in single site
    if (in_array('woocommerce/woocommerce.php', apply_filters('active_plugins', get_option('active_plugins')))) {
        return true;
    }

    // Check if WooCommerce is network active in multisite
    if (is_multisite()) {
        $network_plugins = get_site_option('active_sitewide_plugins');
        if (isset($network_plugins['woocommerce/woocommerce.php'])) {
            return true;
        }
    }

    return false;
}

/**
 * Check minimum requirements
 *
 * @return bool
 */
function pbc_check_minimum_requirements() {
    global $wp_version;

    // Check WordPress version
    if (version_compare($wp_version, '5.0', '<')) {
        return false;
    }

    // Check PHP version
    if (version_compare(PHP_VERSION, '7.4', '<')) {
        return false;
    }

    // Check WooCommerce version if available
    if (defined('WC_VERSION') && version_compare(WC_VERSION, '5.0', '<')) {
        return false;
    }

    return true;
}

/**
 * Display notice if WooCommerce is not active
 */
function pbc_woocommerce_missing_notice() {
    ?>
    <div class="notice notice-error">
        <p>
            <strong><?php _e('Price by Country for WooCommerce', 'price-by-country'); ?></strong><br>
            <?php _e('This plugin requires WooCommerce to be installed and active.', 'price-by-country'); ?>
            <a href="<?php echo admin_url('plugin-install.php?s=woocommerce&tab=search&type=term'); ?>" class="button button-primary">
                <?php _e('Install WooCommerce', 'price-by-country'); ?>
            </a>
        </p>
    </div>
    <?php
}

/**
 * Display notice if minimum requirements are not met
 */
function pbc_requirements_notice() {
    global $wp_version;
    
    $messages = array();
    
    if (version_compare($wp_version, '5.0', '<')) {
        $messages[] = sprintf(__('WordPress %s or higher (current: %s)', 'price-by-country'), '5.0', $wp_version);
    }
    
    if (version_compare(PHP_VERSION, '7.4', '<')) {
        $messages[] = sprintf(__('PHP %s or higher (current: %s)', 'price-by-country'), '7.4', PHP_VERSION);
    }
    
    if (defined('WC_VERSION') && version_compare(WC_VERSION, '5.0', '<')) {
        $messages[] = sprintf(__('WooCommerce %s or higher (current: %s)', 'price-by-country'), '5.0', WC_VERSION);
    }
    
    if (!empty($messages)) {
        ?>
        <div class="notice notice-error">
            <p>
                <strong><?php _e('Price by Country for WooCommerce', 'price-by-country'); ?></strong><br>
                <?php _e('This plugin requires:', 'price-by-country'); ?>
            </p>
            <ul style="list-style: disc; margin-left: 20px;">
                <?php foreach ($messages as $message): ?>
                    <li><?php echo esc_html($message); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php
    }
}

// Include the autoloader
require_once PBC_PLUGIN_DIR . 'includes/class-pbc-autoloader.php';

// Declare HPOS compatibility
add_action('before_woocommerce_init', function() {
    if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
        \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility('custom_order_tables', __FILE__, true);
    }
});

// Initialize the plugin
add_action('plugins_loaded', array('PBC_Core', 'get_instance'), 10);

// Activation hook
register_activation_hook(__FILE__, array('PBC_Core', 'activate'));

// Deactivation hook
register_deactivation_hook(__FILE__, array('PBC_Core', 'deactivate'));

// Uninstall hook
register_uninstall_hook(__FILE__, array('PBC_Core', 'uninstall'));