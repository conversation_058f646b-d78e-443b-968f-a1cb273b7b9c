/**
 * Admin styles for Price by Country plugin
 */

/* Product-level pricing styles */
.pbc-product-pricing {
  border-top: 1px solid #eee;
  padding-top: 12px;
  margin-top: 12px;
}

.pbc-rule-row {
  background: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 10px;
  position: relative;
}

.pbc-rule-fields {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.pbc-country-select {
  min-width: 150px;
  flex: 1;
}

.pbc-adjustment-type {
  min-width: 120px;
}

.pbc-adjustment-value {
  width: 80px;
}

.pbc-active-label {
  display: flex;
  align-items: center;
  gap: 5px;
  white-space: nowrap;
}

.pbc-remove-rule {
  background: #dc3232;
  border-color: #dc3232;
  color: white;
}

.pbc-remove-rule:hover {
  background: #a00;
  border-color: #a00;
}

#pbc-add-product-rule {
  background: #0073aa;
  border-color: #0073aa;
  color: white;
}

#pbc-add-product-rule:hover {
  background: #005a87;
  border-color: #005a87;
}

/* Category-level pricing styles */
.pbc-category-pricing {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  margin: 15px 0;
}

.pbc-category-pricing h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 14px;
  font-weight: 600;
}

.pbc-category-rules {
  margin-bottom: 15px;
}

.pbc-bulk-actions {
  border-top: 1px solid #eee;
  padding-top: 15px;
  margin-top: 15px;
}

.pbc-bulk-actions label {
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
}

/* Global pricing styles */
.pbc-global-settings {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 20px;
  margin: 20px 0;
}

.pbc-settings-section {
  margin-bottom: 30px;
}

.pbc-settings-section:last-child {
  margin-bottom: 0;
}

.pbc-settings-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.pbc-settings-table {
  width: 100%;
  border-collapse: collapse;
}

.pbc-settings-table th,
.pbc-settings-table td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.pbc-settings-table th {
  background: #f9f9f9;
  font-weight: 600;
}

/* Form field styles */
.pbc-field-group {
  margin-bottom: 15px;
}

.pbc-field-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
}

.pbc-field-group .description {
  font-style: italic;
  color: #666;
  font-size: 12px;
  margin-top: 5px;
}

/* Status indicators */
.pbc-status-active {
  color: #46b450;
  font-weight: 600;
}

.pbc-status-inactive {
  color: #dc3232;
  font-weight: 600;
}

/* Loading states */
.pbc-loading {
  opacity: 0.6;
  pointer-events: none;
}

.pbc-loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #0073aa;
  border-radius: 50%;
  animation: pbc-spin 1s linear infinite;
}

@keyframes pbc-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .pbc-rule-fields {
    flex-direction: column;
    align-items: stretch;
  }

  .pbc-country-select,
  .pbc-adjustment-type,
  .pbc-adjustment-value {
    width: 100%;
    min-width: auto;
  }

  .pbc-active-label {
    justify-content: center;
  }
}

/* Error and success messages */
.pbc-message {
  padding: 10px 15px;
  border-radius: 4px;
  margin: 10px 0;
}

.pbc-message.success {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.pbc-message.error {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.pbc-message.warning {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
}

/* Tooltip styles */
.pbc-tooltip {
  position: relative;
  display: inline-block;
  cursor: help;
}

.pbc-tooltip .pbc-tooltip-text {
  visibility: hidden;
  width: 200px;
  background-color: #495057;
  color: #fff;
  text-align: center;
  border-radius: 4px;
  padding: 8px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -100px;
  font-size: 12px;
  line-height: 1.4;
}

.pbc-tooltip:hover .pbc-tooltip-text {
  visibility: visible;
}

.pbc-tooltip .pbc-tooltip-text::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #333 transparent transparent transparent;
}
/* Dashboard styles */
.pbc-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.pbc-stat-card {
  background: #fff;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 4px;
  text-align: center;
}

.pbc-stat-card h3 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.pbc-stat-card .stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #0073aa;
  margin: 0;
}

.pbc-stat-card .stat-number.active {
  color: #46b450;
}

.pbc-stat-card .stat-number.warning {
  color: #f56e28;
}

/* Filters section */
.pbc-filters-section {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 20px;
  margin: 20px 0;
}

.pbc-filters-form .pbc-filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: end;
}

.pbc-filter-group {
  display: flex;
  flex-direction: column;
  min-width: 120px;
}

.pbc-filter-group label {
  font-weight: 600;
  margin-bottom: 5px;
  font-size: 12px;
  color: #666;
}

.pbc-filter-group input,
.pbc-filter-group select {
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 3px;
}

.pbc-filter-actions {
  display: flex;
  gap: 10px;
}

/* Conflicts notice */
.pbc-conflicts-notice {
  margin: 20px 0;
}

.pbc-conflicts-list {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  margin-top: 10px;
}

.pbc-conflict-item {
  border-bottom: 1px solid #eee;
  padding: 10px 0;
}

.pbc-conflict-item:last-child {
  border-bottom: none;
}

.pbc-conflict-rules {
  margin: 10px 0 0 20px;
  list-style: disc;
}

/* Rules table */
.pbc-rules-table-section {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin: 20px 0;
}

.pbc-table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  background: #f9f9f9;
}

.pbc-table-header h3 {
  margin: 0;
  font-size: 16px;
}

.pbc-bulk-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.pbc-rules-table {
  margin: 0;
}

.pbc-rules-table .column-type {
  width: 80px;
}

.pbc-rules-table .column-object {
  width: 200px;
}

.pbc-rules-table .column-country {
  width: 120px;
}

.pbc-rules-table .column-adjustment {
  width: 100px;
}

.pbc-rules-table .column-status {
  width: 80px;
}

.pbc-rules-table .column-priority {
  width: 80px;
}

.pbc-rules-table .column-updated {
  width: 120px;
}

.pbc-rules-table .column-actions {
  width: 120px;
}

/* Rule type indicators */
.pbc-rule-type-global {
  color: #0073aa;
}

.pbc-rule-type-category {
  color: #f56e28;
}

.pbc-rule-type-product {
  color: #46b450;
}

/* Adjustment type indicators */
.pbc-adjustment-percentage {
  color: #0073aa;
}

.pbc-adjustment-fixed {
  color: #46b450;
}

/* Priority indicators */
.pbc-priority {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: bold;
  color: #fff;
}

.pbc-priority-1 {
  background: #46b450;
}

.pbc-priority-2 {
  background: #f56e28;
}

.pbc-priority-3 {
  background: #0073aa;
}

/* Country flag placeholder */
.pbc-country-flag {
  font-weight: 600;
}

/* No rules message */
.pbc-no-rules {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.pbc-no-rules p {
  margin: 10px 0;
}

/* Pagination */
.pbc-pagination {
  padding: 15px 20px;
  border-top: 1px solid #eee;
  text-align: center;
}

.pbc-pagination .page-numbers {
  display: inline-block;
  padding: 6px 12px;
  margin: 0 2px;
  text-decoration: none;
  border: 1px solid #ddd;
  border-radius: 3px;
  color: #0073aa;
}

.pbc-pagination .page-numbers:hover,
.pbc-pagination .page-numbers.current {
  background: #0073aa;
  color: #fff;
  border-color: #0073aa;
}

/* Quick actions */
.pbc-quick-actions {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 20px;
  margin: 20px 0;
}

.pbc-quick-actions h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
}

.pbc-quick-actions p {
  margin: 0;
}

.pbc-quick-actions .button {
  margin-right: 10px;
  margin-bottom: 5px;
}

/* Modal styles */
.pbc-modal {
  position: fixed;
  z-index: 100000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.pbc-modal-content {
  background-color: #fff;
  margin: 5% auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.pbc-modal-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pbc-modal-header h3 {
  margin: 0;
  font-size: 16px;
}

.pbc-modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pbc-modal-close:hover {
  color: #000;
}

.pbc-modal-body {
  padding: 20px;
}

.pbc-modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #eee;
  text-align: right;
}

.pbc-modal-footer .button {
  margin-left: 10px;
}

/* Form styles in modal */
.pbc-form-row {
  margin-bottom: 15px;
}

.pbc-form-row label {
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
}

.pbc-form-row input,
.pbc-form-row select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 3px;
}

.pbc-form-row input[type="checkbox"] {
  width: auto;
  margin-right: 8px;
}

/* Responsive design for dashboard */
@media (max-width: 1200px) {
  .pbc-filters-form .pbc-filter-row {
    flex-direction: column;
    align-items: stretch;
  }

  .pbc-filter-group {
    min-width: auto;
  }

  .pbc-filter-actions {
    justify-content: center;
    margin-top: 10px;
  }
}

@media (max-width: 768px) {
  .pbc-stats-grid {
    grid-template-columns: 1fr;
  }

  .pbc-table-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .pbc-bulk-actions {
    justify-content: center;
  }

  .pbc-rules-table {
    font-size: 12px;
  }

  .pbc-modal-content {
    width: 95%;
    margin: 10% auto;
  }
}

/* Animation for loading states */
.pbc-fade-in {
  animation: pbc-fadeIn 0.3s ease-in;
}

@keyframes pbc-fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Highlight animations */
.pbc-highlight {
  animation: pbc-highlight 2s ease-out;
}

@keyframes pbc-highlight {
  0% {
    background-color: #fff3cd;
  }
  100% {
    background-color: transparent;
  }
}
/* ========================================
   COUNTRY DETECTION SETTINGS STYLES
   ======================================== */

/* Force our styles on the country detection page */
body.wp-admin .wrap .pbc-detection-settings {
  max-width: 1200px;
  margin: 20px 0;
}

/* Ensure our form styling takes precedence */
body.wp-admin #pbc-country-detection-form .pbc-detection-settings {
  max-width: 1200px;
  margin: 20px 0;
}

/* Main settings container */
body.wp-admin .wrap .pbc-detection-settings .pbc-settings-section,
body.wp-admin #pbc-country-detection-form .pbc-settings-section {
  background: #fff !important;
  border: 1px solid #e1e1e1 !important;
  border-radius: 8px !important;
  padding: 0 !important;
  margin-bottom: 24px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  overflow: hidden !important;
}

body.wp-admin .wrap .pbc-detection-settings .pbc-settings-section h3,
body.wp-admin #pbc-country-detection-form .pbc-settings-section h3 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: #fff !important;
  margin: 0 !important;
  padding: 16px 24px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  border-bottom: none !important;
  display: flex !important;
  align-items: center !important;
  gap: 10px !important;
}

.pbc-detection-settings .pbc-settings-section h3:before {
  content: "";
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: inline-block;
}

/* Form table improvements */
body.wp-admin .pbc-detection-settings .form-table,
body.wp-admin #pbc-country-detection-form .form-table {
  margin: 0 !important;
  background: #fff !important;
}

body.wp-admin .pbc-detection-settings .form-table th,
body.wp-admin #pbc-country-detection-form .form-table th {
  background: #f8f9fa !important;
  border-bottom: 1px solid #e9ecef !important;
  padding: 16px 24px !important;
  font-weight: 600 !important;
  color: #495057 !important;
  width: 200px !important;
  vertical-align: top !important;
}

body.wp-admin .pbc-detection-settings .form-table td,
body.wp-admin #pbc-country-detection-form .form-table td {
  padding: 16px 24px !important;
  border-bottom: 1px solid #e9ecef !important;
  vertical-align: top !important;
}

.pbc-detection-settings .form-table tr:last-child th,
.pbc-detection-settings .form-table tr:last-child td {
  border-bottom: none;
}

/* Enhanced form controls */
.pbc-detection-settings select,
.pbc-detection-settings input[type="text"],
.pbc-detection-settings input[type="number"] {
  border: 2px solid #e9ecef;
  border-radius: 6px;
  padding: 10px 12px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: #fff;
}

.pbc-detection-settings select:focus,
.pbc-detection-settings input[type="text"]:focus,
.pbc-detection-settings input[type="number"]:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  outline: none;
}

.pbc-detection-settings .description {
  color: #6c757d;
  font-size: 13px;
  margin-top: 8px;
  line-height: 1.4;
}

/* Enhanced Priority Controls */
.pbc-priority-list {
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 16px;
  max-width: 500px;
  margin-top: 8px;
}

.pbc-priority-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 8px;
  background: #fff;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: move;
  transition: all 0.2s ease;
  position: relative;
}

.pbc-priority-item:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
  transform: translateY(-1px);
}

.pbc-priority-item:last-child {
  margin-bottom: 0;
}

.pbc-priority-item.moving {
  opacity: 0.7;
  transform: scale(0.98);
  z-index: 1000;
}

.pbc-priority-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border-radius: 50%;
  font-size: 13px;
  font-weight: bold;
  margin-right: 12px;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.pbc-priority-label {
  flex: 1;
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

.pbc-priority-controls {
  display: flex;
  gap: 4px;
}

.pbc-priority-controls .button {
  padding: 6px 10px;
  font-size: 12px;
  line-height: 1;
  min-height: auto;
  border-radius: 4px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  color: #495057;
  transition: all 0.2s ease;
}

.pbc-priority-controls .button:hover:not(:disabled) {
  background: #667eea;
  border-color: #667eea;
  color: #fff;
}

.pbc-priority-controls .button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Drag handle indicator */
.pbc-priority-item:before {
  content: "⋮⋮";
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: #adb5bd;
  font-size: 12px;
  line-height: 1;
  letter-spacing: -2px;
}

/* Enhanced Testing Section */
.pbc-test-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  margin-top: 16px;
}

.pbc-test-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #495057;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.pbc-test-section h4:before {
  content: "🧪";
  font-size: 18px;
}

.pbc-test-controls {
  margin: 20px 0;
}

.pbc-test-input-group {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.pbc-test-input-group label {
  min-width: 140px;
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

.pbc-test-input-group input {
  flex: 1;
  max-width: 200px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.pbc-test-input-group input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  outline: none;
}

.pbc-test-input-group .button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: #fff;
  padding: 10px 16px;
  border-radius: 6px;
  font-weight: 600;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.pbc-test-input-group .button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.4);
}

.pbc-test-input-group .button:disabled {
  opacity: 0.6;
  transform: none;
  box-shadow: none;
  cursor: not-allowed;
}

.pbc-test-results {
  margin-top: 20px;
  padding: 0;
  border: none;
  border-radius: 8px;
  background: transparent;
  display: none;
}

.pbc-test-results.show {
  display: block;
  animation: slideDown 0.3s ease;
}

.pbc-test-results h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.pbc-test-success {
  color: #155724;
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  border: 2px solid #c3e6cb;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(21, 87, 36, 0.1);
}

.pbc-test-success:before {
  content: "✅ ";
  font-size: 16px;
  margin-right: 8px;
}

.pbc-test-error {
  color: #721c24;
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  border: 2px solid #f5c6cb;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(114, 28, 36, 0.1);
}

.pbc-test-error:before {
  content: "❌ ";
  font-size: 16px;
  margin-right: 8px;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Detection chart */
.pbc-detection-chart {
  margin-top: 20px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fff;
}

.pbc-detection-chart h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
}

/* Log viewer styles */
.pbc-logs-container {
  max-height: 400px;
  overflow-y: auto;
}

.pbc-logs-container table {
  font-size: 12px;
}

.pbc-logs-container th,
.pbc-logs-container td {
  padding: 6px 8px;
}

/* Enhanced Service-specific Settings */
.pbc-service-settings {
  display: none;
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px solid #e9ecef;
  border-radius: 8px;
  animation: slideDown 0.3s ease;
}

.pbc-service-settings.active {
  display: block;
}

.pbc-service-settings h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

/* Enhanced Accuracy Threshold Slider */
.pbc-accuracy-slider {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 12px 0;
}

.pbc-accuracy-slider input[type="range"] {
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: #e9ecef;
  outline: none;
  -webkit-appearance: none;
}

.pbc-accuracy-slider input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.pbc-accuracy-slider input[type="range"]::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.pbc-accuracy-value {
  font-weight: bold;
  color: #667eea;
  font-size: 16px;
  min-width: 40px;
  text-align: center;
  background: #fff;
  padding: 4px 8px;
  border-radius: 4px;
  border: 2px solid #e9ecef;
}

/* Enhanced Detection Method Indicators */
.pbc-method-indicator {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  color: #fff;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pbc-method-indicator:before {
  content: "";
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
}

.pbc-method-indicator.ip {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.pbc-method-indicator.billing {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.pbc-method-indicator.shipping {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.pbc-method-indicator.auto {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

/* Enhanced Statistics Dashboard */
.pbc-detection-stats-section {
  background: #fff;
  border: 1px solid #e1e1e1;
  border-radius: 8px;
  padding: 0;
  margin-bottom: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.pbc-detection-stats-section h3 {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: #fff;
  margin: 0;
  padding: 16px 24px;
  font-size: 16px;
  font-weight: 600;
  border-bottom: none;
  display: flex;
  align-items: center;
  gap: 10px;
}

.pbc-detection-stats-section h3:before {
  content: "📊";
  font-size: 18px;
}

.pbc-detection-settings .pbc-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  padding: 24px;
  margin: 0;
}

.pbc-detection-settings .pbc-stat-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.pbc-detection-settings .pbc-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.pbc-detection-settings .pbc-stat-card:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.pbc-detection-settings .pbc-stat-card h4 {
  font-size: 12px;
  margin: 0 0 12px 0;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
}

.pbc-detection-settings .pbc-stat-card .stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #495057;
  margin: 0 0 8px 0;
  line-height: 1;
}

.pbc-detection-settings .pbc-stat-card .stat-number.success {
  color: #28a745;
}

.pbc-detection-settings .pbc-stat-card .stat-number.warning {
  color: #ffc107;
}

.pbc-detection-settings .pbc-stat-card .stat-number.error {
  color: #dc3545;
}

.pbc-detection-settings .pbc-stat-card small {
  color: #6c757d;
  font-size: 12px;
  font-weight: 500;
}

/* Responsive design for detection settings */
@media (max-width: 768px) {
  .pbc-test-input-group {
    flex-direction: column;
    align-items: stretch;
  }

  .pbc-test-input-group label {
    min-width: auto;
    margin-bottom: 5px;
  }

  .pbc-test-input-group input {
    max-width: none;
  }

  .pbc-priority-item {
    flex-wrap: wrap;
  }

  .pbc-priority-controls {
    margin-top: 5px;
    width: 100%;
    justify-content: center;
  }
}

/* Animation for priority reordering */
.pbc-priority-item.moving {
  opacity: 0.7;
  transform: scale(0.98);
  transition: all 0.2s ease;
}

/* Validation styles for detection settings */
.pbc-detection-settings input.error,
.pbc-detection-settings select.error {
  border-color: #dc3232 !important;
  box-shadow: 0 0 2px rgba(220, 50, 50, 0.8) !important;
}

/* Help text styling */
.pbc-help-text {
  font-size: 12px;
  color: #666;
  font-style: italic;
  margin-top: 5px;
}

.pbc-help-text code {
  background: #f1f1f1;
  padding: 2px 4px;
  border-radius: 2px;
  font-family: monospace;
}

/* Enhanced Status Indicators */
.pbc-detection-status {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
  position: relative;
  box-shadow: 0 0 0 2px #fff, 0 0 0 3px currentColor;
}

.pbc-detection-status.active {
  background: #28a745;
  animation: pulse-green 2s infinite;
}

.pbc-detection-status.inactive {
  background: #dc3545;
}

.pbc-detection-status.warning {
  background: #ffc107;
  animation: pulse-yellow 2s infinite;
}

@keyframes pulse-green {
  0% {
    box-shadow: 0 0 0 2px #fff, 0 0 0 3px #28a745,
      0 0 0 6px rgba(40, 167, 69, 0);
  }
  70% {
    box-shadow: 0 0 0 2px #fff, 0 0 0 3px #28a745,
      0 0 0 6px rgba(40, 167, 69, 0.4);
  }
  100% {
    box-shadow: 0 0 0 2px #fff, 0 0 0 3px #28a745,
      0 0 0 6px rgba(40, 167, 69, 0);
  }
}

@keyframes pulse-yellow {
  0% {
    box-shadow: 0 0 0 2px #fff, 0 0 0 3px #ffc107,
      0 0 0 6px rgba(255, 193, 7, 0);
  }
  70% {
    box-shadow: 0 0 0 2px #fff, 0 0 0 3px #ffc107,
      0 0 0 6px rgba(255, 193, 7, 0.4);
  }
  100% {
    box-shadow: 0 0 0 2px #fff, 0 0 0 3px #ffc107,
      0 0 0 6px rgba(255, 193, 7, 0);
  }
}

/* Status text labels */
.pbc-status-label {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  font-weight: 600;
}

.pbc-status-label.active {
  color: #28a745;
}

.pbc-status-label.inactive {
  color: #dc3545;
}

.pbc-status-label.warning {
  color: #ffc107;
}
/* ========================================
   IMPORT/EXPORT STYLES
   ======================================== */

.pbc-import-export-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 30px;
  margin-top: 20px;
}

.pbc-section {
  background: #fff;
  border: 1px solid #ccd0d4;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.pbc-section h2 {
  margin-top: 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

/* Export Section */
.pbc-export-filters {
  margin-bottom: 20px;
}

.pbc-export-filters h3 {
  margin-bottom: 15px;
}

.pbc-filter-row {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.pbc-filter-group {
  display: flex;
  flex-direction: column;
  min-width: 200px;
}

.pbc-filter-group label {
  font-weight: 600;
  margin-bottom: 5px;
}

.pbc-filter-group select,
.pbc-filter-group input {
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 3px;
}

.pbc-export-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.pbc-export-files {
  margin-top: 30px;
}

.pbc-export-files h3 {
  margin-bottom: 15px;
}

/* Import Section */
.pbc-import-instructions {
  background: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
}

.pbc-import-instructions h3 {
  margin-top: 0;
  margin-bottom: 10px;
}

.pbc-import-instructions ul {
  margin-bottom: 15px;
}

.pbc-import-form {
  display: grid;
  gap: 20px;
}

.pbc-import-file {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.pbc-import-file label {
  font-weight: 600;
}

.pbc-import-file input[type="file"] {
  padding: 10px;
  border: 2px dashed #ddd;
  border-radius: 4px;
  background: #fafafa;
}

.pbc-import-options h3 {
  margin-bottom: 15px;
}

.pbc-option-group {
  margin-bottom: 15px;
}

.pbc-option-group label {
  font-weight: 600;
  display: block;
  margin-bottom: 5px;
}

.pbc-option-group select {
  width: 100%;
  max-width: 300px;
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 3px;
}

.pbc-option-group input[type="checkbox"] {
  margin-right: 8px;
}

.pbc-option-group .description {
  font-size: 13px;
  color: #666;
  margin-top: 5px;
}

.pbc-import-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.pbc-import-results {
  margin-top: 30px;
  background: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
}

.pbc-import-results h3 {
  margin-top: 0;
  margin-bottom: 15px;
}

.pbc-preview-summary,
.pbc-import-summary {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
}

.pbc-preview-summary h4,
.pbc-import-summary h4 {
  margin-top: 0;
  margin-bottom: 10px;
}

.pbc-preview-summary ul,
.pbc-import-summary ul {
  margin: 0;
  padding-left: 20px;
}

.pbc-validation-errors,
.pbc-conflicts,
.pbc-import-errors {
  margin-top: 20px;
}

.pbc-validation-errors h4,
.pbc-conflicts h4,
.pbc-import-errors h4 {
  margin-bottom: 10px;
  color: #d63638;
}

/* Rollback Section */
.pbc-rollback-section table {
  margin-top: 15px;
}

.pbc-rollback-btn {
  background: #d63638;
  color: #fff;
  border-color: #d63638;
}

.pbc-rollback-btn:hover {
  background: #b32d2e;
  border-color: #b32d2e;
}

/* Modal Styles */
.pbc-modal {
  display: none;
  position: fixed;
  z-index: 100000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.pbc-modal-content {
  background-color: #fff;
  margin: 5% auto;
  padding: 0;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 90%;
  max-width: 600px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.pbc-modal-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pbc-modal-header h3 {
  margin: 0;
}

.pbc-modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pbc-modal-close:hover {
  color: #000;
}

.pbc-modal-body {
  padding: 20px;
}

.pbc-modal-footer {
  padding: 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.pbc-form-group {
  margin-bottom: 20px;
}

.pbc-form-group label {
  display: block;
  font-weight: 600;
  margin-bottom: 5px;
}

.pbc-form-group input,
.pbc-form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 3px;
}

.pbc-form-group .description {
  font-size: 13px;
  color: #666;
  margin-top: 5px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .pbc-filter-row {
    flex-direction: column;
  }

  .pbc-filter-group {
    min-width: auto;
  }

  .pbc-export-actions,
  .pbc-import-actions {
    flex-direction: column;
  }

  .pbc-modal-content {
    width: 95%;
    margin: 10% auto;
  }
}

/* Notice Styles */
.notice.notice-success {
  border-left-color: #00a32a;
}

.notice.notice-error {
  border-left-color: #d63638;
}

.notice.notice-warning {
  border-left-color: #dba617;
}

.notice.notice-info {
  border-left-color: #72aee6;
}

/* Enhanced Save Button */
.pbc-detection-settings .submit {
  text-align: center;
  padding: 24px;
  background: #fff;
  border: 1px solid #e1e1e1;
  border-radius: 8px;
  margin-top: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.pbc-detection-settings .submit .button-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: #fff;
  padding: 12px 32px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.2s ease;
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
  min-width: 200px;
}

.pbc-detection-settings .submit .button-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(102, 126, 234, 0.4);
}

.pbc-detection-settings .submit .button-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

/* Loading state for buttons */
.pbc-detection-settings .button.loading {
  position: relative;
  color: transparent;
  pointer-events: none;
}

.pbc-detection-settings .button.loading:after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top: 2px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Enhanced Log Viewer */
.pbc-logs-container {
  max-height: 400px;
  overflow-y: auto;
  background: #fff;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  margin-top: 16px;
}

.pbc-logs-container table {
  font-size: 12px;
  width: 100%;
  border-collapse: collapse;
}

.pbc-logs-container th {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 12px 16px;
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #e9ecef;
  position: sticky;
  top: 0;
  z-index: 10;
}

.pbc-logs-container td {
  padding: 10px 16px;
  border-bottom: 1px solid #f8f9fa;
  vertical-align: top;
}

.pbc-logs-container tr:hover {
  background: #f8f9fa;
}

.pbc-logs-container .log-level {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.pbc-logs-container .log-level.info {
  background: #d1ecf1;
  color: #0c5460;
}

.pbc-logs-container .log-level.warning {
  background: #fff3cd;
  color: #856404;
}

.pbc-logs-container .log-level.error {
  background: #f8d7da;
  color: #721c24;
}

/* Responsive Design Improvements */
@media (max-width: 1024px) {
  .pbc-detection-settings .pbc-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
  }

  .pbc-priority-list {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .pbc-detection-settings .form-table th {
    width: auto;
    display: block;
    padding: 12px 16px 8px;
    border-bottom: none;
  }

  .pbc-detection-settings .form-table td {
    display: block;
    padding: 0 16px 16px;
  }

  .pbc-test-input-group {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .pbc-test-input-group label {
    min-width: auto;
    margin-bottom: 4px;
  }

  .pbc-test-input-group input {
    max-width: none;
  }

  .pbc-priority-item {
    flex-wrap: wrap;
    padding: 12px;
  }

  .pbc-priority-controls {
    margin-top: 8px;
    width: 100%;
    justify-content: center;
  }

  .pbc-detection-settings .pbc-stats-grid {
    grid-template-columns: 1fr;
  }
}

/* Dark mode support (if WordPress admin uses dark theme) */
@media (prefers-color-scheme: dark) {
  .pbc-detection-settings .pbc-settings-section {
    background: #1e1e1e;
    border-color: #333;
  }

  .pbc-detection-settings .form-table th {
    background: #2a2a2a;
    color: #e0e0e0;
    border-color: #333;
  }

  .pbc-detection-settings .form-table td {
    border-color: #333;
  }

  .pbc-detection-settings select,
  .pbc-detection-settings input[type="text"],
  .pbc-detection-settings input[type="number"] {
    background: #f0f0f1;
    border-color: #c3c4c7;
    color: #1d2327;
  }

  .pbc-detection-settings .description {
    color: #aaa;
  }
}

/* Accessibility improvements */
.pbc-detection-settings *:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.pbc-detection-settings .button:focus {
  outline: 2px solid #fff;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .pbc-detection-settings .pbc-test-section,
  .pbc-detection-settings .submit {
    display: none;
  }

  .pbc-detection-settings .pbc-settings-section h3 {
    background: none !important;
    color: #495057 !important;
    border-bottom: 2px solid #495057;
  }
}
/* Additional CSS for enhanced JavaScript functionality */

/* IP Suggestions */
.pbc-ip-suggestions {
  margin-top: 8px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.pbc-ip-suggestions small {
  display: block;
  margin-bottom: 6px;
  font-weight: 600;
  color: #6c757d;
}

.pbc-ip-suggestion {
  display: inline-block;
  margin: 2px 4px;
  padding: 4px 8px;
  background: #fff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  text-decoration: none;
  font-size: 12px;
  color: #495057;
  transition: all 0.2s ease;
}

.pbc-ip-suggestion:hover {
  background: #667eea;
  border-color: #667eea;
  color: #fff;
  text-decoration: none;
}

/* Validation Errors */
.validation-error {
  display: none;
  margin-top: 4px;
  padding: 6px 8px;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  color: #721c24;
  font-size: 12px;
}

.pbc-detection-settings input.error,
.pbc-detection-settings select.error {
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1) !important;
}

/* Test Result Grid */
.test-result-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-top: 12px;
}

.result-item {
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.result-item strong {
  display: block;
  margin-bottom: 4px;
  color: #495057;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Notifications */
.pbc-notification {
  position: fixed;
  top: 32px;
  right: 20px;
  z-index: 100000;
  padding: 12px 20px;
  border-radius: 6px;
  color: #fff;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  display: none;
}

.pbc-notification-success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.pbc-notification-error {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.pbc-notification-info {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

/* Tooltip Popup */
.pbc-tooltip-popup {
  position: absolute;
  z-index: 100001;
  padding: 8px 12px;
  background: #333;
  color: #fff;
  border-radius: 4px;
  font-size: 12px;
  max-width: 200px;
  display: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.pbc-tooltip-popup:after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #333 transparent transparent transparent;
}

/* Priority Placeholder */
.pbc-priority-placeholder {
  background: #e9ecef;
  border: 2px dashed #adb5bd;
  border-radius: 8px;
  height: 52px;
  margin-bottom: 8px;
}

/* Current Method Indicator */
.pbc-current-method-indicator {
  margin-top: 8px;
}

/* Enhanced form sections with better spacing */
.pbc-detection-settings .form-table tr:first-child th,
.pbc-detection-settings .form-table tr:first-child td {
  padding-top: 24px;
}

.pbc-detection-settings .form-table tr:last-child th,
.pbc-detection-settings .form-table tr:last-child td {
  padding-bottom: 24px;
}

/* Smooth transitions for all interactive elements */
.pbc-detection-settings * {
  transition: all 0.2s ease;
}

/* Focus improvements for accessibility */
.pbc-detection-settings button:focus,
.pbc-detection-settings input:focus,
.pbc-detection-settings select:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Loading overlay for sections */
.pbc-section-loading {
  position: relative;
  pointer-events: none;
  opacity: 0.6;
}

.pbc-section-loading:after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 32px;
  height: 32px;
  margin: -16px 0 0 -16px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 1000;
}

/* Enhanced mobile responsiveness */
@media (max-width: 480px) {
  .pbc-detection-settings .pbc-settings-section h3 {
    padding: 12px 16px;
    font-size: 14px;
  }

  .pbc-detection-settings .form-table th,
  .pbc-detection-settings .form-table td {
    padding: 12px 16px;
  }

  .pbc-test-input-group {
    padding: 12px;
  }

  .pbc-priority-item {
    padding: 10px 12px;
  }

  .pbc-priority-number {
    width: 24px;
    height: 24px;
    font-size: 12px;
  }

  .test-result-grid {
    grid-template-columns: 1fr;
  }

  .pbc-notification {
    right: 10px;
    left: 10px;
    top: 20px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .pbc-detection-settings .pbc-settings-section {
    border-width: 2px;
  }

  .pbc-detection-settings .form-table th,
  .pbc-detection-settings .form-table td {
    border-width: 2px;
  }

  .pbc-detection-settings select,
  .pbc-detection-settings input[type="text"],
  .pbc-detection-settings input[type="number"] {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .pbc-detection-settings *,
  .pbc-notification,
  .pbc-tooltip-popup {
    animation: none !important;
    transition: none !important;
  }

  .pbc-detection-status.active,
  .pbc-detection-status.warning {
    animation: none !important;
  }
}
