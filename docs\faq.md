# Price by Country for WooCommerce - Frequently Asked Questions

## Table of Contents

1. [General Questions](#general-questions)
2. [Setup and Configuration](#setup-and-configuration)
3. [Pricing Rules and Logic](#pricing-rules-and-logic)
4. [Country Detection](#country-detection)
5. [Performance and Caching](#performance-and-caching)
6. [Compatibility](#compatibility)
7. [Business Use Cases](#business-use-cases)
8. [Troubleshooting](#troubleshooting)
9. [Technical Questions](#technical-questions)
10. [Advanced Usage](#advanced-usage)

## General Questions

### What is Price by Country for WooCommerce?

Price by Country is a WooCommerce plugin that allows you to set different prices for your products based on your customers' geographic location. It automatically detects where customers are located and displays appropriate pricing for their country.

### How does country-based pricing benefit my business?

Country-based pricing helps you:

- **Optimize for local markets**: Adjust prices based on local purchasing power
- **Handle currency fluctuations**: Compensate for exchange rate variations
- **Improve competitiveness**: Match local market pricing expectations
- **Increase conversions**: Offer appropriate pricing for different economic regions
- **Manage costs**: Account for varying shipping, tax, and operational costs

### Is this plugin suitable for my store size?

The plugin works for stores of all sizes:

- **Small stores**: Easy setup with minimal configuration needed
- **Medium stores**: Flexible rule system grows with your business
- **Large stores**: Enterprise-grade performance with caching and bulk operations
- **Multi-site networks**: Full multisite compatibility

### What's the difference between this and currency switcher plugins?

**Price by Country**:

- Changes the actual price amounts
- Works within a single currency
- Focuses on geographic pricing strategies
- Integrates with WooCommerce's native pricing system

**Currency Switcher**:

- Changes the currency display
- Usually applies exchange rates
- Focuses on currency conversion
- May require separate payment processing

You can use both types of plugins together for complete international commerce.

## Setup and Configuration

### How long does it take to set up the plugin?

**Basic setup**: 5-10 minutes using the setup wizard
**Complete configuration**: 30-60 minutes depending on your catalog size
**Advanced customization**: 2-4 hours for complex rule systems

### Do I need technical knowledge to use this plugin?

No technical knowledge is required for basic usage:

- **Setup wizard**: Guides you through initial configuration
- **Intuitive interface**: User-friendly admin panels
- **Documentation**: Comprehensive guides and tutorials
- **Support**: Available for complex configurations

### Can I test the plugin before applying it to my live store?

Yes, we recommend testing:

- **Staging environment**: Set up on a test site first
- **Debug mode**: Enable logging to monitor behavior
- **VPN testing**: Use VPN services to test different countries
- **Browser tools**: Simulate different locations using developer tools

### How do I migrate from another pricing plugin?

1. **Export existing data** from your current plugin
2. **Install Price by Country** alongside (don't activate yet)
3. **Convert data format** using our migration tools
4. **Import rules** using the CSV import feature
5. **Test thoroughly** before switching over
6. **Deactivate old plugin** once satisfied

### What if I make a mistake in my pricing rules?

The plugin includes several safety features:

- **Backup/restore**: Export rules before making changes
- **Bulk operations**: Quickly modify multiple rules
- **Rule preview**: See calculated prices before saving
- **Audit trail**: Track changes in debug logs
- **Easy rollback**: Disable rules without deleting them

## Pricing Rules and Logic

### How do I decide between fixed amount and percentage adjustments?

**Use Fixed Amounts When**:

- You have consistent markup needs across products
- Dealing with shipping or handling fees
- Making small adjustments to specific price points
- Working with products that have similar base prices

**Use Percentages When**:

- Maintaining profit margins across different markets
- Applying market-wide adjustments (like VAT)
- Scaling adjustments with product value
- Handling currency fluctuation compensation

### What happens when multiple rules could apply to a product?

The plugin follows a clear priority system:

1. **Product-specific rules** (highest priority)
2. **Category rules** (medium priority)
3. **Global rules** (lowest priority)
4. **Base price** (fallback)

Within each level, higher priority numbers take precedence.

### Can I set different prices for product variations?

Yes, each product variation can have its own country-specific pricing rules. This allows you to:

- Price different sizes/colors differently by country
- Account for varying shipping costs for different variations
- Apply market-specific strategies to specific product options

### How do I handle seasonal pricing changes?

Several approaches work well:

- **Scheduled rules**: Use WordPress cron to activate/deactivate rules
- **Bulk updates**: Export, modify, and re-import rules seasonally
- **Rule versioning**: Create multiple rule sets and switch between them
- **Integration**: Use with other plugins that handle seasonal pricing

### Can I offer country-specific discounts?

Yes, use negative values in your adjustments:

- **Fixed discounts**: -$10 for specific countries
- **Percentage discounts**: -15% for developing markets
- **Promotional pricing**: Temporary negative adjustments
- **Market penetration**: Lower prices for new markets

## Country Detection

### How accurate is the country detection?

Accuracy depends on the detection method:

- **IP-based detection**: 95-98% accurate for most countries
- **Address-based detection**: 99%+ accurate when customers provide addresses
- **Combined method**: Highest accuracy using multiple data points

Factors affecting accuracy:

- VPN usage by customers
- Corporate proxy servers
- Mobile carrier routing
- Geolocation database quality

### What happens if country detection fails?

The plugin has multiple fallback mechanisms:

1. **Default country**: Set in plugin settings
2. **Base pricing**: Original product prices
3. **Error logging**: Track detection failures
4. **Manual override**: Customers can select their country

### Can customers override the detected country?

Yes, you can enable customer country selection:

- **Country selector widget**: Add to checkout or product pages
- **Account settings**: Let customers set preferred country
- **Session override**: Temporary country selection
- **Cookie storage**: Remember customer preferences

### How does this work with VPNs and proxies?

VPNs and proxies can affect detection:

- **IP detection**: May show VPN server location
- **Address detection**: Still works correctly
- **Hybrid approach**: Reduces VPN impact
- **Customer override**: Allows manual correction

### Does this work for mobile users?

Yes, mobile detection works well:

- **Mobile IP detection**: Uses carrier geolocation
- **GPS integration**: Can use device location (with permission)
- **Responsive interface**: Mobile-friendly admin panels
- **Touch optimization**: Easy mobile configuration

## Performance and Caching

### Will this plugin slow down my website?

When properly configured, performance impact is minimal:

- **Built-in caching**: Reduces database queries
- **Optimized code**: Efficient pricing calculations
- **Lazy loading**: Only loads when needed
- **Performance monitoring**: Track impact in debug mode

Typical performance impact: <50ms additional page load time

### How does caching work?

The plugin uses multiple caching layers:

- **Rule caching**: Stores pricing rules in memory
- **Calculation caching**: Caches computed prices
- **Country caching**: Remembers detection results
- **Database caching**: Reduces query overhead

### What caching plugins are compatible?

Compatible with most caching plugins:

- **WP Rocket**: Full compatibility
- **W3 Total Cache**: Works with proper configuration
- **WP Super Cache**: Compatible with exclusions
- **LiteSpeed Cache**: Full support
- **Cloudflare**: Works with page rules

### How do I optimize performance for high-traffic sites?

**Server-level optimizations**:

- Use object caching (Redis/Memcached)
- Enable database query caching
- Implement CDN with proper headers
- Use server-side caching (Varnish/Nginx)

**Plugin-level optimizations**:

- Increase cache durations
- Enable rule preloading
- Minimize rule complexity
- Use bulk operations for updates

### Can I use this with a CDN?

Yes, with proper configuration:

- **Cache headers**: Set appropriate cache control
- **Country headers**: Pass geolocation data
- **Edge includes**: Use ESI for dynamic content
- **Purge rules**: Clear cache when prices change

## Compatibility

### Which WooCommerce versions are supported?

- **Minimum**: WooCommerce 5.0+
- **Recommended**: WooCommerce 7.1+ (for HPOS support)
- **Tested up to**: Latest WooCommerce version
- **Backward compatibility**: Maintains support for older versions

### Does this work with WooCommerce HPOS?

Yes, full High-Performance Order Storage (HPOS) compatibility:

- **Native support**: Built for HPOS architecture
- **Performance benefits**: Faster order processing
- **Migration support**: Smooth transition from legacy storage
- **Future-proof**: Ready for WooCommerce's direction

### What other plugins is this compatible with?

**Fully Compatible**:

- WooCommerce Subscriptions
- WooCommerce Memberships
- WooCommerce Bookings
- WPML (multilingual)
- Polylang
- Most payment gateways

**Requires Configuration**:

- Currency switcher plugins
- Advanced pricing plugins
- Custom checkout plugins
- Some membership plugins

**Not Compatible**:

- Other country-based pricing plugins
- Plugins that override WooCommerce pricing hooks

### Does this work with multisite?

Yes, full multisite support:

- **Network activation**: Install once for all sites
- **Site-specific settings**: Each site can have unique configuration
- **Shared rules**: Option to share rules across network
- **Centralized management**: Network admin controls

### Can I use this with membership plugins?

Yes, works with most membership plugins:

- **Member-specific pricing**: Combine with membership discounts
- **Country + membership**: Layer both types of pricing
- **Access control**: Respect membership restrictions
- **Integration hooks**: Custom integration possible

## Business Use Cases

### How do I handle VAT and tax differences?

The plugin works with WooCommerce's tax system:

- **Tax-inclusive pricing**: Adjust prices including tax
- **Tax-exclusive pricing**: Let WooCommerce add tax
- **VAT handling**: Account for VAT differences in pricing
- **Tax class integration**: Works with different tax classes

### Can I use this for B2B pricing?

Yes, excellent for B2B scenarios:

- **Regional pricing**: Different prices for different markets
- **Volume considerations**: Combine with quantity-based pricing
- **Customer groups**: Use with role-based pricing plugins
- **Contract pricing**: Set specific prices for business customers

### How do I handle different market strategies?

**Market Penetration**:

- Lower prices for new markets
- Gradual price increases over time
- Competitive positioning

**Premium Positioning**:

- Higher prices for developed markets
- Quality perception management
- Brand positioning strategy

**Cost-Plus Pricing**:

- Account for local operational costs
- Shipping and handling variations
- Currency risk management

### What about seasonal or promotional pricing?

Combine with other strategies:

- **Base country pricing**: Set your foundation
- **Seasonal adjustments**: Layer on top of country pricing
- **Promotional codes**: Stack with country-specific prices
- **Time-limited rules**: Activate/deactivate rules as needed

### How do I handle returns and refunds?

WooCommerce handles this automatically:

- **Original price**: Refunds use the price customer paid
- **Country-adjusted**: Includes any country-specific adjustments
- **Partial refunds**: Work normally with adjusted prices
- **Exchange rates**: Handle separately if using multiple currencies

## Troubleshooting

### Prices aren't changing based on country. What should I check?

**Step-by-step diagnosis**:

1. **Verify country detection**: Check what country is being detected
2. **Check rule existence**: Ensure rules exist for the test country
3. **Verify rule status**: Make sure rules are active/enabled
4. **Clear caches**: Clear all caching layers
5. **Check inheritance**: Verify rule priority and inheritance settings
6. **Test in isolation**: Disable other plugins temporarily

### The admin interface isn't loading. How do I fix this?

**Common solutions**:

1. **Check JavaScript errors**: Open browser console for errors
2. **Plugin conflicts**: Deactivate other plugins one by one
3. **Theme issues**: Switch to a default theme temporarily
4. **User permissions**: Ensure proper administrator privileges
5. **Memory limits**: Increase PHP memory limit
6. **File permissions**: Check WordPress file permissions

### Country detection isn't working correctly. What can I do?

**Troubleshooting steps**:

1. **Test detection method**: Try different detection methods
2. **Check IP databases**: Verify geolocation service is working
3. **Test with VPN**: Use VPN to test different countries
4. **Review debug logs**: Enable debug mode and check logs
5. **Manual override**: Enable customer country selection
6. **Fallback settings**: Ensure default country is set

### Performance is slow after installing. How do I optimize?

**Performance optimization**:

1. **Enable caching**: Turn on all caching options
2. **Optimize rules**: Consolidate and simplify rules
3. **Database optimization**: Optimize database tables
4. **Server resources**: Check server performance
5. **Plugin conflicts**: Test for plugin conflicts
6. **Caching plugins**: Configure caching plugin properly

### Import/export isn't working. What's wrong?

**Common import/export issues**:

1. **File format**: Verify CSV format is correct
2. **Column mapping**: Ensure columns are mapped properly
3. **Data validation**: Check for invalid data in CSV
4. **File permissions**: Verify upload permissions
5. **Memory limits**: Increase limits for large files
6. **Character encoding**: Use UTF-8 encoding

## Technical Questions

### How does the plugin integrate with WooCommerce?

The plugin uses WooCommerce's standard hooks and filters:

- **Price filters**: Modifies prices using WooCommerce hooks
- **Product data**: Stores data in WooCommerce meta fields
- **Admin integration**: Extends WooCommerce admin panels
- **API compatibility**: Works with WooCommerce REST API

### Can I customize the plugin behavior with code?

Yes, extensive customization options:

- **Action hooks**: Add custom functionality
- **Filter hooks**: Modify plugin behavior
- **Template overrides**: Customize frontend display
- **API endpoints**: Extend functionality
- **Custom rules**: Add your own rule types

### What data does the plugin store?

**Database tables**:

- Pricing rules and configurations
- Country detection cache
- Performance metrics
- Debug logs (if enabled)

**WordPress options**:

- Plugin settings
- Cache data
- User preferences

### Is the plugin GDPR compliant?

Yes, designed with privacy in mind:

- **Minimal data**: Only stores necessary pricing data
- **Anonymous detection**: IP-based detection is anonymous
- **No personal data**: Doesn't store personal information
- **Data retention**: Configurable log retention periods
- **User control**: Customers can override detection

### Can I extend the plugin with custom code?

Absolutely, the plugin is developer-friendly:

```php
// Example: Custom pricing rule
add_filter('pbc_calculate_price', function($price, $product, $country) {
    // Your custom logic here
    return $price;
}, 10, 3);

// Example: Custom country detection
add_filter('pbc_detect_country', function($country) {
    // Your custom detection logic
    return $country;
});
```

## Advanced Usage

### How do I set up complex pricing strategies?

**Multi-tier pricing**:

- Combine global, category, and product rules
- Use rule priorities for fine control
- Layer different adjustment types

**Dynamic pricing**:

- Integrate with external APIs
- Use WordPress cron for scheduled updates
- Implement real-time currency adjustments

**A/B testing**:

- Create multiple rule sets
- Use customer segmentation
- Track conversion rates by pricing strategy

### Can I integrate with external systems?

Yes, several integration options:

- **REST API**: Full API access to pricing data
- **Webhooks**: Real-time updates to external systems
- **CSV sync**: Regular data synchronization
- **Custom endpoints**: Build your own integrations

### How do I handle complex business rules?

**Custom rule types**:

- Extend the plugin with custom rule logic
- Implement business-specific calculations
- Add approval workflows for price changes

**Integration patterns**:

- ERP system integration
- CRM-based pricing
- Inventory-based adjustments
- Customer-specific pricing

### What about automated pricing updates?

**Scheduled updates**:

- WordPress cron jobs
- External API calls
- CSV file imports
- Database direct updates

**Real-time updates**:

- API webhooks
- Live data feeds
- Currency rate APIs
- Market data integration

### How do I scale for enterprise use?

**Performance scaling**:

- Database optimization
- Caching strategies
- Load balancing
- CDN integration

**Management scaling**:

- Bulk operations
- API automation
- Role-based access
- Audit trails

---

## Still Have Questions?

If you can't find the answer to your question here:

1. **Check the User Manual**: Comprehensive step-by-step guides
2. **Review Installation Guide**: Detailed setup instructions
3. **Browse Developer Documentation**: Technical implementation details
4. **Visit Support Forum**: Community discussions and solutions
5. **Contact Direct Support**: Premium support for license holders
6. **Watch Video Tutorials**: Visual step-by-step instructions

Remember to include your plugin version, WordPress version, WooCommerce version, and any error messages when seeking support!
