<?php
/**
 * WooCommerce Compatibility Integration Tests
 *
 * @package PriceByCountry
 */

class Test_WooCommerce_Compatibility extends WP_UnitTestCase {

    /**
     * Compatibility checker instance
     *
     * @var PBC_Compatibility_Checker
     */
    private $compatibility_checker;

    /**
     * Set up test environment
     */
    public function setUp(): void {
        parent::setUp();
        
        // Initialize compatibility checker
        $this->compatibility_checker = new PBC_Compatibility_Checker();
    }

    /**
     * Test WordPress compatibility
     */
    public function test_wordpress_compatibility() {
        $results = $this->compatibility_checker->get_test_results();
        
        $this->assertArrayHasKey('wordpress', $results);
        $this->assertNotEquals('fail', $results['wordpress']['status']);
        $this->assertArrayHasKey('current_version', $results['wordpress']['details']);
    }

    /**
     * Test WooCommerce compatibility
     */
    public function test_woocommerce_compatibility() {
        $results = $this->compatibility_checker->get_test_results();
        
        $this->assertArrayHasKey('woocommerce', $results);
        $this->assertNotEquals('fail', $results['woocommerce']['status']);
        $this->assertArrayHasKey('current_version', $results['woocommerce']['details']);
    }

    /**
     * Test PHP compatibility
     */
    public function test_php_compatibility() {
        $results = $this->compatibility_checker->get_test_results();
        
        $this->assertArrayHasKey('php', $results);
        $this->assertNotEquals('fail', $results['php']['status']);
        $this->assertArrayHasKey('current_version', $results['php']['details']);
    }

    /**
     * Test HPOS compatibility
     */
    public function test_hpos_compatibility() {
        $results = $this->compatibility_checker->get_test_results();
        
        $this->assertArrayHasKey('hpos', $results);
        $this->assertArrayHasKey('hpos_enabled', $results['hpos']['details']);
        $this->assertArrayHasKey('compatibility_declared', $results['hpos']['details']);
    }

    /**
     * Test WooCommerce features availability
     */
    public function test_woocommerce_features() {
        $results = $this->compatibility_checker->get_test_results();
        
        $this->assertArrayHasKey('features', $results);
        
        $required_features = array(
            'countries', 'geolocation', 'product', 'cart', 
            'checkout', 'order', 'customer', 'session'
        );
        
        foreach ($required_features as $feature) {
            $this->assertArrayHasKey($feature, $results['features']['details']);
            $this->assertTrue($results['features']['details'][$feature], "Feature {$feature} should be available");
        }
    }

    /**
     * Test hook compatibility
     */
    public function test_hook_compatibility() {
        $results = $this->compatibility_checker->get_test_results();
        
        $this->assertArrayHasKey('hooks', $results);
        $this->assertArrayHasKey('details', $results['hooks']);
    }

    /**
     * Test database compatibility
     */
    public function test_database_compatibility() {
        $results = $this->compatibility_checker->get_test_results();
        
        $this->assertArrayHasKey('database', $results);
        $this->assertArrayHasKey('mysql_version', $results['database']['details']);
        $this->assertArrayHasKey('can_create_tables', $results['database']['details']);
    }

    /**
     * Test overall compatibility
     */
    public function test_overall_compatibility() {
        $this->assertTrue($this->compatibility_checker->is_compatible());
    }

    /**
     * Test compatibility report generation
     */
    public function test_compatibility_report() {
        $report = $this->compatibility_checker->generate_report();
        
        $this->assertIsString($report);
        $this->assertStringContains('WooCommerce Compatibility Report', $report);
        $this->assertStringContains('pbc-compatibility-report', $report);
    }

    /**
     * Test WooCommerce version detection
     */
    public function test_woocommerce_version_detection() {
        $this->assertTrue(defined('WC_VERSION'));
        $this->assertNotEmpty(WC_VERSION);
        $this->assertTrue(version_compare(WC_VERSION, '5.0', '>='));
    }

    /**
     * Test WooCommerce classes availability
     */
    public function test_woocommerce_classes() {
        $required_classes = array(
            'WooCommerce',
            'WC_Product',
            'WC_Cart',
            'WC_Checkout',
            'WC_Order',
            'WC_Customer',
            'WC_Countries',
            'WC_Geolocation'
        );

        foreach ($required_classes as $class) {
            $this->assertTrue(class_exists($class), "Class {$class} should exist");
        }
    }

    /**
     * Test WooCommerce functions availability
     */
    public function test_woocommerce_functions() {
        $required_functions = array(
            'wc_get_product',
            'wc_get_order',
            'wc_get_customer',
            'wc_price',
            'wc_format_decimal',
            'get_woocommerce_currency',
            'wc_clean'
        );

        foreach ($required_functions as $function) {
            $this->assertTrue(function_exists($function), "Function {$function} should exist");
        }
    }

    /**
     * Test WooCommerce hooks integration
     */
    public function test_woocommerce_hooks_integration() {
        // Test that our plugin can hook into WooCommerce
        $core = PBC_Core::get_instance();
        $this->assertInstanceOf('PBC_Core', $core);
        
        // Test that hooks manager is initialized
        $this->assertInstanceOf('PBC_Hooks', $core->hooks);
        
        // Test that pricing engine hooks are registered
        $this->assertTrue(has_filter('woocommerce_product_get_price'));
        $this->assertTrue(has_filter('woocommerce_product_get_sale_price'));
    }

    /**
     * Test HPOS compatibility declaration
     */
    public function test_hpos_compatibility_declaration() {
        // Test that HPOS compatibility is declared
        $this->assertTrue(has_action('before_woocommerce_init'));
        
        // Test that FeaturesUtil is available in newer WooCommerce versions
        if (version_compare(WC_VERSION, '7.1', '>=')) {
            $this->assertTrue(class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil'));
        }
    }

    /**
     * Test WooCommerce settings integration
     */
    public function test_woocommerce_settings_integration() {
        // Test that we can access WooCommerce settings
        $this->assertNotEmpty(get_option('woocommerce_default_country'));
        $this->assertNotEmpty(get_woocommerce_currency());
        
        // Test that WooCommerce countries are available
        $countries = WC()->countries->get_countries();
        $this->assertIsArray($countries);
        $this->assertNotEmpty($countries);
    }

    /**
     * Test WooCommerce REST API compatibility
     */
    public function test_rest_api_compatibility() {
        $results = $this->compatibility_checker->get_test_results();
        
        if (isset($results['features']['details']['rest_api'])) {
            $this->assertTrue($results['features']['details']['rest_api']);
        }
        
        // Test that REST API classes exist
        if (class_exists('WC_REST_Products_Controller')) {
            $this->assertTrue(true);
        }
    }

    /**
     * Test multisite compatibility
     */
    public function test_multisite_compatibility() {
        // Test that plugin works in multisite environment
        if (is_multisite()) {
            $this->assertTrue(function_exists('get_site_option'));
            $this->assertTrue(function_exists('update_site_option'));
        }
        
        // Test that WooCommerce detection works in multisite
        $this->assertTrue(function_exists('pbc_is_woocommerce_active'));
    }

    /**
     * Test plugin activation requirements
     */
    public function test_activation_requirements() {
        // Test that check_requirements method exists
        $reflection = new ReflectionClass('PBC_Core');
        $this->assertTrue($reflection->hasMethod('check_requirements'));
        
        // Test that requirements are met
        $method = $reflection->getMethod('check_requirements');
        $method->setAccessible(true);
        $this->assertTrue($method->invoke(null));
    }

    /**
     * Test version comparison functions
     */
    public function test_version_comparisons() {
        global $wp_version;
        
        // Test WordPress version
        $this->assertTrue(version_compare($wp_version, '5.0', '>='));
        
        // Test WooCommerce version
        $this->assertTrue(version_compare(WC_VERSION, '5.0', '>='));
        
        // Test PHP version
        $this->assertTrue(version_compare(PHP_VERSION, '7.4', '>='));
    }

    /**
     * Test compatibility with WooCommerce extensions
     */
    public function test_woocommerce_extensions_compatibility() {
        // Test that our plugin doesn't conflict with common WooCommerce extensions
        // This is a basic test - in real scenarios, you'd test with actual extensions
        
        $this->assertTrue(true); // Placeholder for extension compatibility tests
    }

    /**
     * Test error handling for compatibility issues
     */
    public function test_compatibility_error_handling() {
        $issues = $this->compatibility_checker->get_compatibility_issues();
        
        // Should return an array (empty if no issues)
        $this->assertIsArray($issues);
        
        // Each issue should have required fields
        foreach ($issues as $issue) {
            $this->assertArrayHasKey('test', $issue);
            $this->assertArrayHasKey('status', $issue);
            $this->assertArrayHasKey('message', $issue);
        }
    }

    /**
     * Test compatibility checker performance
     */
    public function test_compatibility_checker_performance() {
        $start_time = microtime(true);
        
        $checker = new PBC_Compatibility_Checker();
        $results = $checker->get_test_results();
        
        $end_time = microtime(true);
        $execution_time = $end_time - $start_time;
        
        // Compatibility check should complete within reasonable time (2 seconds)
        $this->assertLessThan(2.0, $execution_time);
        $this->assertNotEmpty($results);
    }
}