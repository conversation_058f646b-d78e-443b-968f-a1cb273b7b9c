<?php
/**
 * WooCommerce Compatibility Checker for Price by Country
 *
 * @package PriceByCountry
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PBC Compatibility Checker Class
 */
class PBC_Compatibility_Checker {

    /**
     * Minimum required versions
     */
    const MIN_WP_VERSION = '5.0';
    const MIN_WC_VERSION = '5.0';
    const MIN_PHP_VERSION = '7.4';
    const RECOMMENDED_WC_VERSION = '7.1';

    /**
     * Compatibility test results
     *
     * @var array
     */
    private $test_results = array();

    /**
     * Constructor
     */
    public function __construct() {
        // Don't run tests immediately to avoid translation issues
        // Tests will be run when first accessed
    }

    /**
     * Check if translations are available
     *
     * @return bool
     */
    private function are_translations_loaded() {
        return did_action('init') > 0;
    }

    /**
     * Get translated string or fallback
     *
     * @param string $text Text to translate
     * @param string $fallback Fallback text if translations not loaded
     * @return string
     */
    private function get_text($text, $fallback = null) {
        if ($this->are_translations_loaded()) {
            return __($text, 'price-by-country');
        }
        return $fallback ?: $text;
    }

    /**
     * Run all compatibility tests
     */
    private function run_compatibility_tests() {
        $this->test_results = array(
            'wordpress' => $this->test_wordpress_compatibility(),
            'woocommerce' => $this->test_woocommerce_compatibility(),
            'php' => $this->test_php_compatibility(),
            'hpos' => $this->test_hpos_compatibility(),
            'features' => $this->test_woocommerce_features(),
            'hooks' => $this->test_hook_compatibility(),
            'database' => $this->test_database_compatibility(),
            'overall' => false // Will be set after all tests
        );

        // Set overall compatibility
        $this->test_results['overall'] = $this->calculate_overall_compatibility();
    }

    /**
     * Test WordPress compatibility
     *
     * @return array Test results
     */
    private function test_wordpress_compatibility() {
        global $wp_version;

        $result = array(
            'status' => 'pass',
            'message' => '',
            'details' => array()
        );

        // Check WordPress version
        if (version_compare($wp_version, self::MIN_WP_VERSION, '<')) {
            $result['status'] = 'fail';
            $result['message'] = sprintf(
                $this->get_text('WordPress %s or higher is required. Current version: %s', 'WordPress %s or higher is required. Current version: %s'),
                self::MIN_WP_VERSION,
                $wp_version
            );
        } else {
            $result['message'] = sprintf(
                $this->get_text('WordPress version %s is compatible', 'WordPress version %s is compatible'),
                $wp_version
            );
        }

        $result['details'] = array(
            'current_version' => $wp_version,
            'required_version' => self::MIN_WP_VERSION,
            'is_multisite' => is_multisite(),
            'debug_mode' => defined('WP_DEBUG') && WP_DEBUG
        );

        return $result;
    }

    /**
     * Test WooCommerce compatibility
     *
     * @return array Test results
     */
    private function test_woocommerce_compatibility() {
        $result = array(
            'status' => 'pass',
            'message' => '',
            'details' => array()
        );

        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            $result['status'] = 'fail';
            $result['message'] = $this->get_text('WooCommerce is not installed or activated', 'WooCommerce is not installed or activated');
            return $result;
        }

        // Check WooCommerce version
        $wc_version = defined('WC_VERSION') ? WC_VERSION : '0.0.0';
        
        if (version_compare($wc_version, self::MIN_WC_VERSION, '<')) {
            $result['status'] = 'fail';
            $result['message'] = sprintf(
                $this->get_text('WooCommerce %s or higher is required. Current version: %s', 'WooCommerce %s or higher is required. Current version: %s'),
                self::MIN_WC_VERSION,
                $wc_version
            );
        } elseif (version_compare($wc_version, self::RECOMMENDED_WC_VERSION, '<')) {
            $result['status'] = 'warning';
            $result['message'] = sprintf(
                $this->get_text('WooCommerce %s is recommended for full feature support. Current version: %s', 'WooCommerce %s is recommended for full feature support. Current version: %s'),
                self::RECOMMENDED_WC_VERSION,
                $wc_version
            );
        } else {
            $result['message'] = sprintf(
                $this->get_text('WooCommerce version %s is fully compatible', 'WooCommerce version %s is fully compatible'),
                $wc_version
            );
        }

        $result['details'] = array(
            'current_version' => $wc_version,
            'required_version' => self::MIN_WC_VERSION,
            'recommended_version' => self::RECOMMENDED_WC_VERSION,
            'database_version' => get_option('woocommerce_db_version', '0.0.0'),
            'is_rest_api_enabled' => $this->is_wc_rest_api_enabled(),
            'currency' => function_exists('get_woocommerce_currency') ? get_woocommerce_currency() : 'N/A',
            'base_country' => (WC() && WC()->countries) ? WC()->countries->get_base_country() : 'N/A'
        );

        return $result;
    }

    /**
     * Test PHP compatibility
     *
     * @return array Test results
     */
    private function test_php_compatibility() {
        $result = array(
            'status' => 'pass',
            'message' => '',
            'details' => array()
        );

        $php_version = PHP_VERSION;

        if (version_compare($php_version, self::MIN_PHP_VERSION, '<')) {
            $result['status'] = 'fail';
            $result['message'] = sprintf(
                $this->get_text('PHP %s or higher is required. Current version: %s', 'PHP %s or higher is required. Current version: %s'),
                self::MIN_PHP_VERSION,
                $php_version
            );
        } else {
            $result['message'] = sprintf(
                $this->get_text('PHP version %s is compatible', 'PHP version %s is compatible'),
                $php_version
            );
        }

        $result['details'] = array(
            'current_version' => $php_version,
            'required_version' => self::MIN_PHP_VERSION,
            'extensions' => $this->check_php_extensions(),
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time')
        );

        return $result;
    }

    /**
     * Test HPOS compatibility
     *
     * @return array Test results
     */
    private function test_hpos_compatibility() {
        $result = array(
            'status' => 'pass',
            'message' => '',
            'details' => array()
        );

        // Check if HPOS utilities are available
        $features_util_available = class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil');
        $order_util_available = class_exists('\Automattic\WooCommerce\Utilities\OrderUtil');

        if (!$features_util_available) {
            $result['status'] = 'warning';
            $result['message'] = $this->get_text('HPOS utilities not available. Plugin will work with legacy order storage.', 'HPOS utilities not available. Plugin will work with legacy order storage.');
        } else {
            $result['message'] = $this->get_text('HPOS compatibility is fully supported', 'HPOS compatibility is fully supported');
        }

        $result['details'] = array(
            'features_util_available' => $features_util_available,
            'order_util_available' => $order_util_available,
            'hpos_enabled' => $this->is_hpos_enabled(),
            'compatibility_declared' => $this->is_hpos_compatibility_declared(),
            'custom_tables_exist' => $this->do_hpos_tables_exist()
        );

        return $result;
    }

    /**
     * Test WooCommerce features compatibility
     *
     * @return array Test results
     */
    private function test_woocommerce_features() {
        $result = array(
            'status' => 'pass',
            'message' => $this->get_text('All required WooCommerce features are available', 'All required WooCommerce features are available'),
            'details' => array()
        );

        $features = array(
            'countries' => class_exists('WC_Countries'),
            'geolocation' => class_exists('WC_Geolocation'),
            'product' => class_exists('WC_Product'),
            'cart' => class_exists('WC_Cart'),
            'checkout' => class_exists('WC_Checkout'),
            'order' => class_exists('WC_Order'),
            'customer' => class_exists('WC_Customer'),
            'session' => class_exists('WC_Session'),
            'rest_api' => class_exists('WC_REST_Products_Controller'),
            'admin' => class_exists('WC_Admin')
        );

        $missing_features = array_keys(array_filter($features, function($available) {
            return !$available;
        }));

        if (!empty($missing_features)) {
            $result['status'] = 'warning';
            $result['message'] = sprintf(
                $this->get_text('Some WooCommerce features are not available: %s', 'Some WooCommerce features are not available: %s'),
                implode(', ', $missing_features)
            );
        }

        $result['details'] = $features;

        return $result;
    }

    /**
     * Test hook compatibility
     *
     * @return array Test results
     */
    private function test_hook_compatibility() {
        $result = array(
            'status' => 'pass',
            'message' => $this->get_text('All required hooks are available', 'All required hooks are available'),
            'details' => array()
        );

        $required_hooks = array(
            'woocommerce_product_get_price',
            'woocommerce_product_get_sale_price',
            'woocommerce_variation_prices',
            'woocommerce_cart_item_price',
            'woocommerce_checkout_update_order_review',
            'woocommerce_before_calculate_totals',
            'woocommerce_product_options_pricing',
            'woocommerce_process_product_meta'
        );

        $hook_availability = array();
        foreach ($required_hooks as $hook) {
            $hook_availability[$hook] = has_filter($hook) !== false || has_action($hook) !== false;
        }

        $result['details'] = $hook_availability;

        return $result;
    }

    /**
     * Test database compatibility
     *
     * @return array Test results
     */
    private function test_database_compatibility() {
        global $wpdb;

        $result = array(
            'status' => 'pass',
            'message' => $this->get_text('Database is compatible', 'Database is compatible'),
            'details' => array()
        );

        $result['details'] = array(
            'mysql_version' => $wpdb->db_version(),
            'charset' => $wpdb->charset,
            'collate' => $wpdb->collate,
            'prefix' => $wpdb->prefix,
            'can_create_tables' => $this->can_create_database_tables(),
            'innodb_available' => $this->is_innodb_available()
        );

        // Check MySQL version
        if (version_compare($wpdb->db_version(), '5.6', '<')) {
            $result['status'] = 'warning';
            $result['message'] = $this->get_text('MySQL 5.6 or higher is recommended for optimal performance', 'MySQL 5.6 or higher is recommended for optimal performance');
        }

        return $result;
    }

    /**
     * Calculate overall compatibility status
     *
     * @return bool Overall compatibility status
     */
    private function calculate_overall_compatibility() {
        $critical_tests = array('wordpress', 'woocommerce', 'php');
        
        foreach ($critical_tests as $test) {
            if (isset($this->test_results[$test]) && $this->test_results[$test]['status'] === 'fail') {
                return false;
            }
        }

        return true;
    }

    /**
     * Get all test results
     *
     * @return array Test results
     */
    public function get_test_results() {
        if (empty($this->test_results)) {
            $this->run_compatibility_tests();
        }
        return $this->test_results;
    }

    /**
     * Get overall compatibility status
     *
     * @return bool Compatibility status
     */
    public function is_compatible() {
        if (empty($this->test_results)) {
            $this->run_compatibility_tests();
        }
        return $this->test_results['overall'];
    }

    /**
     * Get compatibility issues
     *
     * @return array Issues found
     */
    public function get_compatibility_issues() {
        if (empty($this->test_results)) {
            $this->run_compatibility_tests();
        }
        
        $issues = array();

        foreach ($this->test_results as $test_name => $result) {
            if ($test_name === 'overall') {
                continue;
            }

            if (isset($result['status']) && in_array($result['status'], array('fail', 'warning'))) {
                $issues[] = array(
                    'test' => $test_name,
                    'status' => $result['status'],
                    'message' => $result['message']
                );
            }
        }

        return $issues;
    }

    /**
     * Check if HPOS is enabled
     *
     * @return bool HPOS status
     */
    private function is_hpos_enabled() {
        if (class_exists('\Automattic\WooCommerce\Utilities\OrderUtil')) {
            return \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled();
        }
        return false;
    }

    /**
     * Check if HPOS compatibility is declared
     *
     * @return bool Declaration status
     */
    private function is_hpos_compatibility_declared() {
        return has_action('before_woocommerce_init');
    }

    /**
     * Check if HPOS tables exist
     *
     * @return bool Tables existence
     */
    private function do_hpos_tables_exist() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'wc_orders';
        $table_exists = $wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $table_name)) === $table_name;
        
        return $table_exists;
    }

    /**
     * Check if WooCommerce REST API is enabled
     *
     * @return bool REST API status
     */
    private function is_wc_rest_api_enabled() {
        return class_exists('WC_REST_Products_Controller') && 
               function_exists('rest_get_server') && 
               get_option('woocommerce_api_enabled', 'yes') === 'yes';
    }

    /**
     * Check required PHP extensions
     *
     * @return array Extension availability
     */
    private function check_php_extensions() {
        $required_extensions = array(
            'curl' => extension_loaded('curl'),
            'json' => extension_loaded('json'),
            'mbstring' => extension_loaded('mbstring'),
            'openssl' => extension_loaded('openssl'),
            'zip' => extension_loaded('zip')
        );

        return $required_extensions;
    }

    /**
     * Check if we can create database tables
     *
     * @return bool Creation capability
     */
    private function can_create_database_tables() {
        global $wpdb;
        
        // Try to create a temporary table
        $test_table = $wpdb->prefix . 'pbc_test_' . time();
        $sql = "CREATE TABLE {$test_table} (id int(11) NOT NULL AUTO_INCREMENT, PRIMARY KEY (id)) ENGINE=InnoDB";
        
        $result = $wpdb->query($sql);
        
        if ($result !== false) {
            // Clean up test table
            $wpdb->query("DROP TABLE IF EXISTS {$test_table}");
            return true;
        }
        
        return false;
    }

    /**
     * Check if InnoDB is available
     *
     * @return bool InnoDB availability
     */
    private function is_innodb_available() {
        global $wpdb;
        
        $engines = $wpdb->get_results("SHOW ENGINES", ARRAY_A);
        
        foreach ($engines as $engine) {
            if (strtolower($engine['Engine']) === 'innodb' && 
                in_array(strtolower($engine['Support']), array('yes', 'default'))) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Generate compatibility report
     *
     * @return string HTML report
     */
    public function generate_report() {
        if (empty($this->test_results)) {
            $this->run_compatibility_tests();
        }
        
        $html = '<div class="pbc-compatibility-report">';
        $html .= '<h3>' . $this->get_text('WooCommerce Compatibility Report', 'WooCommerce Compatibility Report') . '</h3>';

        foreach ($this->test_results as $test_name => $result) {
            if ($test_name === 'overall') {
                continue;
            }

            $status_class = 'pbc-status-' . (isset($result['status']) ? $result['status'] : 'unknown');
            $status_icon = $this->get_status_icon($result['status'] ?? 'unknown');

            $html .= '<div class="pbc-test-result ' . $status_class . '">';
            $html .= '<h4>' . $status_icon . ' ' . ucfirst($test_name) . ' Compatibility</h4>';
            $html .= '<p>' . esc_html($result['message']) . '</p>';

            if (!empty($result['details'])) {
                $html .= '<details><summary>' . $this->get_text('Details', 'Details') . '</summary>';
                $html .= '<ul>';
                foreach ($result['details'] as $key => $value) {
                    $html .= '<li><strong>' . esc_html($key) . ':</strong> ' . esc_html($this->format_detail_value($value)) . '</li>';
                }
                $html .= '</ul></details>';
            }

            $html .= '</div>';
        }

        $html .= '</div>';

        return $html;
    }

    /**
     * Get status icon
     *
     * @param string $status Status
     * @return string Icon HTML
     */
    private function get_status_icon($status) {
        switch ($status) {
            case 'pass':
                return '<span class="dashicons dashicons-yes-alt" style="color: green;"></span>';
            case 'warning':
                return '<span class="dashicons dashicons-warning" style="color: orange;"></span>';
            case 'fail':
                return '<span class="dashicons dashicons-dismiss" style="color: red;"></span>';
            default:
                return '<span class="dashicons dashicons-info" style="color: blue;"></span>';
        }
    }

    /**
     * Format detail value for display
     *
     * @param mixed $value Value to format
     * @return string Formatted value
     */
    private function format_detail_value($value) {
        if (is_bool($value)) {
            return $value ? $this->get_text('Yes', 'Yes') : $this->get_text('No', 'No');
        } elseif (is_array($value)) {
            return implode(', ', array_map(array($this, 'format_detail_value'), $value));
        } else {
            return (string) $value;
        }
    }
}