# Price by Country for WooCommerce - Code Documentation Standards

## Table of Contents

1. [PHPDoc Standards](#phpdoc-standards)
2. [Class Documentation](#class-documentation)
3. [Method Documentation](#method-documentation)
4. [Property Documentation](#property-documentation)
5. [Hook Documentation](#hook-documentation)
6. [Inline Comments](#inline-comments)
7. [Code Examples](#code-examples)

## PHPDoc Standards

The plugin follows WordPress and PSR-5 PHPDoc standards for comprehensive code documentation.

### File Headers

Every PHP file should start with a proper file header:

```php
<?php
/**
 * Pricing engine class for Price by Country
 *
 * This file contains the core pricing calculation logic for the Price by Country
 * plugin. It handles rule hierarchy, caching, and price adjustments based on
 * customer location.
 *
 * @package    PriceByCountry
 * @subpackage Core
 * @since      1.0.0
 * <AUTHOR> Name <<EMAIL>>
 * @copyright  2024 Your Company
 * @license    GPL-2.0-or-later
 * @link       https://yoursite.com/price-by-country
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
```

### Class Documentation

```php
/**
 * PBC Pricing Engine Class
 *
 * Handles all pricing calculations and rule hierarchy management for the
 * Price by Country plugin. This class is responsible for:
 * - Calculating country-specific price adjustments
 * - Managing pricing rule hierarchy (product > category > global)
 * - Caching price calculations for performance
 * - Batch processing multiple products
 *
 * @package    PriceByCountry
 * @subpackage Core
 * @since      1.0.0
 * <AUTHOR> Name <<EMAIL>>
 *
 * @property PBC_Database         $database         Database operations instance
 * @property PBC_Country_Detector $country_detector Country detection service
 * @property PBC_Error_Handler    $error_handler    Error handling instance
 * @property PBC_Logger          $logger           Logging service instance
 */
class PBC_Pricing_Engine {
```

### Constants Documentation

```php
/**
 * Rule priority hierarchy (higher number = higher priority)
 *
 * Defines the order in which pricing rules are applied:
 * - Product rules (3) override category and global rules
 * - Category rules (2) override global rules
 * - Global rules (1) are the fallback
 *
 * @since 1.0.0
 * @var array<string, int>
 */
const RULE_PRIORITY = [
    'global' => 1,
    'category' => 2,
    'product' => 3
];

/**
 * Cache key prefix for price calculations
 *
 * Used to generate unique cache keys for storing calculated prices.
 * Format: pbc_price_{product_id}_{country_code}
 *
 * @since 1.0.0
 * @var string
 */
const CACHE_PREFIX = 'pbc_price_';

/**
 * Cache expiration time in seconds (30 minutes)
 *
 * Determines how long calculated prices are cached before
 * being recalculated. Balances performance with data freshness.
 *
 * @since 1.0.0
 * @var int
 */
const CACHE_EXPIRATION = 1800;
```

## Class Documentation

### Property Documentation

```php
/**
 * Database instance for pricing rule operations
 *
 * Handles all database interactions including CRUD operations
 * for pricing rules and country cache data.
 *
 * @since 1.0.0
 * @var PBC_Database
 */
private $database;

/**
 * Country detector instance for location identification
 *
 * Provides country detection functionality using multiple methods
 * including IP geolocation, billing address, and shipping address.
 *
 * @since 1.0.0
 * @var PBC_Country_Detector
 */
private $country_detector;

/**
 * Error handler instance for graceful error management
 *
 * Handles exceptions and errors that occur during pricing calculations,
 * providing fallback mechanisms and logging capabilities.
 *
 * @since 1.0.0
 * @var PBC_Error_Handler
 */
private $error_handler;

/**
 * Logger instance for debugging and audit trails
 *
 * Provides comprehensive logging functionality for price calculations,
 * country detection, and system events.
 *
 * @since 1.0.0
 * @var PBC_Logger
 */
private $logger;
```

### Constructor Documentation

```php
/**
 * Constructor - Initialize pricing engine with dependencies
 *
 * Sets up the pricing engine with required dependencies and initializes
 * error handling and logging services.
 *
 * @since 1.0.0
 *
 * @param PBC_Database         $database         Database operations instance
 * @param PBC_Country_Detector $country_detector Country detection service
 *
 * @throws InvalidArgumentException If required dependencies are not provided
 */
public function __construct($database, $country_detector) {
    if (!$database instanceof PBC_Database) {
        throw new InvalidArgumentException('Database instance required');
    }

    if (!$country_detector instanceof PBC_Country_Detector) {
        throw new InvalidArgumentException('Country detector instance required');
    }

    $this->database = $database;
    $this->country_detector = $country_detector;
    $this->error_handler = PBC_Error_Handler::get_instance();
    $this->logger = PBC_Logger::get_instance();
}
```

## Method Documentation

### Public Method Documentation

```php
/**
 * Get price adjustment for a product in a specific country
 *
 * Calculates the adjusted price for a product based on country-specific
 * pricing rules. Uses rule hierarchy (product > category > global) to
 * determine the applicable adjustment.
 *
 * @since 1.0.0
 *
 * @param int         $product_id   Product ID to calculate pricing for
 * @param string|null $country_code ISO 2-letter country code (optional, will detect if not provided)
 * @param float|null  $base_price   Base price to adjust (optional, will get from product if not provided)
 *
 * @return array {
 *     Price calculation result with adjustment details
 *
 *     @type float  $original_price    Original product price before adjustment
 *     @type float  $adjusted_price    Final price after country adjustment
 *     @type float  $adjustment_amount Difference between original and adjusted price
 *     @type string $adjustment_type   Type of adjustment ('fixed', 'percentage', or 'none')
 *     @type string $country_code      Country code used for calculation
 *     @type string $rule_source       Source of pricing rule ('product', 'category', 'global', or 'none')
 *     @type string $currency_code     Currency code from WooCommerce settings
 *     @type int    $calculated_at     Unix timestamp when calculation was performed
 * }
 *
 * @throws InvalidArgumentException If product ID is invalid
 * @throws Exception               If pricing calculation fails
 *
 * @example
 * // Get Canadian pricing for product 123
 * $result = $pricing_engine->get_price_adjustment(123, 'CA');
 * echo "Original: $" . $result['original_price'];
 * echo "Canadian: $" . $result['adjusted_price'];
 *
 * @example
 * // Auto-detect country and use custom base price
 * $result = $pricing_engine->get_price_adjustment(123, null, 99.99);
 */
public function get_price_adjustment($product_id, $country_code = null, $base_price = null) {
    try {
        $product_id = intval($product_id);

        // Validate product ID
        if ($product_id <= 0) {
            throw new InvalidArgumentException('Invalid product ID provided');
        }

        // Detect country if not provided
        if (!$country_code) {
            $country_code = $this->country_detector->detect_country();
        }

        // Validate country code
        if (!$this->country_detector->is_valid_country_code($country_code)) {
            return $this->create_price_result($base_price, $base_price, 0, 'none', $country_code, 'none');
        }

        // Check cache first
        $cached_result = $this->get_cached_price($product_id, $country_code);
        if ($cached_result !== false) {
            return $cached_result;
        }

        // Get base price if not provided
        if ($base_price === null) {
            $base_price = $this->get_product_base_price($product_id);
            if ($base_price === false) {
                throw new Exception('Unable to retrieve product base price');
            }
        }

        // Find applicable pricing rule using hierarchy
        $rule = $this->find_applicable_rule($product_id, $country_code);

        if (!$rule) {
            // No rule found, return original price
            $result = $this->create_price_result($base_price, $base_price, 0, 'none', $country_code, 'none');
        } else {
            // Calculate adjusted price
            $adjusted_price = $this->calculate_adjusted_price($base_price, $rule);
            $adjustment_amount = $adjusted_price - $base_price;

            $result = $this->create_price_result(
                $base_price,
                $adjusted_price,
                $adjustment_amount,
                $rule->adjustment_type,
                $country_code,
                $rule->rule_type
            );
        }

        // Cache the result
        $this->cache_price_result($product_id, $country_code, $result);

        // Log the price calculation
        $this->logger->log_price_calculation($product_id, $country_code, $result);

        return $result;

    } catch (Exception $e) {
        // Handle pricing calculation error
        return $this->error_handler->handle_pricing_error($e, [
            'product_id' => $product_id,
            'country_code' => $country_code,
            'base_price' => $base_price,
            'function' => __FUNCTION__
        ]);
    }
}
```

### Private Method Documentation

```php
/**
 * Find applicable pricing rule using hierarchy (product > category > global)
 *
 * Searches for the most specific pricing rule that applies to the given
 * product and country combination. Uses caching to improve performance.
 *
 * @since 1.0.0
 * @access private
 *
 * @param int    $product_id   Product ID to find rules for
 * @param string $country_code ISO 2-letter country code
 *
 * @return object|null Pricing rule object or null if no rule found
 *
 * @example
 * // Find rule for product 123 in Canada
 * $rule = $this->find_applicable_rule(123, 'CA');
 * if ($rule) {
 *     echo "Found {$rule->rule_type} rule with {$rule->adjustment_value}% adjustment";
 * }
 */
private function find_applicable_rule($product_id, $country_code) {
    try {
        // Check cache first
        $cached_rule = $this->get_cached_rule($product_id, $country_code);
        if ($cached_rule !== false) {
            return $cached_rule;
        }

        // 1. Check for product-specific rule (highest priority)
        $product_rule = $this->database->get_pricing_rule_by_criteria('product', $product_id, $country_code);
        if ($product_rule) {
            $this->cache_rule($product_id, $country_code, $product_rule);
            return $product_rule;
        }

        // 2. Check for category rules (medium priority)
        $category_rule = $this->find_category_rule($product_id, $country_code);
        if ($category_rule) {
            $this->cache_rule($product_id, $country_code, $category_rule);
            return $category_rule;
        }

        // 3. Check for global rule (lowest priority)
        $global_rule = $this->database->get_pricing_rule_by_criteria('global', null, $country_code);
        if ($global_rule) {
            $this->cache_rule($product_id, $country_code, $global_rule);
            return $global_rule;
        }

        // Cache null result to prevent repeated database queries
        $this->cache_rule($product_id, $country_code, null);
        return null;

    } catch (Exception $e) {
        $this->error_handler->handle_pricing_error($e, [
            'product_id' => $product_id,
            'country_code' => $country_code,
            'function' => __FUNCTION__
        ]);
        return null;
    }
}
```

### Utility Method Documentation

```php
/**
 * Calculate adjusted price based on pricing rule
 *
 * Applies the pricing rule adjustment to the base price using either
 * fixed amount or percentage-based calculations.
 *
 * @since 1.0.0
 * @access private
 *
 * @param float  $base_price Base price before adjustment
 * @param object $rule       Pricing rule object containing adjustment details
 *
 * @return float Adjusted price (minimum 0)
 *
 * @example
 * // Apply 15% discount rule
 * $rule = (object) ['adjustment_type' => 'percentage', 'adjustment_value' => -15.0];
 * $adjusted = $this->calculate_adjusted_price(100.00, $rule); // Returns 85.00
 *
 * @example
 * // Apply $5 fixed increase
 * $rule = (object) ['adjustment_type' => 'fixed', 'adjustment_value' => 5.0];
 * $adjusted = $this->calculate_adjusted_price(100.00, $rule); // Returns 105.00
 */
private function calculate_adjusted_price($base_price, $rule) {
    $base_price = floatval($base_price);
    $adjustment_value = floatval($rule->adjustment_value);

    switch ($rule->adjustment_type) {
        case 'fixed':
            // Fixed amount adjustment (can be positive or negative)
            return max(0, $base_price + $adjustment_value);

        case 'percentage':
            // Percentage adjustment (e.g., -15 = 15% discount, 20 = 20% increase)
            $multiplier = 1 + ($adjustment_value / 100);
            return max(0, $base_price * $multiplier);

        default:
            // Unknown adjustment type - log error and return original price
            error_log("PBC: Unknown adjustment type: {$rule->adjustment_type}");
            return $base_price;
    }
}
```

## Property Documentation

### Static Properties

```php
/**
 * Single instance of the pricing engine (singleton pattern)
 *
 * Ensures only one instance of the pricing engine exists throughout
 * the application lifecycle to maintain consistency and performance.
 *
 * @since 1.0.0
 * @access private
 * @static
 * @var PBC_Pricing_Engine|null
 */
private static $instance = null;
```

### Array Properties

```php
/**
 * Cache of recently calculated prices
 *
 * Stores price calculations in memory to avoid repeated database
 * queries during the same request. Keyed by product_id_country_code.
 *
 * @since 1.0.0
 * @access private
 * @var array<string, array> {
 *     @type string $key   Format: "{product_id}_{country_code}"
 *     @type array  $value Price calculation result array
 * }
 */
private $price_cache = [];

/**
 * Supported adjustment types and their validation rules
 *
 * Defines the valid adjustment types and their constraints for
 * pricing rule validation.
 *
 * @since 1.0.0
 * @access private
 * @var array<string, array> {
 *     @type string $type        Adjustment type identifier
 *     @type array  $constraints {
 *         @type float $min_value Minimum allowed value
 *         @type float $max_value Maximum allowed value
 *         @type string $unit     Unit description for display
 *     }
 * }
 */
private $adjustment_types = [
    'fixed' => [
        'min_value' => -999999.99,
        'max_value' => 999999.99,
        'unit' => 'currency'
    ],
    'percentage' => [
        'min_value' => -100.0,
        'max_value' => 1000.0,
        'unit' => 'percent'
    ]
];
```

## Hook Documentation

### Action Hooks

```php
/**
 * Fires after a price calculation is completed
 *
 * Allows other plugins or themes to react to price calculations,
 * such as logging, analytics, or additional processing.
 *
 * @since 1.0.0
 *
 * @param int    $product_id   Product ID that was calculated
 * @param string $country_code Country code used for calculation
 * @param array  $result       Price calculation result array
 * @param string $rule_source  Source of the pricing rule ('product', 'category', 'global', 'none')
 */
do_action('pbc_price_calculated', $product_id, $country_code, $result, $rule_source);

/**
 * Fires when pricing rules are modified
 *
 * Triggered whenever pricing rules are created, updated, or deleted.
 * Useful for cache invalidation and audit logging.
 *
 * @since 1.0.0
 *
 * @param string   $action     Action performed ('created', 'updated', 'deleted')
 * @param int      $rule_id    ID of the affected pricing rule
 * @param array    $rule_data  Rule data array
 * @param int|null $user_id    ID of user who made the change (null for system changes)
 */
do_action('pbc_pricing_rule_changed', $action, $rule_id, $rule_data, $user_id);
```

### Filter Hooks

```php
/**
 * Filters the price adjustment result before returning
 *
 * Allows modification of the final price calculation result.
 * Can be used to apply additional business logic or adjustments.
 *
 * @since 1.0.0
 *
 * @param array  $result      Price calculation result array
 * @param int    $product_id  Product ID
 * @param string $country_code Country code
 * @param object|null $rule   Applied pricing rule object (null if no rule)
 *
 * @return array Modified price calculation result
 */
$result = apply_filters('pbc_price_adjustment_result', $result, $product_id, $country_code, $rule);

/**
 * Filters the applicable pricing rule before applying
 *
 * Allows modification or replacement of the pricing rule that
 * would be applied to a product/country combination.
 *
 * @since 1.0.0
 *
 * @param object|null $rule        Pricing rule object or null
 * @param int         $product_id  Product ID
 * @param string      $country_code Country code
 * @param array       $all_rules   All applicable rules found (for context)
 *
 * @return object|null Modified or replacement pricing rule
 */
$rule = apply_filters('pbc_applicable_pricing_rule', $rule, $product_id, $country_code, $all_rules);
```

## Inline Comments

### Complex Logic Comments

```php
/**
 * Handle rule hierarchy and caching logic
 */
private function find_applicable_rule($product_id, $country_code) {
    // First check if we have a cached result to avoid database queries
    $cached_rule = $this->get_cached_rule($product_id, $country_code);
    if ($cached_rule !== false) {
        return $cached_rule;
    }

    // Product-specific rules have the highest priority and override
    // both category and global rules for maximum flexibility
    $product_rule = $this->database->get_pricing_rule_by_criteria('product', $product_id, $country_code);
    if ($product_rule) {
        // Cache the result to improve performance on subsequent calls
        $this->cache_rule($product_id, $country_code, $product_rule);
        return $product_rule;
    }

    // If no product rule exists, check category rules
    // A product can belong to multiple categories, so we need to
    // check all categories and return the first rule found
    $category_rule = $this->find_category_rule($product_id, $country_code);
    if ($category_rule) {
        $this->cache_rule($product_id, $country_code, $category_rule);
        return $category_rule;
    }

    // Global rules are the fallback when no specific rules exist
    // These apply to all products unless overridden
    $global_rule = $this->database->get_pricing_rule_by_criteria('global', null, $country_code);
    if ($global_rule) {
        $this->cache_rule($product_id, $country_code, $global_rule);
        return $global_rule;
    }

    // No rules found - cache null to prevent repeated database queries
    // This is important for performance when many products have no rules
    $this->cache_rule($product_id, $country_code, null);
    return null;
}
```

### Performance-Critical Comments

```php
/**
 * Batch price calculation with optimized database queries
 */
public function get_batch_price_adjustments($product_ids, $country_code = null) {
    // Detect country once for the entire batch to avoid repeated detection
    if (!$country_code) {
        $country_code = $this->country_detector->detect_country();
    }

    // Check if we have a cached batch result first
    // This is especially important for frequently accessed product sets
    $batch_cache_key = self::BATCH_CACHE_PREFIX . md5(implode(',', $product_ids) . '_' . $country_code);
    $cached_batch = get_transient($batch_cache_key);
    if ($cached_batch !== false) {
        return $cached_batch;
    }

    $results = [];
    $uncached_products = [];

    // First pass: check individual caches to minimize database queries
    // This hybrid approach balances cache efficiency with batch processing
    foreach ($product_ids as $product_id) {
        $cached_result = $this->get_cached_price($product_id, $country_code);
        if ($cached_result !== false) {
            $results[$product_id] = $cached_result;
        } else {
            $uncached_products[] = $product_id;
        }
    }

    // Second pass: calculate prices for uncached products
    // Process these individually to maintain error isolation
    foreach ($uncached_products as $product_id) {
        $results[$product_id] = $this->get_price_adjustment($product_id, $country_code);
    }

    // Cache the entire batch result for future requests
    // Use shorter expiration for batch cache as it's more volatile
    set_transient($batch_cache_key, $results, self::BATCH_CACHE_EXPIRATION);

    return $results;
}
```

### Error Handling Comments

```php
/**
 * Robust error handling with graceful degradation
 */
public function get_price_adjustment($product_id, $country_code = null, $base_price = null) {
    try {
        // Input validation is critical for security and stability
        $product_id = intval($product_id);
        if ($product_id <= 0) {
            throw new InvalidArgumentException('Invalid product ID provided');
        }

        // Country detection can fail due to various reasons:
        // - Geolocation service unavailable
        // - Invalid IP address
        // - User data not available
        if (!$country_code) {
            $country_code = $this->country_detector->detect_country();
        }

        // Always validate country codes to prevent database errors
        // and ensure consistent behavior across the application
        if (!$this->country_detector->is_valid_country_code($country_code)) {
            // Return neutral result rather than failing completely
            return $this->create_price_result($base_price, $base_price, 0, 'none', $country_code, 'none');
        }

        // ... rest of the method ...

    } catch (Exception $e) {
        // Use centralized error handling to ensure consistent behavior
        // The error handler will log the error and return a safe fallback
        return $this->error_handler->handle_pricing_error($e, [
            'product_id' => $product_id,
            'country_code' => $country_code,
            'base_price' => $base_price,
            'function' => __FUNCTION__
        ]);
    }
}
```

## Code Examples

### Complete Class Documentation Example

```php
<?php
/**
 * Country detection service for Price by Country plugin
 *
 * Provides multiple methods for detecting customer country including
 * IP geolocation, billing address, and shipping address detection.
 * Implements caching and fallback mechanisms for reliability.
 *
 * @package    PriceByCountry
 * @subpackage Services
 * @since      1.0.0
 * <AUTHOR> Name <<EMAIL>>
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Country Detection Service Class
 *
 * Handles customer country detection using multiple methods with
 * intelligent fallback and caching mechanisms.
 *
 * Detection Priority:
 * 1. Shipping address (most reliable for pricing)
 * 2. Billing address (reliable for registered users)
 * 3. IP geolocation (fallback for anonymous users)
 *
 * @since 1.0.0
 */
class PBC_Country_Detector {

    /**
     * Default country code when detection fails
     *
     * @since 1.0.0
     * @var string
     */
    const DEFAULT_COUNTRY = 'US';

    /**
     * Cache expiration time for country detection (1 hour)
     *
     * @since 1.0.0
     * @var int
     */
    const CACHE_EXPIRATION = 3600;

    /**
     * Session cache key for storing detected country
     *
     * @since 1.0.0
     * @var string
     */
    const SESSION_KEY = 'pbc_detected_country';

    /**
     * Error handler instance
     *
     * @since 1.0.0
     * @var PBC_Error_Handler
     */
    private $error_handler;

    /**
     * Logger instance
     *
     * @since 1.0.0
     * @var PBC_Logger
     */
    private $logger;

    /**
     * Constructor - Initialize country detector
     *
     * Sets up error handling and logging for the country detection service.
     *
     * @since 1.0.0
     */
    public function __construct() {
        $this->error_handler = PBC_Error_Handler::get_instance();
        $this->logger = PBC_Logger::get_instance();
    }

    /**
     * Detect customer country using configured method
     *
     * Uses the configured detection method with fallback to other methods
     * if the primary method fails. Results are cached for performance.
     *
     * @since 1.0.0
     *
     * @param string $method Detection method ('auto', 'ip', 'billing', 'shipping')
     *
     * @return string ISO 2-letter country code
     *
     * @example
     * // Auto-detect using configured priority
     * $country = $detector->detect_country();
     *
     * @example
     * // Force IP-based detection
     * $country = $detector->detect_country('ip');
     */
    public function detect_country($method = 'auto') {
        try {
            // Check session cache first
            $cached_country = $this->get_cached_country();
            if ($cached_country) {
                return $cached_country;
            }

            $detected_country = null;

            if ($method === 'auto') {
                // Use configured priority order
                $priority = $this->get_detection_priority();
                foreach ($priority as $detection_method) {
                    $detected_country = $this->detect_by_method($detection_method);
                    if ($detected_country) {
                        break;
                    }
                }
            } else {
                // Use specific method
                $detected_country = $this->detect_by_method($method);
            }

            // Fallback to default if all methods fail
            if (!$detected_country) {
                $detected_country = $this->get_default_country();
            }

            // Cache the result
            $this->cache_country($detected_country, $method);

            // Log the detection
            $this->logger->log_country_detection($detected_country, $method);

            return $detected_country;

        } catch (Exception $e) {
            // Handle detection error and return safe fallback
            $this->error_handler->handle_country_detection_error($e);
            return $this->get_default_country();
        }
    }

    /**
     * Detect country using specific method
     *
     * @since 1.0.0
     * @access private
     *
     * @param string $method Detection method
     *
     * @return string|null Country code or null if detection fails
     */
    private function detect_by_method($method) {
        switch ($method) {
            case 'ip':
                return $this->get_country_from_ip();
            case 'billing':
                return $this->get_country_from_billing();
            case 'shipping':
                return $this->get_country_from_shipping();
            default:
                return null;
        }
    }

    // ... additional methods with similar documentation ...
}
```

This comprehensive documentation standard ensures that all code is properly documented with:

1. **Clear descriptions** of what each component does
2. **Parameter documentation** with types and descriptions
3. **Return value documentation** with detailed structure
4. **Usage examples** showing how to use the code
5. **Error handling documentation** explaining what can go wrong
6. **Performance considerations** for optimization
7. **Hook documentation** for extensibility
8. **Inline comments** explaining complex logic

Following these standards makes the codebase maintainable, extensible, and easy to understand for other developers.
