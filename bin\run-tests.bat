@echo off
REM Test runner script for Price by Country plugin (Windows)
REM This script sets up the test environment and runs PHPUnit tests

setlocal enabledelayedexpansion

REM Configuration
if "%WP_VERSION%"=="" set WP_VERSION=latest
if "%WP_MULTISITE%"=="" set WP_MULTISITE=0
if "%DB_NAME%"=="" set DB_NAME=pbc_test
if "%DB_USER%"=="" set DB_USER=root
if "%DB_PASS%"=="" set DB_PASS=
if "%DB_HOST%"=="" set DB_HOST=localhost

echo Price by Country - Test Runner
echo ==================================

REM Check if PHPUnit is available
phpunit --version >nul 2>&1
if errorlevel 1 (
    echo Error: PHPUnit is not installed or not in PATH
    echo Please install PHPUnit: https://phpunit.de/getting-started.html
    exit /b 1
)

REM Set environment variables
set WP_TESTS_DIR=%TEMP%\wordpress-tests-lib
set WP_CORE_DIR=%TEMP%\wordpress

echo Environment configured!
echo.

REM Parse command line arguments
set SUITE=
set COVERAGE=false
set VERBOSE=false

:parse_args
if "%1"=="--unit" (
    set SUITE=unit
    shift
    goto parse_args
)
if "%1"=="--integration" (
    set SUITE=integration
    shift
    goto parse_args
)
if "%1"=="--coverage" (
    set COVERAGE=true
    shift
    goto parse_args
)
if "%1"=="--verbose" (
    set VERBOSE=true
    shift
    goto parse_args
)
if "%1"=="--help" (
    echo Usage: %0 [options]
    echo.
    echo Options:
    echo   --unit         Run only unit tests
    echo   --integration  Run only integration tests
    echo   --coverage     Generate code coverage report
    echo   --verbose      Verbose output
    echo   --help         Show this help message
    echo.
    echo Environment variables:
    echo   WP_VERSION     WordPress version ^(default: latest^)
    echo   WP_MULTISITE   Enable multisite ^(default: 0^)
    echo   DB_NAME        Test database name ^(default: pbc_test^)
    echo   DB_USER        Database user ^(default: root^)
    echo   DB_PASS        Database password ^(default: empty^)
    echo   DB_HOST        Database host ^(default: localhost^)
    exit /b 0
)
if not "%1"=="" (
    echo Unknown option: %1
    echo Use --help for usage information
    exit /b 1
)

REM Build PHPUnit command
set PHPUNIT_CMD=phpunit

if "%VERBOSE%"=="true" (
    set PHPUNIT_CMD=!PHPUNIT_CMD! --verbose
)

if "%COVERAGE%"=="true" (
    set PHPUNIT_CMD=!PHPUNIT_CMD! --coverage-html tests/coverage/html --coverage-clover tests/coverage/clover.xml
    echo Code coverage will be generated in tests/coverage/
)

REM Create coverage directory if needed
if "%COVERAGE%"=="true" (
    if not exist "tests\coverage" mkdir tests\coverage
)

REM Run tests
set FAILED=0

if "%SUITE%"=="" (
    REM Run all tests
    echo Running all tests...
    echo.
    
    echo Running Unit Tests...
    phpunit --testsuite=unit --colors=always
    if errorlevel 1 (
        echo Unit Tests failed
        set FAILED=1
    ) else (
        echo Unit Tests passed
    )
    
    echo.
    
    echo Running Integration Tests...
    phpunit --testsuite=integration --colors=always
    if errorlevel 1 (
        echo Integration Tests failed
        set FAILED=1
    ) else (
        echo Integration Tests passed
    )
) else (
    REM Run specific test suite
    if "%SUITE%"=="unit" (
        echo Running Unit Tests...
        phpunit --testsuite=unit --colors=always
        if errorlevel 1 set FAILED=1
    )
    if "%SUITE%"=="integration" (
        echo Running Integration Tests...
        phpunit --testsuite=integration --colors=always
        if errorlevel 1 set FAILED=1
    )
)

echo.
echo ==================================

if %FAILED%==0 (
    echo All tests passed!
    
    if "%COVERAGE%"=="true" (
        echo Coverage report generated in tests/coverage/html/index.html
    )
    
    exit /b 0
) else (
    echo Some tests failed!
    exit /b 1
)