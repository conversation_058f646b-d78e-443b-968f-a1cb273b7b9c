# Design Document

## Overview

The Price by Country for WooCommerce plugin implements a hierarchical pricing system that allows store owners to set different prices based on customer location. The plugin integrates deeply with WooCommerce's existing pricing mechanisms while maintaining compatibility with core functionality and third-party extensions.

The system follows WordPress plugin architecture standards and leverages WooCommerce's hook system for seamless integration. The design prioritizes performance, scalability, and maintainability while providing a user-friendly admin interface.

## Architecture

### Plugin Structure

```
price-by-country-woocommerce/
├── price-by-country.php (Main plugin file)
├── includes/
│   ├── class-pbc-core.php (Core plugin class)
│   ├── class-pbc-pricing-engine.php (Pricing calculation logic)
│   ├── class-pbc-country-detector.php (Country detection service)
│   ├── class-pbc-admin.php (Admin interface controller)
│   ├── class-pbc-api.php (REST API extensions)
│   ├── class-pbc-database.php (Database operations)
│   └── class-pbc-hooks.php (WooCommerce integration hooks)
├── admin/
│   ├── css/ (Admin stylesheets)
│   ├── js/ (Admin JavaScript)
│   └── views/ (Admin template files)
├── public/
│   ├── css/ (Frontend stylesheets)
│   └── js/ (Frontend JavaScript)
└── languages/ (Translation files)
```

### Core Components

#### 1. Pricing Engine (`PBC_Pricing_Engine`)

- Calculates country-specific prices based on rules hierarchy
- Handles percentage and fixed amount adjustments
- Manages price caching for performance
- Integrates with WooCommerce price filters

#### 2. Country Detector (`PBC_Country_Detector`)

- Implements multiple detection methods (IP, billing, shipping)
- Provides fallback mechanisms
- Caches detection results per session
- Handles detection priority logic

#### 3. Admin Controller (`PBC_Admin`)

- Manages admin interface rendering
- Handles form submissions and validation
- Provides AJAX endpoints for dynamic updates
- Manages settings and configuration

#### 4. Database Manager (`PBC_Database`)

- Handles custom table creation and management
- Provides CRUD operations for pricing rules
- Manages data migration and updates
- Optimizes queries for performance

## Components and Interfaces

### Database Schema

#### Table: `wp_pbc_pricing_rules`

```sql
CREATE TABLE wp_pbc_pricing_rules (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    rule_type enum('global','category','product') NOT NULL,
    object_id bigint(20) DEFAULT NULL,
    country_code varchar(2) NOT NULL,
    adjustment_type enum('fixed','percentage') NOT NULL,
    adjustment_value decimal(10,4) NOT NULL,
    is_active tinyint(1) DEFAULT 1,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_rule_lookup (rule_type, object_id, country_code),
    KEY idx_country (country_code),
    KEY idx_active (is_active)
);
```

#### Table: `wp_pbc_country_cache`

```sql
CREATE TABLE wp_pbc_country_cache (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    session_id varchar(255) NOT NULL,
    ip_address varchar(45) NOT NULL,
    country_code varchar(2) NOT NULL,
    detection_method enum('ip','billing','shipping') NOT NULL,
    expires_at datetime NOT NULL,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY idx_session (session_id),
    KEY idx_ip (ip_address),
    KEY idx_expires (expires_at)
);
```

### API Interfaces

#### Pricing Rule Interface

```php
interface PBC_Pricing_Rule_Interface {
    public function get_price_adjustment($product_id, $country_code);
    public function save_rule($rule_data);
    public function delete_rule($rule_id);
    public function get_rules_by_type($type, $object_id = null);
}
```

#### Country Detection Interface

```php
interface PBC_Country_Detector_Interface {
    public function detect_country($method = 'auto');
    public function get_country_from_ip($ip_address);
    public function get_country_from_billing($user_id);
    public function get_country_from_shipping($user_id);
}
```

### WooCommerce Integration Points

#### Price Modification Hooks

- `woocommerce_product_get_price` - Modify regular price
- `woocommerce_product_get_sale_price` - Modify sale price
- `woocommerce_variation_prices` - Handle variable product pricing
- `woocommerce_cart_item_price` - Update cart display prices

#### Admin Integration Hooks

- `woocommerce_product_options_pricing` - Add product-level pricing fields
- `woocommerce_process_product_meta` - Save product pricing rules
- `product_cat_add_form_fields` - Add category pricing fields
- `edited_product_cat` - Save category pricing rules

#### Checkout Integration Hooks

- `woocommerce_checkout_update_order_review` - Recalculate on address change
- `woocommerce_cart_calculate_fees` - Apply country-based adjustments
- `woocommerce_before_calculate_totals` - Ensure price updates

## Data Models

### Pricing Rule Model

```php
class PBC_Pricing_Rule {
    public $id;
    public $rule_type; // 'global', 'category', 'product'
    public $object_id; // null for global, category_id or product_id
    public $country_code; // ISO 2-letter country code
    public $adjustment_type; // 'fixed' or 'percentage'
    public $adjustment_value; // numeric value
    public $is_active; // boolean
    public $created_at;
    public $updated_at;
}
```

### Country Detection Result Model

```php
class PBC_Country_Detection_Result {
    public $country_code;
    public $detection_method; // 'ip', 'billing', 'shipping'
    public $confidence_level; // 'high', 'medium', 'low'
    public $cached; // boolean
    public $detected_at;
}
```

### Price Calculation Result Model

```php
class PBC_Price_Result {
    public $original_price;
    public $adjusted_price;
    public $adjustment_amount;
    public $adjustment_type;
    public $country_code;
    public $rule_source; // 'product', 'category', 'global'
    public $currency_code;
}
```

## Error Handling

### Error Categories

#### 1. Country Detection Errors

- **IP Detection Failure**: Fallback to default country or prompt user
- **Geolocation Service Unavailable**: Use cached data or default pricing
- **Invalid Country Code**: Log error and use default pricing

#### 2. Pricing Calculation Errors

- **Invalid Rule Data**: Skip rule and continue with next priority
- **Database Connection Issues**: Use cached prices or default pricing
- **Circular Rule Dependencies**: Detect and break cycles, log warning

#### 3. Admin Interface Errors

- **Invalid Form Data**: Display validation errors to admin
- **Permission Errors**: Show appropriate access denied messages
- **AJAX Request Failures**: Provide user-friendly error messages

### Error Handling Strategy

```php
class PBC_Error_Handler {
    public function handle_pricing_error($error, $context) {
        // Log error for debugging
        error_log("PBC Pricing Error: " . $error->getMessage());

        // Return safe fallback
        return $this->get_fallback_price($context);
    }

    public function handle_country_detection_error($error) {
        // Use cached country if available
        if ($cached_country = $this->get_cached_country()) {
            return $cached_country;
        }

        // Fallback to store default country
        return get_option('woocommerce_default_country');
    }
}
```

## Testing Strategy

### Unit Testing

- **Pricing Engine Tests**: Verify calculation accuracy for all rule types
- **Country Detection Tests**: Mock different detection scenarios
- **Database Operations Tests**: Test CRUD operations and data integrity
- **Admin Interface Tests**: Validate form processing and validation

### Integration Testing

- **WooCommerce Hook Integration**: Ensure proper price modification
- **Checkout Flow Testing**: Verify real-time price updates
- **REST API Testing**: Validate API responses and data consistency
- **Multi-site Compatibility**: Test plugin behavior in network installations

### Performance Testing

- **Price Calculation Performance**: Benchmark calculation speed with large rule sets
- **Database Query Optimization**: Monitor query performance and caching effectiveness
- **Frontend Load Testing**: Ensure minimal impact on page load times
- **Memory Usage Testing**: Verify efficient memory usage during price calculations

### User Acceptance Testing Scenarios

- **Admin Workflow Testing**: Complete pricing rule setup and management
- **Customer Experience Testing**: Verify accurate price display and updates
- **Edge Case Testing**: Handle unusual country codes, missing data, and conflicts
- **Compatibility Testing**: Ensure compatibility with popular WooCommerce extensions

### Automated Testing Implementation

```php
class PBC_Test_Suite {
    public function test_price_calculation() {
        // Test various pricing scenarios
        $this->assert_price_equals(100, 'US', 100); // No rule
        $this->assert_price_equals(100, 'CA', 85);  // 15% discount
        $this->assert_price_equals(100, 'EU', 110); // €10 increase
    }

    public function test_country_detection() {
        // Mock different detection methods
        $this->mock_ip_detection('***********', 'US');
        $this->assert_country_equals('US');
    }
}
```

## Performance Considerations

### Caching Strategy

- **Rule Caching**: Cache pricing rules in WordPress transients
- **Country Detection Caching**: Store detection results per session
- **Price Calculation Caching**: Cache calculated prices for product/country combinations
- **Database Query Caching**: Utilize WordPress object cache for database queries

### Optimization Techniques

- **Lazy Loading**: Load pricing rules only when needed
- **Batch Processing**: Process multiple price calculations efficiently
- **Database Indexing**: Optimize database queries with proper indexes
- **Minification**: Minimize admin and frontend assets

### Scalability Considerations

- **Large Product Catalogs**: Efficient handling of stores with thousands of products
- **Multiple Countries**: Support for extensive country lists without performance degradation
- **High Traffic**: Minimize database queries during peak traffic periods
- **Memory Management**: Efficient memory usage for large rule sets
