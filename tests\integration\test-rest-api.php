<?php
/**
 * Integration tests for REST API with country pricing
 *
 * @package PriceByCountry
 */

class Test_REST_API extends WP_UnitTestCase {

    /**
     * Test product IDs
     *
     * @var array
     */
    private $test_products = [];

    /**
     * Database instance
     *
     * @var PBC_Database
     */
    private $database;

    /**
     * API instance
     *
     * @var PBC_API
     */
    private $api;

    /**
     * Set up test environment
     */
    public function setUp(): void {
        parent::setUp();

        // Initialize components
        $this->database = new PBC_Database();
        $this->database->init();

        $country_detector = new PBC_Country_Detector();
        $pricing_engine = new PBC_Pricing_Engine($this->database, $country_detector);
        $this->api = new PBC_API($pricing_engine, $country_detector);

        // Create test products
        $this->test_products = [
            'simple' => PBC_Test_Helper::create_test_product('100.00'),
            'variable' => PBC_Test_Helper::create_test_product('150.00')
        ];

        // Set up pricing rules
        $this->setup_pricing_rules();

        // Mock REST API environment
        $this->mock_rest_environment();
    }

    /**
     * Test product price API endpoint with country parameter
     */
    public function test_product_price_api_with_country() {
        $request = new WP_REST_Request('GET', '/pbc/v1/products/' . $this->test_products['simple'] . '/price');
        $request->set_param('country', 'CA');

        $response = $this->api->get_product_price($request);
        $data = $response->get_data();

        $this->assertEquals(200, $response->get_status());
        $this->assertEquals(90.00, $data['adjusted_price']); // 100 - 10
        $this->assertEquals('CA', $data['country_code']);
        $this->assertEquals('fixed', $data['adjustment_type']);
        $this->assertEquals('product', $data['rule_source']);
    }

    /**
     * Test product price API without country (should detect)
     */
    public function test_product_price_api_without_country() {
        // Mock IP-based detection
        $_SERVER['REMOTE_ADDR'] = '***********';
        $_SERVER['HTTP_X_PBC_COUNTRY'] = 'UK';

        $request = new WP_REST_Request('GET', '/pbc/v1/products/' . $this->test_products['simple'] . '/price');

        $response = $this->api->get_product_price($request);
        $data = $response->get_data();

        $this->assertEquals(200, $response->get_status());
        $this->assertEquals(120.00, $data['adjusted_price']); // 100 * 1.2
        $this->assertEquals('UK', $data['country_code']);
    }

    /**
     * Test batch product prices API
     */
    public function test_batch_product_prices_api() {
        $request = new WP_REST_Request('POST', '/pbc/v1/products/batch-prices');
        $request->set_param('product_ids', [$this->test_products['simple'], $this->test_products['variable']]);
        $request->set_param('country', 'CA');

        $response = $this->api->get_batch_product_prices($request);
        $data = $response->get_data();

        $this->assertEquals(200, $response->get_status());
        $this->assertCount(2, $data['products']);

        // Check simple product
        $simple_data = $data['products'][$this->test_products['simple']];
        $this->assertEquals(90.00, $simple_data['adjusted_price']);

        // Check variable product
        $variable_data = $data['products'][$this->test_products['variable']];
        $this->assertEquals(180.00, $variable_data['adjusted_price']); // 150 * 1.2
    }

    /**
     * Test WooCommerce REST API product modification
     */
    public function test_woocommerce_rest_api_product_modification() {
        // Mock WooCommerce REST API request
        $request = new WP_REST_Request('GET', '/wc/v3/products/' . $this->test_products['simple']);
        $request->set_header('X-PBC-Country', 'CA');

        // Mock product data
        $product_data = [
            'id' => $this->test_products['simple'],
            'name' => 'Test Product',
            'price' => '100.00',
            'regular_price' => '100.00',
            'sale_price' => ''
        ];

        $product = wc_get_product($this->test_products['simple']);

        // Apply REST API filter
        $modified_data = apply_filters('woocommerce_rest_prepare_product_object', $product_data, $product, $request);

        // Verify price modification
        $this->assertEquals('90.00', $modified_data['price']);
        $this->assertEquals('100.00', $modified_data['regular_price']); // Should remain unchanged
    }

    /**
     * Test country detection API endpoint
     */
    public function test_country_detection_api() {
        $_SERVER['REMOTE_ADDR'] = '***********00';
        $_SERVER['HTTP_USER_AGENT'] = 'Test Browser';

        $request = new WP_REST_Request('GET', '/pbc/v1/detect-country');

        $response = $this->api->detect_country($request);
        $data = $response->get_data();

        $this->assertEquals(200, $response->get_status());
        $this->assertArrayHasKey('country_code', $data);
        $this->assertArrayHasKey('detection_method', $data);
        $this->assertArrayHasKey('confidence_level', $data);
    }

    /**
     * Test pricing rules API endpoints
     */
    public function test_pricing_rules_api() {
        // Test GET rules
        $request = new WP_REST_Request('GET', '/pbc/v1/pricing-rules');
        $response = $this->api->get_pricing_rules($request);
        $data = $response->get_data();

        $this->assertEquals(200, $response->get_status());
        $this->assertArrayHasKey('rules', $data);
        $this->assertGreaterThan(0, count($data['rules']));

        // Test GET specific rule
        $rule_id = $data['rules'][0]['id'];
        $request = new WP_REST_Request('GET', '/pbc/v1/pricing-rules/' . $rule_id);
        $response = $this->api->get_pricing_rule($request);
        $rule_data = $response->get_data();

        $this->assertEquals(200, $response->get_status());
        $this->assertEquals($rule_id, $rule_data['id']);
    }

    /**
     * Test creating pricing rule via API
     */
    public function test_create_pricing_rule_api() {
        $request = new WP_REST_Request('POST', '/pbc/v1/pricing-rules');
        $request->set_param('rule_type', 'product');
        $request->set_param('object_id', $this->test_products['simple']);
        $request->set_param('country_code', 'JP');
        $request->set_param('adjustment_type', 'percentage');
        $request->set_param('adjustment_value', 25.00);

        $response = $this->api->create_pricing_rule($request);
        $data = $response->get_data();

        $this->assertEquals(201, $response->get_status());
        $this->assertArrayHasKey('id', $data);
        $this->assertEquals('product', $data['rule_type']);
        $this->assertEquals('JP', $data['country_code']);
    }

    /**
     * Test updating pricing rule via API
     */
    public function test_update_pricing_rule_api() {
        // First create a rule
        $rule_id = $this->database->create_pricing_rule([
            'rule_type' => 'product',
            'object_id' => $this->test_products['simple'],
            'country_code' => 'DE',
            'adjustment_type' => 'fixed',
            'adjustment_value' => 15.00,
            'is_active' => 1
        ]);

        // Update the rule
        $request = new WP_REST_Request('PUT', '/pbc/v1/pricing-rules/' . $rule_id);
        $request->set_param('adjustment_value', 20.00);
        $request->set_param('adjustment_type', 'percentage');

        $response = $this->api->update_pricing_rule($request);
        $data = $response->get_data();

        $this->assertEquals(200, $response->get_status());
        $this->assertEquals(20.00, $data['adjustment_value']);
        $this->assertEquals('percentage', $data['adjustment_type']);
    }

    /**
     * Test deleting pricing rule via API
     */
    public function test_delete_pricing_rule_api() {
        // Create a rule to delete
        $rule_id = $this->database->create_pricing_rule([
            'rule_type' => 'global',
            'object_id' => null,
            'country_code' => 'FR',
            'adjustment_type' => 'fixed',
            'adjustment_value' => 5.00,
            'is_active' => 1
        ]);

        $request = new WP_REST_Request('DELETE', '/pbc/v1/pricing-rules/' . $rule_id);
        $response = $this->api->delete_pricing_rule($request);

        $this->assertEquals(200, $response->get_status());

        // Verify rule was deleted
        $deleted_rule = $this->database->get_pricing_rule($rule_id);
        $this->assertNull($deleted_rule);
    }

    /**
     * Test API authentication and permissions
     */
    public function test_api_authentication() {
        // Test without authentication (should fail for write operations)
        $request = new WP_REST_Request('POST', '/pbc/v1/pricing-rules');
        $request->set_param('rule_type', 'product');

        $response = $this->api->create_pricing_rule($request);
        $this->assertEquals(401, $response->get_status());

        // Test with proper authentication
        $user_id = $this->factory->user->create(['role' => 'administrator']);
        wp_set_current_user($user_id);

        $response = $this->api->create_pricing_rule($request);
        $this->assertNotEquals(401, $response->get_status());
    }

    /**
     * Test API rate limiting
     */
    public function test_api_rate_limiting() {
        $request = new WP_REST_Request('GET', '/pbc/v1/products/' . $this->test_products['simple'] . '/price');
        $request->set_param('country', 'US');

        // Make multiple requests rapidly
        $responses = [];
        for ($i = 0; $i < 15; $i++) {
            $responses[] = $this->api->get_product_price($request);
        }

        // Check if rate limiting kicks in
        $last_response = end($responses);
        $this->assertLessThanOrEqual(429, $last_response->get_status());
    }

    /**
     * Test API error handling
     */
    public function test_api_error_handling() {
        // Test with invalid product ID
        $request = new WP_REST_Request('GET', '/pbc/v1/products/99999/price');
        $response = $this->api->get_product_price($request);

        $this->assertEquals(404, $response->get_status());
        $this->assertArrayHasKey('error', $response->get_data());

        // Test with invalid country code
        $request = new WP_REST_Request('GET', '/pbc/v1/products/' . $this->test_products['simple'] . '/price');
        $request->set_param('country', 'INVALID');

        $response = $this->api->get_product_price($request);
        $this->assertEquals(400, $response->get_status());
    }

    /**
     * Test API response caching
     */
    public function test_api_response_caching() {
        $request = new WP_REST_Request('GET', '/pbc/v1/products/' . $this->test_products['simple'] . '/price');
        $request->set_param('country', 'CA');

        // First request
        $start_time = microtime(true);
        $response1 = $this->api->get_product_price($request);
        $time1 = microtime(true) - $start_time;

        // Second request (should be cached)
        $start_time = microtime(true);
        $response2 = $this->api->get_product_price($request);
        $time2 = microtime(true) - $start_time;

        // Cached response should be faster
        $this->assertLessThan($time1, $time2);
        $this->assertEquals($response1->get_data(), $response2->get_data());
    }

    /**
     * Test API with different HTTP methods
     */
    public function test_api_http_methods() {
        $product_id = $this->test_products['simple'];

        // Test OPTIONS request
        $request = new WP_REST_Request('OPTIONS', '/pbc/v1/products/' . $product_id . '/price');
        $response = $this->api->get_product_price($request);
        $this->assertEquals(200, $response->get_status());

        // Test HEAD request
        $request = new WP_REST_Request('HEAD', '/pbc/v1/products/' . $product_id . '/price');
        $response = $this->api->get_product_price($request);
        $this->assertEquals(200, $response->get_status());
    }

    /**
     * Test API with CORS headers
     */
    public function test_api_cors_headers() {
        $_SERVER['HTTP_ORIGIN'] = 'https://example.com';

        $request = new WP_REST_Request('GET', '/pbc/v1/products/' . $this->test_products['simple'] . '/price');
        $response = $this->api->get_product_price($request);

        $headers = $response->get_headers();
        $this->assertArrayHasKey('Access-Control-Allow-Origin', $headers);
        $this->assertArrayHasKey('Access-Control-Allow-Methods', $headers);
    }

    /**
     * Test API pagination
     */
    public function test_api_pagination() {
        // Create multiple pricing rules
        for ($i = 1; $i <= 25; $i++) {
            $this->database->create_pricing_rule([
                'rule_type' => 'product',
                'object_id' => $i,
                'country_code' => 'US',
                'adjustment_type' => 'fixed',
                'adjustment_value' => $i,
                'is_active' => 1
            ]);
        }

        // Test first page
        $request = new WP_REST_Request('GET', '/pbc/v1/pricing-rules');
        $request->set_param('per_page', 10);
        $request->set_param('page', 1);

        $response = $this->api->get_pricing_rules($request);
        $data = $response->get_data();

        $this->assertEquals(200, $response->get_status());
        $this->assertCount(10, $data['rules']);
        $this->assertEquals(1, $data['current_page']);
        $this->assertGreaterThan(2, $data['total_pages']);

        // Test second page
        $request->set_param('page', 2);
        $response = $this->api->get_pricing_rules($request);
        $data = $response->get_data();

        $this->assertCount(10, $data['rules']);
        $this->assertEquals(2, $data['current_page']);
    }

    /**
     * Test API filtering and search
     */
    public function test_api_filtering() {
        $request = new WP_REST_Request('GET', '/pbc/v1/pricing-rules');
        $request->set_param('country', 'CA');
        $request->set_param('rule_type', 'product');

        $response = $this->api->get_pricing_rules($request);
        $data = $response->get_data();

        $this->assertEquals(200, $response->get_status());
        
        // All returned rules should match filters
        foreach ($data['rules'] as $rule) {
            $this->assertEquals('CA', $rule['country_code']);
            $this->assertEquals('product', $rule['rule_type']);
        }
    }

    /**
     * Set up pricing rules for testing
     */
    private function setup_pricing_rules() {
        // Simple product rules
        $this->database->create_pricing_rule([
            'rule_type' => 'product',
            'object_id' => $this->test_products['simple'],
            'country_code' => 'CA',
            'adjustment_type' => 'fixed',
            'adjustment_value' => -10.00,
            'is_active' => 1
        ]);

        $this->database->create_pricing_rule([
            'rule_type' => 'product',
            'object_id' => $this->test_products['simple'],
            'country_code' => 'UK',
            'adjustment_type' => 'percentage',
            'adjustment_value' => 20.00,
            'is_active' => 1
        ]);

        // Variable product rules
        $this->database->create_pricing_rule([
            'rule_type' => 'product',
            'object_id' => $this->test_products['variable'],
            'country_code' => 'CA',
            'adjustment_type' => 'percentage',
            'adjustment_value' => 20.00,
            'is_active' => 1
        ]);

        // Global rule
        $this->database->create_pricing_rule([
            'rule_type' => 'global',
            'object_id' => null,
            'country_code' => 'AU',
            'adjustment_type' => 'fixed',
            'adjustment_value' => 5.00,
            'is_active' => 1
        ]);
    }

    /**
     * Mock REST API environment
     */
    private function mock_rest_environment() {
        // Mock WP_REST_Request if not available
        if (!class_exists('WP_REST_Request')) {
            class WP_REST_Request {
                private $method;
                private $route;
                private $params = [];
                private $headers = [];

                public function __construct($method, $route) {
                    $this->method = $method;
                    $this->route = $route;
                }

                public function set_param($key, $value) {
                    $this->params[$key] = $value;
                }

                public function get_param($key) {
                    return $this->params[$key] ?? null;
                }

                public function set_header($key, $value) {
                    $this->headers[$key] = $value;
                }

                public function get_header($key) {
                    return $this->headers[$key] ?? null;
                }

                public function get_method() {
                    return $this->method;
                }

                public function get_route() {
                    return $this->route;
                }
            }
        }

        // Mock WP_REST_Response if not available
        if (!class_exists('WP_REST_Response')) {
            class WP_REST_Response {
                private $data;
                private $status;
                private $headers = [];

                public function __construct($data = null, $status = 200, $headers = []) {
                    $this->data = $data;
                    $this->status = $status;
                    $this->headers = $headers;
                }

                public function get_data() {
                    return $this->data;
                }

                public function get_status() {
                    return $this->status;
                }

                public function get_headers() {
                    return $this->headers;
                }

                public function set_status($status) {
                    $this->status = $status;
                }

                public function header($key, $value) {
                    $this->headers[$key] = $value;
                }
            }
        }
    }

    /**
     * Clean up after tests
     */
    public function tearDown(): void {
        // Clean up test data
        PBC_Test_Helper::cleanup_test_data();
        
        // Reset current user
        wp_set_current_user(0);
        
        parent::tearDown();
    }
}