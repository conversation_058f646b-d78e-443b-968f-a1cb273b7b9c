# Video Tutorial Scripts for Price by Country Plugin

## Table of Contents

1. [Getting Started Tutorial](#getting-started-tutorial)
2. [Setup Wizard Walkthrough](#setup-wizard-walkthrough)
3. [Creating Your First Pricing Rule](#creating-your-first-pricing-rule)
4. [Advanced Pricing Strategies](#advanced-pricing-strategies)
5. [Import/Export Tutorial](#importexport-tutorial)
6. [Troubleshooting Common Issues](#troubleshooting-common-issues)
7. [Performance Optimization](#performance-optimization)

---

## Getting Started Tutorial

**Duration**: 8-10 minutes  
**Target Audience**: New users, store owners  
**Prerequisites**: WordPress and WooCommerce installed

### Script

**[INTRO - 0:00-0:30]**

"Welcome to Price by Country for WooCommerce! I'm [Name], and in this tutorial, I'll show you how to set up country-specific pricing for your online store in just a few minutes.

By the end of this video, you'll know how to:

- Install and activate the plugin
- Run the setup wizard
- Create your first pricing rule
- Test that everything works correctly

Let's get started!"

**[INSTALLATION - 0:30-2:00]**

"First, let's install the plugin. I'm starting with a fresh WooCommerce store here.

[Screen: WordPress admin dashboard]

1. Go to Plugins → Add New
2. Click 'Upload Plugin'
3. Choose your Price by Country zip file
4. Click 'Install Now'
5. Click 'Activate Plugin'

[Screen shows plugin activation]

Great! Notice that we're automatically redirected to the setup wizard. This makes getting started really easy."

**[SETUP WIZARD - 2:00-4:30]**

"The setup wizard will guide us through the basic configuration. Let's go through each step:

[Screen: Setup wizard welcome page]

**Step 1: Welcome**
This gives us an overview of what the plugin does. Click 'Let's Get Started!'

**Step 2: Country Detection**
[Screen: Country detection settings]

This is important - how should the plugin detect where your customers are located?

- 'Automatic' uses multiple methods - this is recommended for most stores
- 'IP Address Only' is good if you want immediate detection
- 'Billing Address' is most accurate but requires customer input

I'll choose 'Automatic' and set 'United States' as my default country.

**Step 3: Sample Rules**
[Screen: Sample rules creation]

This is really helpful for getting started. The wizard can create example pricing rules for common countries. I'll enable this and choose 'Percentage adjustments' since they're easier to manage.

**Step 4: Complete**
[Screen: Completion page]

Perfect! Our basic setup is complete. The wizard has created sample rules for the UK (+15%), Canada (+5%), and Australia (+20%)."

**[TESTING THE SETUP - 4:30-6:30]**

"Now let's test that everything works. I'll open my store in a new tab.

[Screen: Store frontend]

Currently, I'm seeing US pricing. Let me use a VPN to simulate being in the UK.

[Screen shows VPN connection to UK]

Now I'll refresh the page... Perfect! You can see the prices have increased by 15% as expected. Let me check a specific product:

[Screen: Product page showing UK pricing]

This $100 product is now showing as $115 - exactly what we configured.

Let me test the checkout process too...

[Screen: Add to cart and checkout]

Great! The pricing carries through to checkout correctly."

**[NEXT STEPS - 6:30-8:00]**

"Excellent! Your Price by Country plugin is now working. Here's what you should do next:

1. **Review the sample rules** - Go to WooCommerce → Price by Country to see what was created
2. **Add more countries** - Set up pricing for all your target markets
3. **Test thoroughly** - Use VPN services to test different countries
4. **Monitor performance** - Check your analytics to see how different pricing affects sales

In our next video, I'll show you how to create advanced pricing strategies for different business scenarios.

Thanks for watching, and don't forget to subscribe for more WooCommerce tutorials!"

**[OUTRO - 8:00-8:30]**

"If you have questions, check out our comprehensive documentation or leave a comment below. See you in the next video!"

---

## Setup Wizard Walkthrough

**Duration**: 12-15 minutes  
**Target Audience**: New users wanting detailed setup guidance  
**Prerequisites**: Plugin installed but not configured

### Script

**[INTRO - 0:00-0:45]**

"In this detailed walkthrough, I'll take you through every option in the Price by Country setup wizard. This is perfect if you want to understand all the settings before configuring your store.

We'll cover:

- Each setup wizard step in detail
- When to choose different options
- Best practices for different store types
- Common mistakes to avoid

Let's dive in!"

**[WIZARD OVERVIEW - 0:45-1:30]**

"The setup wizard appears automatically after plugin activation, but you can always access it later from Dashboard → Price by Country Setup.

[Screen: Setup wizard navigation]

The wizard has four main steps:

1. Welcome and overview
2. Country detection configuration
3. Sample rules creation
4. Completion and next steps

Each step builds on the previous one, so it's important to get the early steps right."

**[STEP 1: WELCOME - 1:30-2:30]**

"[Screen: Welcome step]

The welcome step gives you an overview of the plugin's capabilities:

- Automatic country detection
- Flexible pricing rules
- Performance optimization
- Easy management

You can click 'Skip Setup' if you prefer to configure manually, but I recommend going through the wizard - it only takes a few minutes and ensures everything is set up correctly.

Click 'Let's Get Started!' to continue."

**[STEP 2: COUNTRY DETECTION - 2:30-7:00]**

"[Screen: Country detection step]

This is the most important step - how will the plugin detect your customers' countries?

**Automatic Detection (Recommended)**
This uses multiple methods in priority order:

1. Shipping address (if customer has entered it)
2. Billing address (if available)
3. IP address (as fallback)

This gives the best accuracy for returning customers while still working for first-time visitors.

**IP Address Only**
Uses geolocation based on the visitor's IP address.

- Pros: Works immediately for all visitors
- Cons: Can be inaccurate with VPNs or corporate networks
- Best for: Stores with mostly first-time visitors

**Billing Address Only**
Uses the country from the customer's billing address.

- Pros: Most accurate when available
- Cons: Requires customer to enter billing info first
- Best for: B2B stores or high-value items

**Shipping Address Only**
Uses the shipping country.

- Pros: Accurate for physical products
- Cons: Doesn't work for digital products
- Best for: Physical product stores only

[Screen shows selection of 'Automatic']

I'll choose 'Automatic' for the best balance.

**Default Country**
This is crucial - what country should we use if detection fails?
Usually, this should be your store's base country or your largest market.

[Screen shows country selection]

I'll set this to 'United States' since that's where my store is based.

**Cache Duration**
This controls how long we remember a customer's detected country:

- 30 minutes: Very fresh detection, more server load
- 1 hour: Good balance (recommended)
- 2 hours: Less server load, less responsive to changes
- 24 hours: Minimal server load, but customers might see wrong prices if they travel

I'll stick with 1 hour."

**[STEP 3: SAMPLE RULES - 7:00-10:30]**

"[Screen: Sample rules step]

This step can create example pricing rules to get you started quickly.

**Create Sample Rules**
I recommend enabling this - you can always modify or delete these rules later, and they help you understand how the system works.

**Rule Type**

- **Percentage adjustments**: Easier to manage, scale with your prices
- **Fixed amount adjustments**: More precise control, but need updating when base prices change

For most stores, percentage adjustments work better.

**Countries Included**
The wizard will create rules for common international markets:

- United Kingdom: +15% (accounts for VAT and shipping)
- Canada: +5% (similar market, slight shipping premium)
- Australia: +20% (distance and shipping costs)
- European Union: +12% (VAT and market differences)

These are reasonable starting points that you can adjust based on your specific needs.

[Screen shows configuration selection]

I'll enable sample rules with percentage adjustments."

**[STEP 4: COMPLETION - 10:30-12:00]**

"[Screen: Completion step]

Great! The wizard shows us a summary of what was configured:

- Country detection method: Automatic
- Default country: United States
- Sample rules: Created for 4 countries
- Cache duration: 1 hour

**Next Steps**
The completion page gives us several important links:

- **View Dashboard**: See all your pricing rules
- **Settings**: Fine-tune the configuration
- **Documentation**: Comprehensive guides
- **Test Your Store**: Important - always test before going live!

Click 'Go to Dashboard' to see your new pricing rules."

**[REVIEWING THE RESULTS - 12:00-14:00]**

"[Screen: Plugin dashboard]

Perfect! Let's see what the wizard created:

**Global Rules**

- GB (United Kingdom): +15%
- CA (Canada): +5%
- AU (Australia): +20%
- EU (European Union): +12%

These are global rules, meaning they apply to all products unless overridden.

**Settings Summary**

- Detection method: Automatic
- Default country: US
- Cache: 1 hour
- Debug mode: Disabled (good for production)

This is a solid foundation that will work for most stores."

**[TESTING AND NEXT STEPS - 14:00-15:00]**

"Before we finish, let's quickly test the setup:

[Screen: Frontend store with VPN]

I'll use a VPN to simulate being in the UK... Perfect! Prices are showing with the 15% increase.

**Your Next Steps:**

1. **Test thoroughly** with different countries
2. **Adjust the sample rules** based on your business needs
3. **Add more countries** as needed
4. **Set up product-specific rules** for special cases
5. **Monitor performance** and customer feedback

That's it! Your Price by Country plugin is now configured and ready to use."

---

## Creating Your First Pricing Rule

**Duration**: 10-12 minutes  
**Target Audience**: Users ready to create custom pricing rules  
**Prerequisites**: Plugin installed and basic setup complete

### Script

**[INTRO - 0:00-0:30]**

"Now that you have Price by Country installed, let's create your first custom pricing rule. I'll show you the three different types of rules and when to use each one.

By the end of this video, you'll know how to:

- Create global, category, and product-specific rules
- Choose between fixed and percentage adjustments
- Test your rules effectively
- Avoid common mistakes"

**[UNDERSTANDING RULE TYPES - 0:30-2:00]**

"[Screen: Plugin dashboard]

There are three types of pricing rules, and they work in a hierarchy:

**Global Rules** (lowest priority)

- Apply to all products by default
- Good for store-wide adjustments
- Example: +15% for all UK customers

**Category Rules** (medium priority)

- Apply to all products in a category
- Override global rules
- Example: Electronics category gets +20% in Australia

**Product Rules** (highest priority)

- Apply to specific products only
- Override both global and category rules
- Example: Flagship product gets special pricing

The plugin always uses the most specific rule available."

**[CREATING A GLOBAL RULE - 2:00-4:30]**

"Let's start with a global rule. This will be our foundation.

[Screen: WooCommerce → Price by Country → Global Rules]

Click 'Add New Global Rule'

[Screen: New rule form]

**Country Selection**
I'll choose 'Germany' - a major European market.

**Adjustment Type**

- **Fixed Amount**: Add or subtract a specific dollar amount
- **Percentage**: Increase or decrease by a percentage

For Germany, I want to account for VAT and shipping, so I'll use a percentage adjustment.

**Adjustment Value**
I'll enter '12' for a 12% increase. This accounts for:

- Higher VAT rates in Germany
- Shipping costs from the US
- Market positioning

**Priority**
Leave this at 1 for now - we'll cover priorities in advanced tutorials.

**Status**
Make sure this is 'Active'

[Screen shows form completion and save]

Perfect! Now all products will be 12% more expensive for German customers."

**[CREATING A CATEGORY RULE - 4:30-7:00]**

"Now let's create a category rule. Maybe electronics need different pricing due to regulations.

[Screen: Products → Categories]

I'll edit my 'Electronics' category.

[Screen: Category edit page, Price by Country section]

**Enable Price by Country**
Check this box to enable country pricing for this category.

**Inheritance**
This controls how products in this category behave:

- 'Use category rules only': Ignore global rules
- 'Inherit from global, override specific countries': Best of both worlds
- 'Fully inherit from global': Category rules are additions to global rules

I'll choose 'Inherit from global, override specific countries'

**Add Country Rule**
Let's add a rule for Germany that's different from our global rule.

Electronics often have higher compliance costs in Germany, so I'll set this to +18% instead of the global +12%.

[Screen shows rule creation]

Now German customers will see:

- Electronics: +18% (category rule)
- Other products: +12% (global rule)"

**[CREATING A PRODUCT RULE - 7:00-9:30]**

"Finally, let's create a product-specific rule. Maybe we have a flagship product that needs special pricing.

[Screen: Products → Edit Product]

I'll edit my best-selling smartphone.

[Screen: Product edit page, Price by Country tab]

**Enable Price by Country**
Check this box first.

**Inheritance Settings**

- 'No inheritance': Use only product-specific rules
- 'Inherit from category': Use category rules, override specific countries
- 'Inherit from global': Use global rules, override specific countries

I'll choose 'Inherit from category' since I want the electronics category rules as my base.

**Add Country Rule**
For this flagship product, I want aggressive pricing in Germany to compete with local brands.

I'll set Germany to +8% - lower than both the category (+18%) and global (+12%) rules.

[Screen shows rule creation and save]

Now German customers will see:

- This flagship smartphone: +8% (product rule - most specific)
- Other electronics: +18% (category rule)
- Non-electronics: +12% (global rule)"

**[TESTING YOUR RULES - 9:30-11:00]**

"Always test your rules before going live!

[Screen: Store frontend]

Let me test with a VPN set to Germany:

**Flagship Smartphone**

- Base price: $1000
- German price: $1080 (8% increase) ✓

**Regular Electronics Product**

- Base price: $500
- German price: $590 (18% increase) ✓

**Non-Electronics Product**

- Base price: $100
- German price: $112 (12% increase) ✓

Perfect! All our rules are working correctly."

**[COMMON MISTAKES TO AVOID - 11:00-12:00]**

"Here are the most common mistakes I see:

1. **Forgetting to activate rules** - Always check the 'Active' status
2. **Not understanding inheritance** - Remember: Product > Category > Global
3. **Conflicting rules** - Use the priority system to resolve conflicts
4. **Not testing** - Always test with VPN or browser tools
5. **Ignoring caching** - Clear caches after making changes

**Pro Tips:**

- Start with global rules, then add exceptions
- Use percentage adjustments for easier management
- Document your pricing strategy
- Monitor customer feedback and conversion rates

That's it! You now know how to create all three types of pricing rules. In the next video, I'll show you advanced strategies for complex business scenarios."

---

## Advanced Pricing Strategies

**Duration**: 15-18 minutes  
**Target Audience**: Experienced users, business owners with complex needs  
**Prerequisites**: Basic rules created, understanding of plugin fundamentals

### Script

**[INTRO - 0:00-0:45]**

"Welcome to advanced pricing strategies! In this tutorial, I'll show you how to implement sophisticated pricing models that go beyond basic country adjustments.

We'll cover:

- Multi-tier regional pricing
- Seasonal and promotional strategies
- Currency risk management
- B2B vs B2C pricing
- Performance optimization for complex rules

This is perfect if you're running a larger store or have complex international requirements."

**[REGIONAL PRICING STRATEGY - 0:45-4:00]**

"Instead of setting prices country by country, let's create regional pricing tiers.

[Screen: Global rules dashboard]

**Tier 1: Premium Markets**
These are developed markets where customers can pay premium prices:

- Switzerland: +25%
- Norway: +22%
- Denmark: +20%
- Germany: +15%

**Tier 2: Standard Markets**
Balanced pricing for competitive markets:

- United Kingdom: +12%
- France: +10%
- Canada: +8%
- Australia: +18% (shipping premium)

**Tier 3: Growth Markets**
Aggressive pricing for market penetration:

- Mexico: -10%
- Brazil: -15%
- India: -25%
- South Africa: -20%

[Screen shows creation of multiple global rules]

This gives us a coherent global strategy rather than random country adjustments."

**[CATEGORY-BASED REGIONAL STRATEGY - 4:00-7:30]**

"Different product categories might need different regional strategies.

[Screen: Category management]

**Electronics Category**
High shipping costs and regulations affect electronics differently:

- Premium markets: +20% (compliance costs)
- Standard markets: +15% (shipping)
- Growth markets: -10% (competitive pressure)

**Fashion Category**  
Fashion is more sensitive to local trends and competition:

- Premium markets: +15% (brand positioning)
- Standard markets: +8% (competitive)
- Growth markets: -20% (market penetration)

**Digital Products Category**
No shipping, different value perception:

- Premium markets: +10% (purchasing power)
- Standard markets: +5% (competitive)
- Growth markets: -15% (affordability)

[Screen shows category rule creation for each]

This creates a sophisticated matrix of pricing that reflects real market conditions."

**[SEASONAL PRICING INTEGRATION - 7:30-10:30]**

"Let's add seasonal pricing on top of our country rules.

[Screen: WordPress admin, showing custom code]

Since the plugin doesn't have built-in seasonal features, we'll use WordPress hooks:

```php
// Seasonal adjustment hook
add_filter('pbc_calculate_price', 'seasonal_price_adjustment', 20, 3);

function seasonal_price_adjustment($price, $product, $country) {
    // Get current season
    $month = date('n');
    $is_winter_collection = in_array($month, [10, 11, 12, 1, 2]);

    // Opposite seasons for Southern Hemisphere
    $southern_hemisphere = ['AU', 'NZ', 'ZA', 'CL', 'AR', 'BR'];

    if (in_array($country, $southern_hemisphere)) {
        $is_winter_collection = !$is_winter_collection;
    }

    // Apply seasonal adjustments
    if ($is_winter_collection && has_term('winter-collection', 'product_cat', $product->get_id())) {
        // Winter items are full price in winter, discounted in summer
        return $price;
    } elseif (!$is_winter_collection && has_term('winter-collection', 'product_cat', $product->get_id())) {
        // 30% discount for winter items in summer
        return $price * 0.7;
    }

    return $price;
}
```

[Screen shows code implementation]

This automatically adjusts pricing based on seasons and hemispheres!"

**[CURRENCY RISK MANAGEMENT - 10:30-13:00]**

"For stores dealing with volatile currencies, we need risk management.

[Screen: Advanced global rules]

**High Volatility Countries**
Add currency risk buffers:

- Turkey: Base adjustment +5%, Currency buffer +8% = +13%
- Brazil: Base adjustment -15%, Currency buffer +5% = -10%
- Argentina: Base adjustment -20%, Currency buffer +10% = -10%

**Automated Adjustment System**
[Screen: Custom code example]

```php
// Currency risk monitoring
add_action('wp_scheduled_delete', 'check_currency_rates');

function check_currency_rates() {
    $rates = get_exchange_rates(); // Your API call

    // Check if rates exceed tolerance
    if ($rates['USD_TRY'] > 25) {
        // Increase Turkey pricing by 5%
        update_country_adjustment('TR', 'increase', 5);
    }

    if ($rates['USD_BRL'] > 5.5) {
        // Increase Brazil pricing by 3%
        update_country_adjustment('BR', 'increase', 3);
    }
}
```

This provides automatic protection against currency volatility."

**[B2B VS B2C PRICING - 13:00-15:30]**

"Different customer types need different international pricing.

[Screen: User role management]

**B2B Customers**
Typically get:

- Lower margins due to volume
- Different regional strategies
- Currency risk considerations

**Implementation**
[Screen: Custom code]

```php
// B2B pricing adjustment
add_filter('pbc_calculate_price', 'b2b_country_pricing', 15, 3);

function b2b_country_pricing($price, $product, $country) {
    if (!is_user_logged_in()) return $price;

    $user = wp_get_current_user();
    if (in_array('wholesale_customer', $user->roles)) {
        // B2B gets different country adjustments
        $b2b_adjustments = [
            'GB' => 0.08,  // 8% instead of 15%
            'DE' => 0.06,  // 6% instead of 12%
            'CA' => 0.03,  // 3% instead of 8%
        ];

        if (isset($b2b_adjustments[$country])) {
            return $price * (1 + $b2b_adjustments[$country]);
        }
    }

    return $price;
}
```

This gives B2B customers different international pricing structures."

**[PERFORMANCE OPTIMIZATION - 15:30-17:30]**

"Complex pricing rules can impact performance. Let's optimize:

[Screen: Plugin performance settings]

**Caching Strategy**

1. **Enable Object Caching**: Use Redis or Memcached
2. **Increase Cache Duration**: 2-4 hours for stable rules
3. **Preload Common Rules**: Load frequently used rules into memory

**Database Optimization**
[Screen: Database management]

```sql
-- Add indexes for complex queries
ALTER TABLE wp_pbc_pricing_rules
ADD INDEX idx_country_category (country_code, rule_type, object_id);

-- Optimize rule lookup queries
ALTER TABLE wp_pbc_pricing_rules
ADD INDEX idx_active_priority (is_active, priority DESC);
```

**Rule Optimization**

- Consolidate similar rules
- Use fewer, more general rules when possible
- Remove unused or expired rules
- Monitor query performance

[Screen: Performance monitoring dashboard]

With these optimizations, even complex rule sets perform well."

**[MONITORING AND ANALYTICS - 17:30-18:00]**

"Finally, let's set up monitoring for our advanced pricing:

**Key Metrics to Track**

- Conversion rates by country and pricing tier
- Average order value by region
- Customer acquisition cost by market
- Margin impact of pricing strategies

**A/B Testing**
Test different pricing strategies:

- Regional vs country-specific pricing
- Different adjustment percentages
- Seasonal timing variations

**Regular Reviews**

- Monthly: High-volatility markets
- Quarterly: Standard markets
- Annually: Overall strategy review

This ensures your advanced pricing strategy continues to deliver results.

That's advanced pricing strategies! These techniques will help you build sophisticated, profitable international pricing that adapts to market conditions."

---

## Import/Export Tutorial

**Duration**: 8-10 minutes  
**Target Audience**: Users managing large numbers of pricing rules  
**Prerequisites**: Basic understanding of CSV files and spreadsheets

### Script

**[INTRO - 0:00-0:30]**

"Managing pricing rules one by one is fine for small stores, but what if you have hundreds of products and dozens of countries? In this tutorial, I'll show you how to use the import/export feature to manage pricing rules in bulk.

Perfect for:

- Large product catalogs
- Multiple country rollouts
- Seasonal pricing updates
- Migrating from other systems"

**[UNDERSTANDING THE CSV FORMAT - 0:30-2:30]**

"[Screen: Sample CSV file in Excel/Google Sheets]

The import/export system uses CSV files with these columns:

**Required Columns:**

- `rule_type`: global, category, or product
- `country_code`: Two-letter code (US, GB, CA, etc.)
- `adjustment_type`: fixed or percentage
- `adjustment_value`: The adjustment amount
- `is_active`: 1 for active, 0 for inactive

**Optional Columns:**

- `object_id`: Category ID or Product ID (empty for global rules)
- `priority`: Rule priority (higher numbers win)
- `notes`: Your internal notes

[Screen shows example data]

Here's what a typical file looks like:

```
rule_type,object_id,country_code,adjustment_type,adjustment_value,is_active
global,,GB,percentage,15,1
global,,CA,percentage,5,1
category,25,AU,percentage,20,1
product,150,DE,fixed,-10,1
```

This creates global rules for GB and CA, a category rule for Australia, and a product-specific rule for Germany."

**[EXPORTING EXISTING RULES - 2:30-4:00]**

"Let's start by exporting your current rules to see the format.

[Screen: WooCommerce → Price by Country → Import/Export]

**Export Options:**

- **All Rules**: Everything in your system
- **Global Rules Only**: Just store-wide rules
- **Category Rules Only**: Category-specific rules
- **Product Rules Only**: Product-specific rules
- **By Country**: Rules for specific countries
- **Active Rules Only**: Skip disabled rules

I'll export all rules to get a complete backup.

[Screen shows export process]

Click 'Export Rules' and save the CSV file. This is also a great backup before making bulk changes!

[Screen: Downloaded CSV file opened in Excel]

Perfect! Here are all my current rules in spreadsheet format. I can see:

- 5 global rules for different countries
- 3 category rules for electronics
- 12 product-specific rules

This makes it easy to review and plan changes."

**[PREPARING BULK CHANGES - 4:00-6:00]**

"Now let's say I want to expand to 10 new European countries. Instead of creating rules one by one, I'll use the spreadsheet.

[Screen: Excel/Google Sheets with exported data]

**Adding New Countries:**
I'll copy the existing GB rule and modify it for other EU countries:

```
global,,FR,percentage,12,1
global,,DE,percentage,12,1
global,,IT,percentage,10,1
global,,ES,percentage,8,1
global,,NL,percentage,14,1
```

**Bulk Price Adjustments:**
Maybe I need to increase all my Australian pricing by 3% due to shipping cost increases. I can:

1. Filter for AU rules
2. Adjust all the values
3. Re-import

**Seasonal Updates:**
For winter collection launch, I might add temporary rules:

```
category,15,GB,percentage,-20,1
category,15,DE,percentage,-20,1
category,15,FR,percentage,-20,1
```

This gives 20% off winter items in Europe during summer."

**[IMPORTING THE CHANGES - 6:00-8:00]**

"Now let's import our changes.

[Screen: Import/Export page, Import tab]

**Step 1: Upload File**
Click 'Choose File' and select your modified CSV.

**Step 2: Column Mapping**
The system will try to auto-detect columns, but verify:

- rule_type → Rule Type
- country_code → Country Code
- adjustment_value → Adjustment Value
- etc.

[Screen shows column mapping interface]

**Step 3: Import Options**

- **Update existing rules**: Modify rules that already exist
- **Skip duplicates**: Don't import rules that already exist
- **Replace all**: Delete existing rules and import new ones

I'll choose 'Update existing rules' to be safe.

**Step 4: Preview**
[Screen: Import preview]

The system shows what will be imported:

- 10 new global rules (European countries)
- 3 updated category rules (Australian adjustments)
- 0 errors detected

**Step 5: Import**
Click 'Import Rules' to process the file.

[Screen: Import success message]

Success! 13 rules imported, 0 errors. The bulk update is complete."

**[VERIFICATION AND TESTING - 8:00-9:30]**

"Always verify your imports worked correctly.

[Screen: Global rules dashboard]

I can see all my new European country rules are here with the correct percentages.

[Screen: Category rules]

The Australian category adjustments are updated.

**Testing:**
Let me test a few countries with VPN:

- France: Showing 12% increase ✓
- Germany: Showing 12% increase ✓
- Australia: Electronics showing updated pricing ✓

Perfect! The bulk import saved me hours of manual work."

**[BEST PRACTICES - 9:30-10:00]**

"**Import/Export Best Practices:**

1. **Always backup first** - Export before importing changes
2. **Test with small batches** - Import 5-10 rules first to verify format
3. **Use consistent formatting** - Keep country codes and types consistent
4. **Validate your data** - Check for typos and invalid values
5. **Document changes** - Use the notes column for tracking
6. **Schedule regular exports** - Monthly backups of all rules

**Common Mistakes:**

- Wrong country codes (use GB, not UK)
- Mixing adjustment types in one import
- Forgetting to set is_active to 1
- Not mapping columns correctly

That's bulk import/export! This feature makes managing large-scale pricing strategies much more efficient."

---

## Troubleshooting Common Issues

**Duration**: 12-15 minutes  
**Target Audience**: Users experiencing problems  
**Prerequisites**: Plugin installed, some familiarity with WordPress admin

### Script

**[INTRO - 0:00-0:30]**

"Running into issues with Price by Country? Don't worry - in this tutorial, I'll walk you through the most common problems and their solutions.

We'll cover:

- Prices not changing based on country
- Admin interface problems
- Performance issues
- Country detection failures
- Import/export problems

Let's troubleshoot together!"

**[ISSUE 1: PRICES NOT CHANGING - 0:30-4:00]**

"This is the most common issue. Let's diagnose it step by step.

[Screen: Store frontend showing unchanged prices]

**Step 1: Check Country Detection**
First, let's see what country is being detected.

[Screen: WordPress admin, plugin debug settings]

Go to WooCommerce → Settings → Price by Country → Debug
Enable 'Debug Mode' and set log level to 'Debug'

[Screen: Store frontend with debug info]

Now when I visit the store, I can see in the browser console:
'PBC: Detected country: US'

But I'm using a UK VPN! This tells us country detection isn't working.

**Step 2: Verify Detection Method**
[Screen: Plugin settings, Detection tab]

Check your detection method:

- If using 'IP Address Only', make sure geolocation is enabled in WooCommerce
- If using 'Automatic', check the priority order
- Make sure default country is set correctly

I'll switch to 'IP Address Only' and test again.

[Screen: Frontend with VPN]

Now it shows 'PBC: Detected country: GB' - much better!

**Step 3: Check Rule Existence**
[Screen: Global rules dashboard]

Do you have rules for the country you're testing? I can see I have a GB rule for +15%.

**Step 4: Verify Rule Status**
Make sure the rule is:

- Active (green checkmark)
- Has correct adjustment type and value
- Priority is set appropriately

**Step 5: Clear Caches**
[Screen: Plugin settings, Performance tab]

Click 'Clear All Caches' - this is crucial after making changes.

[Screen: Frontend test]

Perfect! Now UK visitors see 15% higher prices."

**[ISSUE 2: ADMIN INTERFACE NOT LOADING - 4:00-7:00]**

"If the admin interface isn't working properly:

[Screen: Broken admin interface]

**Step 1: Check JavaScript Errors**
Open browser developer tools (F12) and check the Console tab.

[Screen: Browser console showing errors]

I see: 'Uncaught ReferenceError: pbc_admin is not defined'

This suggests a JavaScript loading problem.

**Step 2: Plugin Conflicts**
Deactivate other plugins one by one to find conflicts.

[Screen: Plugins page]

I'll start with other pricing or WooCommerce plugins...

After deactivating 'Dynamic Pricing Pro', the interface loads correctly! This is a plugin conflict.

**Step 3: Theme Conflicts**
If plugin deactivation doesn't help, try switching to a default theme temporarily.

[Screen: Theme switcher]

Switch to Twenty Twenty-Three and test...

**Step 4: Check User Permissions**
Make sure your user has administrator privileges:

[Screen: User profile]

Go to Users → Your Profile and verify role is 'Administrator'

**Step 5: File Permissions**
Check that WordPress can load the plugin files:

[Screen: File manager or FTP]

Plugin files should have 644 permissions, directories should have 755.

**Step 6: Memory Limits**
If the interface is slow or timing out:

[Screen: Site Health]

Go to Tools → Site Health → Info → Server
Check PHP memory limit - should be at least 256MB for complex stores."

**[ISSUE 3: PERFORMANCE PROBLEMS - 7:00-10:00]**

"If your site is slow after installing the plugin:

[Screen: Slow loading page]

**Step 1: Enable Performance Monitoring**
[Screen: Plugin debug settings]

Enable debug mode and check the performance logs:

[Screen: Debug logs]

I can see:

- 15 database queries per page (should be <5)
- 150ms additional load time (should be <50ms)

This indicates optimization is needed.

**Step 2: Enable Caching**
[Screen: Plugin performance settings]

Make sure caching is enabled:

- Enable Rule Caching: Yes
- Cache Duration: 1 hour (or longer for stable rules)
- Enable Object Cache: Yes (if available)

**Step 3: Optimize Rules**
[Screen: Rules dashboard]

Look for optimization opportunities:

- Consolidate similar rules
- Remove unused rules
- Use global rules instead of many product rules

I had 50 product rules that could be replaced with 3 category rules!

**Step 4: Database Optimization**
[Screen: Database management plugin]

Run database optimization:

- Optimize tables
- Remove old cache entries
- Update database indexes

**Step 5: Check Query Performance**
[Screen: Query monitor plugin]

Use Query Monitor plugin to identify slow queries:

- Look for queries taking >100ms
- Check for missing indexes
- Identify N+1 query problems

After optimization:

- 3 database queries per page ✓
- 25ms additional load time ✓

Much better!"

**[ISSUE 4: COUNTRY DETECTION FAILURES - 10:00-12:30]**

"If country detection isn't working reliably:

[Screen: Debug logs showing detection failures]

**Common Causes:**

**VPN/Proxy Issues**
Many customers use VPNs, which can confuse IP-based detection.

Solution: Use 'Automatic' detection method with address fallback.

**Corporate Networks**
Business customers often appear to be in different countries.

Solution: Enable customer country override option.

**Mobile Carriers**
Mobile IP addresses can be inaccurate.

Solution: Combine IP detection with address detection.

**Geolocation Database Issues**
The IP geolocation database might be outdated.

[Screen: WooCommerce geolocation settings]

Go to WooCommerce → Settings → General → Geolocation
Make sure 'Geolocate (with page caching support)' is enabled.

**Testing Detection**
[Screen: Plugin test tools]

Use the built-in test tools:

- Test different IP addresses
- Simulate different scenarios
- Check detection accuracy

**Fallback Strategy**
[Screen: Plugin settings]

Always configure proper fallbacks:

- Default country set correctly
- Customer override enabled
- Clear error handling"

**[ISSUE 5: IMPORT/EXPORT PROBLEMS - 12:30-14:30]**

"Import/export issues are usually data format problems:

[Screen: Import error message]

**Common Import Errors:**

**Invalid Country Codes**
Error: 'Invalid country code: UK'
Solution: Use ISO codes (GB, not UK)

**Wrong Data Types**
Error: 'Adjustment value must be numeric'
Solution: Check for text in numeric fields

**Missing Required Fields**
Error: 'Missing rule_type column'
Solution: Verify all required columns are present

**Character Encoding Issues**
Error: 'Invalid characters in import file'
Solution: Save CSV as UTF-8 encoding

[Screen: Excel save dialog]

In Excel: File → Save As → CSV UTF-8

**File Size Limits**
For large imports:

- Break into smaller files
- Increase PHP upload limits
- Use server-side import tools

**Validation Process**
[Screen: Import preview]

Always use the preview feature:

- Check column mapping
- Verify data looks correct
- Start with small test imports"

**[PREVENTION AND MONITORING - 14:30-15:00]**

"**Preventing Issues:**

1. **Regular Backups**: Export rules monthly
2. **Staging Testing**: Test changes before production
3. **Performance Monitoring**: Watch for slowdowns
4. **Error Logging**: Keep debug logs for troubleshooting
5. **User Training**: Educate team members on proper usage

**Getting Help:**

- Check documentation first
- Search support forums
- Contact support with specific error messages
- Include system information and debug logs

Most issues have simple solutions once you know where to look!"

---

## Performance Optimization

**Duration**: 10-12 minutes  
**Target Audience**: Site owners concerned about performance  
**Prerequisites**: Plugin installed and working

### Script

**[INTRO - 0:00-0:30]**

"Worried about how Price by Country might affect your site's performance? In this tutorial, I'll show you how to optimize the plugin for maximum speed while maintaining full functionality.

We'll cover:

- Built-in caching options
- Database optimization
- Server-level improvements
- Monitoring and measurement
- Best practices for high-traffic sites"

**[UNDERSTANDING PERFORMANCE IMPACT - 0:30-2:00]**

"[Screen: Performance monitoring dashboard]

First, let's understand what affects performance:

**Database Queries**
Each pricing calculation requires database lookups:

- Rule retrieval: 1-3 queries per product
- Country detection: 1 query per visitor
- Cache lookups: Minimal impact when optimized

**Calculation Overhead**

- Simple rules: <1ms per product
- Complex inheritance: 2-5ms per product
- Multiple rule types: Additive impact

**Typical Impact**
Well-optimized setup:

- Additional queries: 2-5 per page
- Additional load time: 10-30ms
- Memory usage: +2-5MB

Poorly optimized setup:

- Additional queries: 10-50 per page
- Additional load time: 100-500ms
- Memory usage: +10-50MB

The difference is in the configuration!"

**[BUILT-IN CACHING OPTIMIZATION - 2:00-5:00]**

"[Screen: Plugin performance settings]

The plugin has several caching layers:

**Rule Caching**
Stores pricing rules in memory to avoid repeated database queries.

Settings:

- Enable Rule Caching: Always 'Yes'
- Cache Duration: 1-4 hours depending on how often you change rules
- Cache Method: 'Object Cache' if available, otherwise 'Transients'

**Country Detection Caching**
Remembers detected countries to avoid repeated geolocation lookups.

Settings:

- Detection Cache: 30 minutes to 2 hours
- Longer = better performance, less responsive to customer location changes

**Calculation Caching**
Caches final calculated prices.

Settings:

- Price Cache Duration: 15-60 minutes
- Clear automatically when rules change

[Screen: Configuring cache settings]

For a typical store, I recommend:

- Rule Cache: 2 hours
- Detection Cache: 1 hour
- Price Cache: 30 minutes

**Advanced Caching**
[Screen: Object cache configuration]

If you have Redis or Memcached:

- Enable 'Use Object Cache'
- Set longer cache durations
- Enable 'Preload Common Rules'

This can reduce database queries by 80-90%!"

**[DATABASE OPTIMIZATION - 5:00-7:30]**

"[Screen: Database management interface]

**Table Optimization**
The plugin creates optimized database tables, but you can improve them:

```sql
-- Add custom indexes for your specific usage patterns
ALTER TABLE wp_pbc_pricing_rules
ADD INDEX idx_country_active (country_code, is_active);

-- Optimize for category-heavy stores
ALTER TABLE wp_pbc_pricing_rules
ADD INDEX idx_category_lookup (rule_type, object_id, country_code, is_active);

-- Clean up old cache entries
DELETE FROM wp_options
WHERE option_name LIKE '_transient_pbc_%'
AND option_value < UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 DAY));
```

**Query Optimization**
[Screen: Query monitoring plugin]

Use Query Monitor to identify slow queries:

- Look for queries taking >50ms
- Check for missing WHERE clauses
- Identify N+1 query problems

**Rule Structure Optimization**
[Screen: Rules dashboard]

Optimize your rule structure:

- Use fewer, more general rules when possible
- Prefer global rules over many product rules
- Remove unused or expired rules
- Consolidate similar adjustments

Example optimization:

- Before: 100 product rules for +15% in UK
- After: 1 global rule for +15% in UK, exceptions only for special products
- Result: 99% fewer database queries!"

**[SERVER-LEVEL OPTIMIZATION - 7:30-9:30]**

"[Screen: Server configuration]

**PHP Optimization**
Ensure adequate PHP resources:

- Memory limit: 256MB minimum, 512MB recommended
- Max execution time: 60 seconds minimum
- OPcache enabled for PHP code caching

**Database Server**
MySQL/MariaDB optimization:

- Query cache enabled
- Adequate buffer pool size
- Regular table optimization

**Web Server Caching**
[Screen: Caching plugin configuration]

Configure your caching plugin properly:

**WP Rocket:**

- Exclude checkout and cart pages
- Enable database cleanup
- Use advanced cache features

**W3 Total Cache:**

- Enable object caching
- Configure database caching
- Set appropriate cache lifetimes

**Cloudflare:**

- Use page rules for dynamic content
- Enable browser caching
- Configure edge caching appropriately

**CDN Configuration**
[Screen: CDN settings]

For international stores:

- Use geographically distributed CDN
- Configure proper cache headers
- Handle dynamic pricing correctly"

**[MONITORING AND MEASUREMENT - 9:30-11:00]**

"[Screen: Performance monitoring tools]

**Built-in Monitoring**
Enable plugin performance monitoring:

- Query count tracking
- Execution time measurement
- Cache hit rate monitoring
- Memory usage tracking

**External Tools**
Use professional monitoring:

**GTmetrix/PageSpeed Insights:**

- Measure overall page load times
- Identify performance bottlenecks
- Track improvements over time

**New Relic/DataDog:**

- Database query performance
- Application performance monitoring
- Real user monitoring

**Query Monitor Plugin:**

- WordPress-specific performance data
- Database query analysis
- Hook execution timing

[Screen: Performance dashboard]

Key metrics to track:

- Page load time impact: <50ms additional
- Database queries: <5 additional per page
- Memory usage: <10MB additional
- Cache hit rate: >90% for rules

**Performance Testing**
[Screen: Load testing tool]

Test under realistic conditions:

- Multiple countries simultaneously
- High traffic scenarios
- Complex rule combinations
- Mobile device performance"

**[HIGH-TRAFFIC OPTIMIZATION - 11:00-12:00]**

"[Screen: Enterprise configuration]

For high-traffic sites (>10,000 visitors/day):

**Advanced Caching Strategy**

- Use Redis/Memcached for object caching
- Implement full-page caching with ESI
- Use database read replicas
- Enable connection pooling

**Load Balancing Considerations**

- Sticky sessions for country detection
- Shared cache layer across servers
- Database connection optimization

**Rule Management**

- Minimize rule complexity
- Use bulk operations for updates
- Schedule rule changes during low traffic
- Implement rule versioning

**Monitoring and Alerting**

- Set up performance alerts
- Monitor cache hit rates
- Track database performance
- Alert on slow queries

**Example High-Traffic Setup:**

- Redis object cache: 4GB memory
- Database read replicas: 2 servers
- CDN: Global distribution
- Cache duration: 4+ hours
- Result: <10ms additional load time even at 100,000+ visitors/day

That's performance optimization! With these techniques, Price by Country will run smoothly even on the busiest stores."

---

## Video Production Notes

### Technical Requirements

**Recording Setup:**

- Screen resolution: 1920x1080 minimum
- Recording software: Camtasia, ScreenFlow, or OBS
- Audio: Clear microphone, noise-free environment
- Cursor highlighting for better visibility

**Post-Production:**

- Add intro/outro branding
- Include chapter markers for longer videos
- Add captions/subtitles for accessibility
- Export in multiple formats (1080p, 720p, mobile-optimized)

### Supplementary Materials

**For Each Video:**

- Written transcript
- Key points summary
- Related documentation links
- Sample files/code snippets
- Practice exercises

**Video Series Landing Page:**

- Complete tutorial index
- Difficulty level indicators
- Prerequisites for each video
- Estimated completion times
- Progress tracking

### Distribution Strategy

**Primary Platforms:**

- YouTube (main channel)
- Vimeo (backup/premium)
- Plugin documentation site
- Support knowledge base

**SEO Optimization:**

- Keyword-rich titles and descriptions
- Custom thumbnails with consistent branding
- Playlist organization by topic/difficulty
- Cross-linking between related videos

This comprehensive video tutorial series will provide users with visual, step-by-step guidance for all aspects of the Price by Country plugin, from basic setup to advanced optimization strategies.
