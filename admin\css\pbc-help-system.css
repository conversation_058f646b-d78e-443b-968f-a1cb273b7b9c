/**
 * Help System Styles for Price by Country Plugin
 * Provides styling for tooltips, tours, and contextual help
 */

/* Tooltips */
.pbc-tooltip {
  position: absolute;
  z-index: 10000;
  background: #333;
  color: #fff;
  border-radius: 4px;
  padding: 0;
  max-width: 300px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  display: none;
}

.pbc-tooltip-title {
  background: #2271b1;
  padding: 8px 12px;
  font-weight: 600;
  border-radius: 4px 4px 0 0;
  font-size: 13px;
}

.pbc-tooltip-content {
  padding: 10px 12px;
  font-size: 12px;
  line-height: 1.4;
}

.pbc-tooltip-content strong {
  color: #fff;
}

/* Help buttons */
.pbc-help-button {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: #2271b1;
  color: #fff;
  border-radius: 50%;
  text-align: center;
  line-height: 16px;
  font-size: 12px;
  text-decoration: none;
  margin-left: 5px;
  cursor: pointer;
  vertical-align: middle;
}

.pbc-help-button:hover {
  background: #135e96;
  color: #fff;
}

.pbc-help-button::before {
  content: "?";
  font-weight: bold;
}

/* Guided Tours */
.pbc-tour-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: none;
}

.pbc-tour-spotlight {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid #2271b1;
  border-radius: 4px;
  box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
}

.pbc-tour-popup {
  position: absolute;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  max-width: 350px;
  min-width: 280px;
}

.pbc-tour-header {
  padding: 15px 20px 10px;
  border-bottom: 1px solid #ddd;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pbc-tour-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.pbc-tour-progress {
  font-size: 12px;
  color: #666;
  background: #f0f0f1;
  padding: 2px 8px;
  border-radius: 10px;
}

.pbc-tour-content {
  padding: 15px 20px;
  font-size: 14px;
  line-height: 1.5;
  color: #555;
}

.pbc-tour-actions {
  padding: 10px 20px 15px;
  text-align: right;
  border-top: 1px solid #f0f0f1;
}

.pbc-tour-actions .button {
  margin-left: 10px;
}

/* Help Modal */
.pbc-help-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10001;
  display: none;
}

.pbc-help-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
}

.pbc-help-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.pbc-help-header {
  padding: 20px 25px 15px;
  border-bottom: 1px solid #ddd;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pbc-help-header h2 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.pbc-help-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.pbc-help-close:hover {
  background: #f0f0f1;
  color: #333;
}

.pbc-help-body {
  padding: 20px 25px;
  font-size: 14px;
  line-height: 1.6;
  color: #555;
}

.pbc-help-body ul {
  margin: 10px 0;
  padding-left: 20px;
}

.pbc-help-body li {
  margin-bottom: 8px;
}

.pbc-help-body strong {
  color: #333;
}

.pbc-help-footer {
  padding: 15px 25px 20px;
  border-top: 1px solid #f0f0f1;
  text-align: right;
}

.pbc-help-footer .button {
  margin-left: 10px;
}

/* Help indicators in forms */
.pbc-field-help {
  position: relative;
}

.pbc-field-help .pbc-help-button {
  position: absolute;
  right: -25px;
  top: 50%;
  transform: translateY(-50%);
}

/* Responsive design */
@media (max-width: 768px) {
  .pbc-tooltip {
    max-width: 250px;
    font-size: 11px;
  }

  .pbc-tour-popup {
    max-width: 300px;
    min-width: 250px;
  }

  .pbc-help-content {
    width: 95%;
    max-height: 90vh;
  }

  .pbc-help-header,
  .pbc-help-body,
  .pbc-help-footer {
    padding-left: 15px;
    padding-right: 15px;
  }
}

/* Integration with WordPress admin */
.wp-admin .pbc-tooltip {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

.wp-admin .pbc-help-content {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

/* Animation classes */
.pbc-tooltip.fade-in {
  animation: pbcFadeIn 0.2s ease-out;
}

.pbc-tour-popup.slide-up {
  animation: pbcSlideUp 0.3s ease-out;
}

@keyframes pbcFadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pbcSlideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Help system status indicators */
.pbc-help-available::after {
  content: "";
  display: inline-block;
  width: 6px;
  height: 6px;
  background: #2271b1;
  border-radius: 50%;
  margin-left: 5px;
  vertical-align: middle;
}

/* Quick help panel */
.pbc-quick-help {
  background: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  margin: 15px 0;
}

.pbc-quick-help h4 {
  margin: 0 0 10px 0;
  color: #2271b1;
  font-size: 14px;
}

.pbc-quick-help p {
  margin: 0;
  font-size: 13px;
  line-height: 1.4;
  color: #666;
}

.pbc-quick-help a {
  color: #2271b1;
  text-decoration: none;
}

.pbc-quick-help a:hover {
  text-decoration: underline;
}
