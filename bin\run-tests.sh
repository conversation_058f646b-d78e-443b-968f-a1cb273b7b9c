#!/bin/bash

# Test runner script for Price by Country plugin
# This script sets up the test environment and runs PHPUnit tests

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
WP_VERSION=${WP_VERSION:-latest}
WP_MULTISITE=${WP_MULTISITE:-0}
DB_NAME=${DB_NAME:-pbc_test}
DB_USER=${DB_USER:-root}
DB_PASS=${DB_PASS:-}
DB_HOST=${DB_HOST:-localhost}

echo -e "${YELLOW}Price by Country - Test Runner${NC}"
echo "=================================="

# Check if PHPUnit is available
if ! command -v phpunit &> /dev/null; then
    echo -e "${RED}Error: PHPUnit is not installed or not in PATH${NC}"
    echo "Please install PHPUnit: https://phpunit.de/getting-started.html"
    exit 1
fi

# Check if WordPress test suite is set up
if [ ! -d "/tmp/wordpress-tests-lib" ]; then
    echo -e "${YELLOW}Setting up WordPress test environment...${NC}"
    
    # Download WordPress test suite
    if [ ! -f "/tmp/install-wp-tests.sh" ]; then
        curl -s https://raw.githubusercontent.com/wp-cli/scaffold-command/master/templates/install-wp-tests.sh > /tmp/install-wp-tests.sh
        chmod +x /tmp/install-wp-tests.sh
    fi
    
    # Install WordPress test suite
    /tmp/install-wp-tests.sh $DB_NAME $DB_USER "$DB_PASS" $DB_HOST $WP_VERSION
fi

# Set environment variables
export WP_TESTS_DIR="/tmp/wordpress-tests-lib"
export WP_CORE_DIR="/tmp/wordpress"

echo -e "${GREEN}Environment ready!${NC}"
echo ""

# Function to run specific test suite
run_test_suite() {
    local suite=$1
    local description=$2
    
    echo -e "${YELLOW}Running $description...${NC}"
    
    if phpunit --testsuite=$suite --colors=always; then
        echo -e "${GREEN}✓ $description passed${NC}"
        return 0
    else
        echo -e "${RED}✗ $description failed${NC}"
        return 1
    fi
}

# Parse command line arguments
SUITE=""
COVERAGE=false
VERBOSE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --unit)
            SUITE="unit"
            shift
            ;;
        --integration)
            SUITE="integration"
            shift
            ;;
        --coverage)
            COVERAGE=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            echo "Usage: $0 [options]"
            echo ""
            echo "Options:"
            echo "  --unit         Run only unit tests"
            echo "  --integration  Run only integration tests"
            echo "  --coverage     Generate code coverage report"
            echo "  --verbose      Verbose output"
            echo "  --help         Show this help message"
            echo ""
            echo "Environment variables:"
            echo "  WP_VERSION     WordPress version (default: latest)"
            echo "  WP_MULTISITE   Enable multisite (default: 0)"
            echo "  DB_NAME        Test database name (default: pbc_test)"
            echo "  DB_USER        Database user (default: root)"
            echo "  DB_PASS        Database password (default: empty)"
            echo "  DB_HOST        Database host (default: localhost)"
            exit 0
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Build PHPUnit command
PHPUNIT_CMD="phpunit"

if [ "$VERBOSE" = true ]; then
    PHPUNIT_CMD="$PHPUNIT_CMD --verbose"
fi

if [ "$COVERAGE" = true ]; then
    PHPUNIT_CMD="$PHPUNIT_CMD --coverage-html tests/coverage/html --coverage-clover tests/coverage/clover.xml"
    echo -e "${YELLOW}Code coverage will be generated in tests/coverage/${NC}"
fi

# Create coverage directory if needed
if [ "$COVERAGE" = true ]; then
    mkdir -p tests/coverage
fi

# Run tests
FAILED=0

if [ -z "$SUITE" ]; then
    # Run all tests
    echo -e "${YELLOW}Running all tests...${NC}"
    echo ""
    
    if ! run_test_suite "unit" "Unit Tests"; then
        FAILED=1
    fi
    
    echo ""
    
    if ! run_test_suite "integration" "Integration Tests"; then
        FAILED=1
    fi
else
    # Run specific test suite
    case $SUITE in
        "unit")
            if ! run_test_suite "unit" "Unit Tests"; then
                FAILED=1
            fi
            ;;
        "integration")
            if ! run_test_suite "integration" "Integration Tests"; then
                FAILED=1
            fi
            ;;
    esac
fi

echo ""
echo "=================================="

if [ $FAILED -eq 0 ]; then
    echo -e "${GREEN}All tests passed! ✓${NC}"
    
    if [ "$COVERAGE" = true ]; then
        echo -e "${YELLOW}Coverage report generated in tests/coverage/html/index.html${NC}"
    fi
    
    exit 0
else
    echo -e "${RED}Some tests failed! ✗${NC}"
    exit 1
fi