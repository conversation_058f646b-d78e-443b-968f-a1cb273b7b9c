<?php
/**
 * Core plugin class for Price by Country
 *
 * @package PriceByCountry
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Main PBC Core Class
 */
class PBC_Core {

    /**
     * Single instance of the class
     *
     * @var PBC_Core
     */
    private static $instance = null;

    /**
     * Plugin version
     *
     * @var string
     */
    public $version = PBC_VERSION;

    /**
     * Database manager instance
     *
     * @var PBC_Database
     */
    public $database;

    /**
     * Pricing engine instance
     *
     * @var PBC_Pricing_Engine
     */
    public $pricing_engine;

    /**
     * Country detector instance
     *
     * @var PBC_Country_Detector
     */
    public $country_detector;

    /**
     * Admin controller instance
     *
     * @var PBC_Admin
     */
    public $admin;

    /**
     * API controller instance
     *
     * @var PBC_API
     */
    public $api;

    /**
     * Hooks manager instance
     *
     * @var PBC_Hooks
     */
    public $hooks;

    /**
     * Error handler instance
     *
     * @var PBC_Error_Handler
     */
    public $error_handler;

    /**
     * Logger instance
     *
     * @var PBC_Logger
     */
    public $logger;

    /**
     * Cache manager instance
     *
     * @var PBC_Cache_Manager
     */
    public $cache_manager;

    /**
     * Compatibility checker instance
     *
     * @var PBC_Compatibility_Checker
     */
    public $compatibility_checker;

    /**
     * Setup wizard instance
     *
     * @var PBC_Setup_Wizard
     */
    public $setup_wizard;

    /**
     * Get single instance of the class
     *
     * @return PBC_Core
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }

    /**
     * Initialize the plugin
     */
    private function init() {
        // Load text domain
        add_action('init', array($this, 'load_textdomain'));

        // Initialize components
        $this->init_components();

        // Initialize hooks
        $this->init_hooks();

        // Plugin loaded action
        do_action('pbc_loaded');
    }

    /**
     * Initialize plugin components
     */
    private function init_components() {
        // Initialize logger first
        $this->logger = PBC_Logger::get_instance();
        
        // Initialize error handler
        $this->error_handler = PBC_Error_Handler::get_instance();
        
        // Initialize database manager
        $this->database = new PBC_Database();
        $this->database->init();

        // Initialize country detector
        $this->country_detector = new PBC_Country_Detector();

        // Initialize pricing engine
        $this->pricing_engine = new PBC_Pricing_Engine($this->database, $this->country_detector);

        // Initialize admin interface (only in admin)
        if (is_admin()) {
            $this->admin = new PBC_Admin($this->database, $this->pricing_engine);
        }

        // Initialize API extensions
        $this->api = new PBC_API($this->pricing_engine, $this->database);

        // Initialize WooCommerce hooks
        $this->hooks = new PBC_Hooks($this->pricing_engine, $this->country_detector);

        // Initialize cache manager
        $this->cache_manager = new PBC_Cache_Manager($this->pricing_engine, $this->country_detector, $this->database);

        // Initialize compatibility checker after WooCommerce is fully loaded
        if (class_exists('WooCommerce')) {
            add_action('woocommerce_init', array($this, 'init_compatibility_checker'));
        } else {
            // Fallback: initialize immediately if WooCommerce is not available
            $this->compatibility_checker = new PBC_Compatibility_Checker();
        }

        // Initialize setup wizard (only in admin)
        if (is_admin()) {
            $this->setup_wizard = new PBC_Setup_Wizard();
        }
    }

    /**
     * Initialize plugin hooks
     */
    private function init_hooks() {
        // Plugin activation/deactivation hooks are handled in main plugin file
        
        // Add version check hook
        add_action('admin_init', array($this, 'check_version'));

        // Add HPOS compatibility check
        add_action('admin_init', array($this, 'check_hpos_compatibility'));

        // Add compatibility check hook
        add_action('admin_init', array($this, 'check_compatibility'));

        // Add setup wizard redirect hook
        add_action('admin_init', array($this, 'maybe_redirect_to_setup'));

        // Add cache cleanup hook
        add_action('pbc_cleanup_cache', array($this->database, 'cleanup_expired_cache'));

        // Add cache warming hook
        add_action('pbc_warm_cache', array($this->cache_manager, 'warm_popular_cache'));

        // Add cache invalidation hooks
        add_action('woocommerce_product_object_updated_props', array($this, 'invalidate_product_cache_on_update'), 10, 2);
        add_action('profile_update', array($this, 'invalidate_user_cache_on_update'));
        add_action('woocommerce_customer_save_address', array($this, 'invalidate_user_cache_on_address_update'), 10, 2);
        add_action('pbc_pricing_rule_changed', array($this, 'invalidate_cache_on_rule_change'), 10, 3);

        // Add AJAX hooks
        add_action('wp_ajax_pbc_get_countries', array($this, 'ajax_get_countries'));
        add_action('wp_ajax_pbc_test_country_detection', array($this, 'ajax_test_country_detection'));
    }

    /**
     * Load plugin text domain for translations
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'price-by-country',
            false,
            dirname(PBC_PLUGIN_BASENAME) . '/languages'
        );
    }

    /**
     * Check plugin version and run updates if needed
     */
    public function check_version() {
        $installed_version = get_option('pbc_version', '0.0.0');
        
        if (version_compare($installed_version, PBC_VERSION, '<')) {
            $this->update_plugin($installed_version);
            update_option('pbc_version', PBC_VERSION);
        }
    }

    /**
     * Update plugin data when version changes
     *
     * @param string $old_version Previous version
     */
    private function update_plugin($old_version) {
        // Run database updates if needed
        $this->database->update_database($old_version);

        // Clear any cached data
        $this->clear_cache();

        // Fire update action
        do_action('pbc_updated', $old_version, PBC_VERSION);
    }

    /**
     * Clear plugin cache
     */
    public function clear_cache() {
        // Use cache manager if available
        if ($this->cache_manager) {
            $this->cache_manager->clear_all_caches();
        } else {
            // Fallback to basic cache clearing
            delete_transient('pbc_pricing_rules');
            delete_transient('pbc_country_list');
        }
        
        // Clear WooCommerce cache
        if (function_exists('wc_delete_product_transients')) {
            wc_delete_product_transients();
        }
    }

    /**
     * Invalidate product cache when product is updated
     *
     * @param WC_Product $product Product object
     * @param array $updated_props Updated properties
     */
    public function invalidate_product_cache_on_update($product, $updated_props) {
        if ($this->cache_manager && $product) {
            $this->cache_manager->invalidate_product_cache($product->get_id());
        }
    }

    /**
     * Invalidate user cache when user profile is updated
     *
     * @param int $user_id User ID
     */
    public function invalidate_user_cache_on_update($user_id) {
        if ($this->cache_manager) {
            $this->cache_manager->invalidate_user_cache($user_id);
        }
    }

    /**
     * Invalidate user cache when address is updated
     *
     * @param int $user_id User ID
     * @param string $load_address Address type (billing/shipping)
     */
    public function invalidate_user_cache_on_address_update($user_id, $load_address) {
        if ($this->cache_manager) {
            $this->cache_manager->invalidate_user_cache($user_id);
        }
    }

    /**
     * Invalidate cache when pricing rule changes
     *
     * @param string $rule_type Rule type
     * @param int|null $object_id Object ID
     * @param string $country_code Country code
     */
    public function invalidate_cache_on_rule_change($rule_type, $object_id, $country_code) {
        if ($this->pricing_engine) {
            $this->pricing_engine->invalidate_cache_on_rule_change($rule_type, $object_id, $country_code);
        }
    }

    /**
     * AJAX handler to get countries list
     */
    public function ajax_get_countries() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'price-by-country'));
        }

        $countries = WC()->countries->get_countries();
        wp_send_json_success($countries);
    }

    /**
     * AJAX handler to test country detection
     */
    public function ajax_test_country_detection() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'price-by-country'));
        }

        $method = sanitize_text_field($_POST['method'] ?? 'auto');
        $result = $this->country_detector->detect_country($method);

        wp_send_json_success($result);
    }

    /**
     * Plugin activation
     */
    public static function activate() {
        // Check WordPress and WooCommerce versions
        if (!self::check_requirements()) {
            deactivate_plugins(PBC_PLUGIN_BASENAME);
            wp_die(__('Price by Country for WooCommerce requires WordPress 5.0+ and WooCommerce 5.0+', 'price-by-country'));
        }

        // Create database tables
        $database = new PBC_Database();
        $database->init();

        // Set default options
        self::set_default_options();

        // Schedule cache cleanup
        if (!wp_next_scheduled('pbc_cleanup_cache')) {
            wp_schedule_event(time(), 'daily', 'pbc_cleanup_cache');
        }

        // Schedule cache warming
        if (!wp_next_scheduled('pbc_warm_cache')) {
            wp_schedule_event(time(), 'hourly', 'pbc_warm_cache');
        }

        // Set version
        update_option('pbc_version', PBC_VERSION);

        // Clear rewrite rules
        flush_rewrite_rules();

        // Set activation redirect transient
        set_transient('pbc_activation_redirect', true, 30);

        // Fire activation action
        do_action('pbc_activated');
    }

    /**
     * Plugin deactivation
     */
    public static function deactivate() {
        // Clear scheduled events
        wp_clear_scheduled_hook('pbc_cleanup_cache');
        wp_clear_scheduled_hook('pbc_warm_cache');

        // Clear rewrite rules
        flush_rewrite_rules();

        // Fire deactivation action
        do_action('pbc_deactivated');
    }

    /**
     * Plugin uninstall
     */
    public static function uninstall() {
        // Check if user wants to keep data
        $keep_data_on_uninstall = get_option('pbc_keep_data_on_uninstall', 0);

        if (!$keep_data_on_uninstall) {
            // Remove database tables only if user chose not to keep data
            $database = new PBC_Database();
            $database->drop_tables();

            // Remove options
            delete_option('pbc_version');
            delete_option('pbc_settings');
            delete_option('pbc_detection_method');
            delete_option('pbc_detection_priority');
            delete_option('pbc_cache_duration');
            delete_option('pbc_fallback_country');
            delete_option('pbc_enable_logging');
            delete_option('pbc_ip_detection_service');
            delete_option('pbc_detection_accuracy_threshold');

            // Clear transients
            delete_transient('pbc_pricing_rules');
            delete_transient('pbc_country_list');
            delete_transient('pbc_activation_redirect');

            // Clear any scheduled events
            wp_clear_scheduled_hook('pbc_cleanup_cache');
            wp_clear_scheduled_hook('pbc_warm_cache');

            error_log('PBC: All plugin data removed during uninstall');
        } else {
            // Keep data but remove the keep_data option itself for next time
            delete_option('pbc_keep_data_on_uninstall');
            error_log('PBC: Plugin data preserved during uninstall as requested by user');
        }

        // Fire uninstall action
        do_action('pbc_uninstalled');
    }

    /**
     * Check plugin requirements
     *
     * @return bool
     */
    private static function check_requirements() {
        // Use compatibility checker for comprehensive testing
        $checker = new PBC_Compatibility_Checker();
        return $checker->is_compatible();
    }

    /**
     * Initialize compatibility checker after WooCommerce is loaded
     */
    public function init_compatibility_checker() {
        if (!$this->compatibility_checker) {
            $this->compatibility_checker = new PBC_Compatibility_Checker();
        }
    }

    /**
     * Check overall plugin compatibility and show notices
     */
    public function check_compatibility() {
        if (!$this->compatibility_checker) {
            return;
        }

        $issues = $this->compatibility_checker->get_compatibility_issues();
        
        if (!empty($issues)) {
            add_action('admin_notices', array($this, 'display_compatibility_notices'));
        }

        // Show setup wizard notice if not completed
        if (PBC_Setup_Wizard::should_show_setup()) {
            add_action('admin_notices', array($this, 'display_setup_notice'));
        }
    }

    /**
     * Display compatibility notices
     */
    public function display_compatibility_notices() {
        if (!current_user_can('manage_options')) {
            return;
        }

        $issues = $this->compatibility_checker->get_compatibility_issues();

        foreach ($issues as $issue) {
            $notice_class = $issue['status'] === 'fail' ? 'notice-error' : 'notice-warning';
            ?>
            <div class="notice <?php echo esc_attr($notice_class); ?>">
                <p>
                    <strong><?php _e('Price by Country for WooCommerce:', 'price-by-country'); ?></strong>
                    <?php echo esc_html($issue['message']); ?>
                </p>
            </div>
            <?php
        }
    }

    /**
     * Maybe redirect to setup wizard
     */
    public function maybe_redirect_to_setup() {
        // Don't redirect if we're already in setup or if setup is completed
        if (isset($_GET['page']) && $_GET['page'] === 'pbc-setup') {
            return;
        }

        if (get_option('pbc_setup_completed', false)) {
            return;
        }

        // Don't redirect on AJAX requests
        if (defined('DOING_AJAX') && DOING_AJAX) {
            return;
        }

        // Don't redirect if user can't manage WooCommerce
        if (!current_user_can('manage_woocommerce')) {
            return;
        }

        // Only redirect on plugin activation or first admin page load
        if (get_transient('pbc_activation_redirect')) {
            delete_transient('pbc_activation_redirect');
            wp_redirect(PBC_Setup_Wizard::get_setup_url());
            exit;
        }
    }

    /**
     * Display setup wizard notice
     */
    public function display_setup_notice() {
        if (!current_user_can('manage_woocommerce')) {
            return;
        }

        // Don't show on setup page
        if (isset($_GET['page']) && $_GET['page'] === 'pbc-setup') {
            return;
        }

        ?>
        <div class="notice notice-info is-dismissible">
            <p>
                <strong><?php _e('Price by Country for WooCommerce', 'price-by-country'); ?></strong><br>
                <?php _e('Welcome! Let\'s get your country-based pricing set up.', 'price-by-country'); ?>
            </p>
            <p>
                <a href="<?php echo esc_url(PBC_Setup_Wizard::get_setup_url()); ?>" class="button button-primary">
                    <?php _e('Run Setup Wizard', 'price-by-country'); ?>
                </a>
                <a href="<?php echo esc_url(add_query_arg('pbc_hide_setup_notice', '1')); ?>" class="button">
                    <?php _e('Skip Setup', 'price-by-country'); ?>
                </a>
            </p>
        </div>
        <?php

        // Handle skip setup
        if (isset($_GET['pbc_hide_setup_notice'])) {
            update_option('pbc_setup_completed', true);
        }
    }

    /**
     * Check HPOS compatibility and show notice if needed
     */
    public function check_hpos_compatibility() {
        if ($this->is_hpos_enabled()) {
            // HPOS is enabled - plugin is compatible
            return;
        }

        // HPOS is not enabled, but plugin is still compatible
        // This is just for informational purposes
        if (current_user_can('manage_woocommerce') && isset($_GET['page']) && $_GET['page'] === 'wc-settings') {
            add_action('admin_notices', array($this, 'hpos_info_notice'));
        }
    }

    /**
     * Display HPOS information notice
     */
    public function hpos_info_notice() {
        ?>
        <div class="notice notice-info is-dismissible">
            <p>
                <?php _e('Price by Country for WooCommerce is fully compatible with WooCommerce High-Performance Order Storage (HPOS).', 'price-by-country'); ?>
                <a href="<?php echo admin_url('admin.php?page=wc-settings&tab=advanced&section=features'); ?>" target="_blank">
                    <?php _e('Enable HPOS here', 'price-by-country'); ?>
                </a>
            </p>
        </div>
        <?php
    }

    /**
     * Set default plugin options
     */
    private static function set_default_options() {
        $default_settings = array(
            'country_detection_method' => 'auto',
            'detection_priority' => array('shipping', 'billing', 'ip'),
            'cache_duration' => 3600,
            'enable_logging' => false,
            'log_level' => 'info',
            'log_retention_days' => 30,
            'default_country' => substr(get_option('woocommerce_default_country', 'US:*'), 0, 2),
        );

        add_option('pbc_settings', $default_settings);
    }

    /**
     * Get plugin setting
     *
     * @param string $key Setting key
     * @param mixed $default Default value
     * @return mixed
     */
    public function get_setting($key, $default = null) {
        $settings = get_option('pbc_settings', array());
        return isset($settings[$key]) ? $settings[$key] : $default;
    }

    /**
     * Update plugin setting
     *
     * @param string $key Setting key
     * @param mixed $value Setting value
     */
    public function update_setting($key, $value) {
        $settings = get_option('pbc_settings', array());
        $settings[$key] = $value;
        update_option('pbc_settings', $settings);
    }

    /**
     * Check if WooCommerce HPOS is enabled
     *
     * @return bool
     */
    public function is_hpos_enabled() {
        if (class_exists('\Automattic\WooCommerce\Utilities\OrderUtil')) {
            return \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled();
        }
        return false;
    }

    /**
     * Get order meta data in HPOS-compatible way
     *
     * @param int|WC_Order $order Order ID or order object
     * @param string $meta_key Meta key
     * @param bool $single Whether to return single value
     * @return mixed Meta value
     */
    public function get_order_meta($order, $meta_key, $single = true) {
        if (!$order instanceof WC_Order) {
            $order = wc_get_order($order);
        }
        
        if (!$order) {
            return $single ? '' : array();
        }

        return $order->get_meta($meta_key, $single);
    }

    /**
     * Update order meta data in HPOS-compatible way
     *
     * @param int|WC_Order $order Order ID or order object
     * @param string $meta_key Meta key
     * @param mixed $meta_value Meta value
     * @return bool Success status
     */
    public function update_order_meta($order, $meta_key, $meta_value) {
        if (!$order instanceof WC_Order) {
            $order = wc_get_order($order);
        }
        
        if (!$order) {
            return false;
        }

        $order->update_meta_data($meta_key, $meta_value);
        $order->save();
        
        return true;
    }

    /**
     * Get plugin instance
     *
     * @return PBC_Core
     */
    public static function instance() {
        return self::get_instance();
    }
}