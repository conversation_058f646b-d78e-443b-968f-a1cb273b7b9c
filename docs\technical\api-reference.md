# Price by Country for WooCommerce - API Reference

## Table of Contents

1. [Overview](#overview)
2. [Authentication](#authentication)
3. [WooCommerce REST API Extensions](#woocommerce-rest-api-extensions)
4. [Custom API Endpoints](#custom-api-endpoints)
5. [Error Handling](#error-handling)
6. [Rate Limiting](#rate-limiting)
7. [Examples](#examples)

## Overview

The Price by Country for WooCommerce plugin extends the WooCommerce REST API to include country-based pricing functionality. It provides both extensions to existing WooCommerce endpoints and custom endpoints for managing pricing rules.

### Base URLs

- **WooCommerce API**: `/wp-json/wc/v3/`
- **Plugin API**: `/wp-json/pbc/v1/`

### Content Type

All API requests should use `Content-Type: application/json` for POST and PUT requests.

## Authentication

### WooCommerce API Authentication

The plugin uses WooCommerce's standard authentication methods:

#### Consumer Key/Secret (Recommended)

```bash
curl -X GET https://yoursite.com/wp-json/wc/v3/products/123?country=CA \
  -u consumer_key:consumer_secret
```

#### Basic Authentication (Development Only)

```bash
curl -X GET https://yoursite.com/wp-json/wc/v3/products/123?country=CA \
  -u username:password
```

### Custom API Authentication

Custom endpoints require WordPress authentication:

#### Cookie Authentication

```javascript
// Set nonce in request headers
headers: {
  'X-WP-Nonce': wpApiSettings.nonce
}
```

#### Application Passwords (WordPress 5.6+)

```bash
curl -X GET https://yoursite.com/wp-json/pbc/v1/pricing-rules \
  -u username:application_password
```

### Required Permissions

- **Read operations**: No special permissions for public data
- **Write operations**: `manage_woocommerce` capability
- **Delete operations**: `manage_options` capability

## WooCommerce REST API Extensions

### Products Endpoint Extensions

The plugin extends WooCommerce product endpoints to include country-based pricing data.

#### Get Product with Country Pricing

**Endpoint:** `GET /wp-json/wc/v3/products/{id}`

**Parameters:**

- `country` (string, optional): ISO 2-letter country code

**Example Request:**

```bash
curl -X GET "https://yoursite.com/wp-json/wc/v3/products/123?country=CA" \
  -u consumer_key:consumer_secret
```

**Example Response:**

```json
{
  "id": 123,
  "name": "Sample Product",
  "slug": "sample-product",
  "permalink": "https://yoursite.com/product/sample-product/",
  "date_created": "2024-01-15T10:30:00",
  "date_modified": "2024-01-20T14:45:00",
  "type": "simple",
  "status": "publish",
  "featured": false,
  "catalog_visibility": "visible",
  "description": "A sample product for demonstration",
  "short_description": "Sample product",
  "sku": "SAMPLE-123",
  "price": "85.00",
  "regular_price": "85.00",
  "sale_price": "",
  "date_on_sale_from": null,
  "date_on_sale_to": null,
  "price_html": "<span class=\"woocommerce-Price-amount amount\"><span class=\"woocommerce-Price-currencySymbol\">$</span>85.00</span>",
  "on_sale": false,
  "purchasable": true,
  "total_sales": 15,
  "virtual": false,
  "downloadable": false,
  "downloads": [],
  "download_limit": -1,
  "download_expiry": -1,
  "external_url": "",
  "button_text": "",
  "tax_status": "taxable",
  "tax_class": "",
  "manage_stock": false,
  "stock_quantity": null,
  "stock_status": "instock",
  "backorders": "no",
  "backorders_allowed": false,
  "backordered": false,
  "sold_individually": false,
  "weight": "",
  "dimensions": {
    "length": "",
    "width": "",
    "height": ""
  },
  "shipping_required": true,
  "shipping_taxable": true,
  "shipping_class": "",
  "shipping_class_id": 0,
  "reviews_allowed": true,
  "average_rating": "0.00",
  "rating_count": 0,
  "related_ids": [124, 125, 126],
  "upsell_ids": [],
  "cross_sell_ids": [],
  "parent_id": 0,
  "purchase_note": "",
  "categories": [
    {
      "id": 15,
      "name": "Electronics",
      "slug": "electronics"
    }
  ],
  "tags": [],
  "images": [
    {
      "id": 789,
      "date_created": "2024-01-15T10:30:00",
      "date_modified": "2024-01-15T10:30:00",
      "src": "https://yoursite.com/wp-content/uploads/2024/01/sample-product.jpg",
      "name": "sample-product.jpg",
      "alt": "Sample Product"
    }
  ],
  "attributes": [],
  "default_attributes": [],
  "variations": [],
  "grouped_products": [],
  "menu_order": 0,
  "meta_data": [],
  "country_pricing": {
    "country_code": "CA",
    "original_price": "100.00",
    "adjusted_price": "85.00",
    "adjustment": {
      "type": "percentage",
      "value": -15.0,
      "rule_source": "product",
      "rule_id": 456
    }
  },
  "_links": {
    "self": [
      {
        "href": "https://yoursite.com/wp-json/wc/v3/products/123"
      }
    ],
    "collection": [
      {
        "href": "https://yoursite.com/wp-json/wc/v3/products"
      }
    ]
  }
}
```

#### Get Product without Country Parameter

When no country parameter is provided, the response includes all available pricing rules:

**Example Request:**

```bash
curl -X GET "https://yoursite.com/wp-json/wc/v3/products/123" \
  -u consumer_key:consumer_secret
```

**Additional Response Fields:**

```json
{
  "id": 123,
  "name": "Sample Product",
  // ... other product fields ...
  "country_pricing_rules": [
    {
      "country_code": "CA",
      "adjustment_type": "percentage",
      "adjustment_value": -15.0,
      "rule_type": "product",
      "rule_id": 456,
      "is_active": true
    },
    {
      "country_code": "GB",
      "adjustment_type": "fixed",
      "adjustment_value": 5.0,
      "rule_type": "category",
      "rule_id": 789,
      "is_active": true
    }
  ]
}
```

#### Get Multiple Products with Country Pricing

**Endpoint:** `GET /wp-json/wc/v3/products`

**Parameters:**

- `country` (string, optional): ISO 2-letter country code
- Standard WooCommerce product query parameters

**Example Request:**

```bash
curl -X GET "https://yoursite.com/wp-json/wc/v3/products?country=CA&per_page=5" \
  -u consumer_key:consumer_secret
```

#### Product Variations

Product variations also support country-based pricing:

**Endpoint:** `GET /wp-json/wc/v3/products/{id}/variations/{variation_id}`

**Example Request:**

```bash
curl -X GET "https://yoursite.com/wp-json/wc/v3/products/123/variations/456?country=CA" \
  -u consumer_key:consumer_secret
```

## Custom API Endpoints

### Pricing Rules Management

#### Get Pricing Rules

**Endpoint:** `GET /wp-json/pbc/v1/pricing-rules`

**Parameters:**

- `rule_type` (string, optional): Filter by rule type (`global`, `category`, `product`)
- `object_id` (integer, optional): Filter by object ID
- `country_code` (string, optional): Filter by country code
- `per_page` (integer, optional): Number of results per page (default: 10, max: 100)
- `page` (integer, optional): Page number (default: 1)

**Example Request:**

```bash
curl -X GET "https://yoursite.com/wp-json/pbc/v1/pricing-rules?rule_type=product&per_page=20" \
  -u username:application_password
```

**Example Response:**

```json
[
  {
    "id": 456,
    "rule_type": "product",
    "object_id": 123,
    "country_code": "CA",
    "adjustment_type": "percentage",
    "adjustment_value": -15.0,
    "is_active": true,
    "created_at": "2024-01-15T10:30:00",
    "updated_at": "2024-01-20T14:45:00",
    "object_details": {
      "name": "Sample Product",
      "sku": "SAMPLE-123"
    }
  },
  {
    "id": 789,
    "rule_type": "category",
    "object_id": 15,
    "country_code": "GB",
    "adjustment_type": "fixed",
    "adjustment_value": 5.0,
    "is_active": true,
    "created_at": "2024-01-16T09:15:00",
    "updated_at": "2024-01-16T09:15:00",
    "object_details": {
      "name": "Electronics",
      "slug": "electronics"
    }
  }
]
```

#### Get Single Pricing Rule

**Endpoint:** `GET /wp-json/pbc/v1/pricing-rules/{id}`

**Example Request:**

```bash
curl -X GET "https://yoursite.com/wp-json/pbc/v1/pricing-rules/456" \
  -u username:application_password
```

**Example Response:**

```json
{
  "id": 456,
  "rule_type": "product",
  "object_id": 123,
  "country_code": "CA",
  "adjustment_type": "percentage",
  "adjustment_value": -15.0,
  "is_active": true,
  "created_at": "2024-01-15T10:30:00",
  "updated_at": "2024-01-20T14:45:00",
  "object_details": {
    "name": "Sample Product",
    "sku": "SAMPLE-123",
    "regular_price": "100.00"
  }
}
```

#### Create Pricing Rule

**Endpoint:** `POST /wp-json/pbc/v1/pricing-rules`

**Required Fields:**

- `rule_type`: `global`, `category`, or `product`
- `country_code`: ISO 2-letter country code
- `adjustment_type`: `fixed` or `percentage`
- `adjustment_value`: Numeric adjustment value

**Optional Fields:**

- `object_id`: Required for `category` and `product` rules
- `is_active`: Boolean (default: true)

**Example Request:**

```bash
curl -X POST "https://yoursite.com/wp-json/pbc/v1/pricing-rules" \
  -u username:application_password \
  -H "Content-Type: application/json" \
  -d '{
    "rule_type": "product",
    "object_id": 123,
    "country_code": "CA",
    "adjustment_type": "percentage",
    "adjustment_value": -15.0,
    "is_active": true
  }'
```

**Example Response:**

```json
{
  "id": 456,
  "rule_type": "product",
  "object_id": 123,
  "country_code": "CA",
  "adjustment_type": "percentage",
  "adjustment_value": -15.0,
  "is_active": true,
  "created_at": "2024-01-25T10:30:00",
  "updated_at": "2024-01-25T10:30:00",
  "object_details": {
    "name": "Sample Product",
    "sku": "SAMPLE-123"
  }
}
```

#### Update Pricing Rule

**Endpoint:** `PUT /wp-json/pbc/v1/pricing-rules/{id}`

**Example Request:**

```bash
curl -X PUT "https://yoursite.com/wp-json/pbc/v1/pricing-rules/456" \
  -u username:application_password \
  -H "Content-Type: application/json" \
  -d '{
    "adjustment_value": -20.0,
    "is_active": true
  }'
```

**Example Response:**

```json
{
  "id": 456,
  "rule_type": "product",
  "object_id": 123,
  "country_code": "CA",
  "adjustment_type": "percentage",
  "adjustment_value": -20.0,
  "is_active": true,
  "created_at": "2024-01-15T10:30:00",
  "updated_at": "2024-01-25T11:45:00",
  "object_details": {
    "name": "Sample Product",
    "sku": "SAMPLE-123"
  }
}
```

#### Delete Pricing Rule

**Endpoint:** `DELETE /wp-json/pbc/v1/pricing-rules/{id}`

**Example Request:**

```bash
curl -X DELETE "https://yoursite.com/wp-json/pbc/v1/pricing-rules/456" \
  -u username:application_password
```

**Example Response:**

```json
{
  "deleted": true,
  "previous": {
    "id": 456,
    "rule_type": "product",
    "object_id": 123,
    "country_code": "CA",
    "adjustment_type": "percentage",
    "adjustment_value": -20.0,
    "is_active": true,
    "created_at": "2024-01-15T10:30:00",
    "updated_at": "2024-01-25T11:45:00"
  },
  "message": "Pricing rule deleted successfully"
}
```

### Product Country Pricing

#### Get Product Country Pricing

**Endpoint:** `GET /wp-json/pbc/v1/products/{id}/country-pricing`

**Parameters:**

- `country` (string, optional): Specific country code

**Example Request (Specific Country):**

```bash
curl -X GET "https://yoursite.com/wp-json/pbc/v1/products/123/country-pricing?country=CA" \
  -u consumer_key:consumer_secret
```

**Example Response (Specific Country):**

```json
{
  "product": {
    "id": 123,
    "name": "Sample Product",
    "sku": "SAMPLE-123",
    "type": "simple"
  },
  "country_code": "CA",
  "original_price": "100.00",
  "adjusted_price": "85.00",
  "adjustment": {
    "type": "percentage",
    "value": -15.0,
    "rule_source": "product",
    "rule_id": 456
  }
}
```

**Example Request (All Countries):**

```bash
curl -X GET "https://yoursite.com/wp-json/pbc/v1/products/123/country-pricing" \
  -u consumer_key:consumer_secret
```

**Example Response (All Countries):**

```json
{
  "product": {
    "id": 123,
    "name": "Sample Product",
    "sku": "SAMPLE-123",
    "type": "simple"
  },
  "pricing_rules": [
    {
      "country_code": "CA",
      "adjustment_type": "percentage",
      "adjustment_value": -15.0,
      "rule_type": "product",
      "rule_id": 456,
      "is_active": true
    },
    {
      "country_code": "GB",
      "adjustment_type": "fixed",
      "adjustment_value": 5.0,
      "rule_type": "category",
      "rule_id": 789,
      "is_active": true
    }
  ]
}
```

## Error Handling

### Error Response Format

All API errors follow a consistent format:

```json
{
  "code": "error_code",
  "message": "Human-readable error message",
  "data": {
    "status": 400,
    "additional_info": "Optional additional information"
  }
}
```

### Common Error Codes

#### Authentication Errors

**401 Unauthorized:**

```json
{
  "code": "rest_forbidden",
  "message": "Sorry, you are not allowed to do that.",
  "data": {
    "status": 401
  }
}
```

**403 Forbidden:**

```json
{
  "code": "pbc_insufficient_permissions",
  "message": "You do not have permission to manage pricing rules.",
  "data": {
    "status": 403
  }
}
```

#### Validation Errors

**Invalid Country Code:**

```json
{
  "code": "pbc_invalid_country_code",
  "message": "Country code must be exactly 2 characters",
  "data": {
    "status": 400
  }
}
```

**Invalid Rule Type:**

```json
{
  "code": "pbc_invalid_rule_type",
  "message": "Rule type must be one of: global, category, product",
  "data": {
    "status": 400
  }
}
```

**Invalid Adjustment Value:**

```json
{
  "code": "pbc_invalid_percentage",
  "message": "Percentage adjustment must be between -100 and 1000",
  "data": {
    "status": 400
  }
}
```

#### Resource Errors

**Not Found:**

```json
{
  "code": "pbc_rule_not_found",
  "message": "Pricing rule not found",
  "data": {
    "status": 404
  }
}
```

**Duplicate Rule:**

```json
{
  "code": "pbc_duplicate_rule",
  "message": "A pricing rule already exists for this combination of rule type, object, and country",
  "data": {
    "status": 409,
    "existing_rule_id": 456
  }
}
```

#### Server Errors

**Internal Server Error:**

```json
{
  "code": "pbc_pricing_calculation_error",
  "message": "Error calculating pricing data: Database connection failed",
  "data": {
    "status": 500
  }
}
```

## Rate Limiting

The plugin respects WordPress and WooCommerce rate limiting:

- **Default limit**: 100 requests per minute per IP
- **Authenticated users**: 1000 requests per minute
- **Rate limit headers** are included in responses:
  - `X-RateLimit-Limit`: Request limit per window
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Time when the rate limit resets

## Examples

### JavaScript/Node.js Examples

#### Get Product with Country Pricing

```javascript
const WooCommerceRestApi = require("@woocommerce/woocommerce-rest-api").default;

const WooCommerce = new WooCommerceRestApi({
  url: "https://yoursite.com",
  consumerKey: "ck_your_consumer_key",
  consumerSecret: "cs_your_consumer_secret",
  version: "wc/v3",
});

// Get product with Canadian pricing
WooCommerce.get("products/123", { country: "CA" })
  .then((response) => {
    const product = response.data;
    console.log("Product:", product.name);
    console.log("Original Price:", product.country_pricing.original_price);
    console.log("Canadian Price:", product.country_pricing.adjusted_price);
  })
  .catch((error) => {
    console.error("Error:", error.response.data);
  });
```

#### Create Pricing Rule

```javascript
const axios = require("axios");

const createPricingRule = async () => {
  try {
    const response = await axios.post(
      "https://yoursite.com/wp-json/pbc/v1/pricing-rules",
      {
        rule_type: "product",
        object_id: 123,
        country_code: "CA",
        adjustment_type: "percentage",
        adjustment_value: -15.0,
        is_active: true,
      },
      {
        auth: {
          username: "your_username",
          password: "your_application_password",
        },
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    console.log("Rule created:", response.data);
  } catch (error) {
    console.error("Error:", error.response.data);
  }
};

createPricingRule();
```

### PHP Examples

#### Get Product with Country Pricing

```php
<?php
$consumer_key = 'ck_your_consumer_key';
$consumer_secret = 'cs_your_consumer_secret';
$store_url = 'https://yoursite.com';

$url = $store_url . '/wp-json/wc/v3/products/123?country=CA';

$response = wp_remote_get($url, array(
    'headers' => array(
        'Authorization' => 'Basic ' . base64_encode($consumer_key . ':' . $consumer_secret)
    )
));

if (!is_wp_error($response)) {
    $product = json_decode(wp_remote_retrieve_body($response), true);

    echo "Product: " . $product['name'] . "\n";
    echo "Original Price: " . $product['country_pricing']['original_price'] . "\n";
    echo "Canadian Price: " . $product['country_pricing']['adjusted_price'] . "\n";
} else {
    echo "Error: " . $response->get_error_message() . "\n";
}
?>
```

#### Batch Update Pricing Rules

```php
<?php
function batch_update_pricing_rules($rules) {
    $username = 'your_username';
    $password = 'your_application_password';
    $base_url = 'https://yoursite.com/wp-json/pbc/v1/pricing-rules';

    foreach ($rules as $rule_data) {
        $url = $base_url;
        $method = 'POST';

        // If rule has ID, update existing rule
        if (isset($rule_data['id'])) {
            $url .= '/' . $rule_data['id'];
            $method = 'PUT';
            unset($rule_data['id']);
        }

        $response = wp_remote_request($url, array(
            'method' => $method,
            'headers' => array(
                'Authorization' => 'Basic ' . base64_encode($username . ':' . $password),
                'Content-Type' => 'application/json'
            ),
            'body' => json_encode($rule_data)
        ));

        if (is_wp_error($response)) {
            echo "Error updating rule: " . $response->get_error_message() . "\n";
        } else {
            $result = json_decode(wp_remote_retrieve_body($response), true);
            echo "Rule updated: ID " . $result['id'] . "\n";
        }
    }
}

// Example usage
$rules = array(
    array(
        'rule_type' => 'product',
        'object_id' => 123,
        'country_code' => 'CA',
        'adjustment_type' => 'percentage',
        'adjustment_value' => -15.0
    ),
    array(
        'id' => 456, // Update existing rule
        'adjustment_value' => -20.0
    )
);

batch_update_pricing_rules($rules);
?>
```

### Python Examples

#### Get All Pricing Rules

```python
import requests
from requests.auth import HTTPBasicAuth

def get_all_pricing_rules():
    url = "https://yoursite.com/wp-json/pbc/v1/pricing-rules"
    auth = HTTPBasicAuth('your_username', 'your_application_password')

    page = 1
    all_rules = []

    while True:
        params = {'page': page, 'per_page': 100}
        response = requests.get(url, auth=auth, params=params)

        if response.status_code != 200:
            print(f"Error: {response.status_code} - {response.text}")
            break

        rules = response.json()
        if not rules:
            break

        all_rules.extend(rules)
        page += 1

    return all_rules

# Get and display all rules
rules = get_all_pricing_rules()
for rule in rules:
    print(f"Rule {rule['id']}: {rule['rule_type']} - {rule['country_code']} - {rule['adjustment_value']}%")
```

### cURL Examples

#### Complete CRUD Operations

```bash
#!/bin/bash

BASE_URL="https://yoursite.com/wp-json/pbc/v1/pricing-rules"
AUTH="username:application_password"

# Create a new pricing rule
echo "Creating pricing rule..."
RULE_ID=$(curl -s -X POST "$BASE_URL" \
  -u "$AUTH" \
  -H "Content-Type: application/json" \
  -d '{
    "rule_type": "product",
    "object_id": 123,
    "country_code": "CA",
    "adjustment_type": "percentage",
    "adjustment_value": -15.0
  }' | jq -r '.id')

echo "Created rule with ID: $RULE_ID"

# Read the pricing rule
echo "Reading pricing rule..."
curl -s -X GET "$BASE_URL/$RULE_ID" \
  -u "$AUTH" | jq '.'

# Update the pricing rule
echo "Updating pricing rule..."
curl -s -X PUT "$BASE_URL/$RULE_ID" \
  -u "$AUTH" \
  -H "Content-Type: application/json" \
  -d '{
    "adjustment_value": -20.0
  }' | jq '.'

# Delete the pricing rule
echo "Deleting pricing rule..."
curl -s -X DELETE "$BASE_URL/$RULE_ID" \
  -u "$AUTH" | jq '.'
```

This API reference provides comprehensive documentation for integrating with the Price by Country for WooCommerce plugin's REST API endpoints.
