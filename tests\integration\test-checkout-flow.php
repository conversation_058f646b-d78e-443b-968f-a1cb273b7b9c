<?php
/**
 * Integration tests for checkout flow with address changes and price updates
 *
 * @package PriceByCountry
 */

class Test_Checkout_Flow extends WP_UnitTestCase {

    /**
     * Test product IDs
     *
     * @var array
     */
    private $test_products = [];

    /**
     * Test user ID
     *
     * @var int
     */
    private $test_user_id;

    /**
     * Database instance
     *
     * @var PBC_Database
     */
    private $database;

    /**
     * Set up test environment
     */
    public function setUp(): void {
        parent::setUp();

        // Initialize database
        $this->database = new PBC_Database();
        $this->database->init();

        // Create test products
        $this->test_products = [
            'simple' => PBC_Test_Helper::create_test_product('50.00'),
            'variable' => PBC_Test_Helper::create_test_product('75.00')
        ];

        // Create test user
        $this->test_user_id = $this->factory->user->create([
            'role' => 'customer'
        ]);

        // Set up pricing rules for different countries
        $this->setup_pricing_rules();

        // Mock WooCommerce checkout environment
        $this->mock_checkout_environment();
    }

    /**
     * Test complete checkout flow with country-specific pricing
     */
    public function test_complete_checkout_flow() {
        // Start with US customer
        $this->set_customer_country('US');

        // Add products to cart
        WC()->cart->add_to_cart($this->test_products['simple'], 2); // 2 x $50 = $100
        WC()->cart->add_to_cart($this->test_products['variable'], 1); // 1 x $75 = $75

        // Calculate initial totals (US pricing - no rules)
        WC()->cart->calculate_totals();
        $initial_total = WC()->cart->get_total('raw');
        $this->assertEquals(125.00, $initial_total); // $100 + $75

        // Customer changes billing address to Canada during checkout
        $this->simulate_address_change('CA');

        // Recalculate totals with CA pricing
        WC()->cart->calculate_totals();
        $ca_total = WC()->cart->get_total('raw');
        
        // CA has -$10 fixed discount on simple product and +20% on variable
        // Simple: (50 - 10) * 2 = 80
        // Variable: 75 * 1.2 = 90
        // Total: 80 + 90 = 170
        $this->assertEquals(170.00, $ca_total);

        // Complete the order
        $order = $this->complete_checkout();
        
        // Verify order totals and country information
        $this->assertEquals(170.00, $order->get_total());
        $this->assertEquals('CA', $order->get_meta('_pbc_pricing_country'));
    }

    /**
     * Test address change during checkout updates prices
     */
    public function test_address_change_updates_prices() {
        // Start with default country (US)
        $this->set_customer_country('US');
        WC()->cart->add_to_cart($this->test_products['simple'], 1);

        // Initial calculation
        WC()->cart->calculate_totals();
        $us_total = WC()->cart->get_total('raw');
        $this->assertEquals(50.00, $us_total);

        // Change to UK (has percentage increase)
        $this->simulate_address_change('UK');
        WC()->cart->calculate_totals();
        $uk_total = WC()->cart->get_total('raw');
        $this->assertEquals(60.00, $uk_total); // 50 * 1.2 = 60

        // Change to Australia (has fixed discount)
        $this->simulate_address_change('AU');
        WC()->cart->calculate_totals();
        $au_total = WC()->cart->get_total('raw');
        $this->assertEquals(45.00, $au_total); // 50 - 5 = 45
    }

    /**
     * Test shipping address vs billing address priority
     */
    public function test_shipping_vs_billing_address_priority() {
        // Set different billing and shipping countries
        $this->set_customer_addresses('US', 'CA'); // billing: US, shipping: CA

        WC()->cart->add_to_cart($this->test_products['simple'], 1);
        WC()->cart->calculate_totals();

        // Should use shipping address (CA) for pricing
        $total = WC()->cart->get_total('raw');
        $this->assertEquals(40.00, $total); // CA pricing: 50 - 10 = 40
    }

    /**
     * Test fallback to billing when shipping not available
     */
    public function test_fallback_to_billing_address() {
        // Set only billing address
        $this->set_customer_addresses('UK', '');

        WC()->cart->add_to_cart($this->test_products['simple'], 1);
        WC()->cart->calculate_totals();

        // Should use billing address (UK) for pricing
        $total = WC()->cart->get_total('raw');
        $this->assertEquals(60.00, $total); // UK pricing: 50 * 1.2 = 60
    }

    /**
     * Test multiple address changes in single session
     */
    public function test_multiple_address_changes() {
        WC()->cart->add_to_cart($this->test_products['simple'], 1);

        // Start with US
        $this->set_customer_country('US');
        WC()->cart->calculate_totals();
        $this->assertEquals(50.00, WC()->cart->get_total('raw'));

        // Change to CA
        $this->simulate_address_change('CA');
        WC()->cart->calculate_totals();
        $this->assertEquals(40.00, WC()->cart->get_total('raw'));

        // Change to UK
        $this->simulate_address_change('UK');
        WC()->cart->calculate_totals();
        $this->assertEquals(60.00, WC()->cart->get_total('raw'));

        // Back to US
        $this->simulate_address_change('US');
        WC()->cart->calculate_totals();
        $this->assertEquals(50.00, WC()->cart->get_total('raw'));
    }

    /**
     * Test cart persistence across country changes
     */
    public function test_cart_persistence_across_country_changes() {
        // Add multiple products
        WC()->cart->add_to_cart($this->test_products['simple'], 3);
        WC()->cart->add_to_cart($this->test_products['variable'], 2);

        $initial_count = WC()->cart->get_cart_contents_count();
        $this->assertEquals(5, $initial_count);

        // Change countries multiple times
        $this->simulate_address_change('CA');
        $this->simulate_address_change('UK');
        $this->simulate_address_change('AU');

        // Cart contents should remain the same
        $final_count = WC()->cart->get_cart_contents_count();
        $this->assertEquals(5, $final_count);

        // But prices should reflect final country (AU)
        WC()->cart->calculate_totals();
        $expected_total = (45.00 * 3) + (75.00 * 2); // AU pricing
        $this->assertEquals($expected_total, WC()->cart->get_total('raw'));
    }

    /**
     * Test checkout with guest user
     */
    public function test_guest_checkout_flow() {
        // Simulate guest user (not logged in)
        wp_set_current_user(0);

        // Set address via form data
        $_POST['billing_country'] = 'CA';
        $_POST['shipping_country'] = 'CA';

        WC()->cart->add_to_cart($this->test_products['simple'], 1);

        // Simulate checkout form submission
        $this->simulate_checkout_form_submission();

        WC()->cart->calculate_totals();
        $total = WC()->cart->get_total('raw');

        // Should use CA pricing even for guest
        $this->assertEquals(40.00, $total);
    }

    /**
     * Test order review updates during checkout
     */
    public function test_order_review_updates() {
        WC()->cart->add_to_cart($this->test_products['simple'], 2);
        WC()->cart->add_to_cart($this->test_products['variable'], 1);

        // Initial order review
        $this->set_customer_country('US');
        $review_data = $this->get_order_review_data();
        $this->assertEquals(125.00, $review_data['total']);

        // Update address and refresh order review
        $this->simulate_address_change('CA');
        $updated_review = $this->get_order_review_data();
        $this->assertEquals(170.00, $updated_review['total']);

        // Verify line item prices are updated
        foreach ($updated_review['items'] as $item) {
            if ($item['product_id'] == $this->test_products['simple']) {
                $this->assertEquals(40.00, $item['price']); // 50 - 10
            } elseif ($item['product_id'] == $this->test_products['variable']) {
                $this->assertEquals(90.00, $item['price']); // 75 * 1.2
            }
        }
    }

    /**
     * Test AJAX checkout updates
     */
    public function test_ajax_checkout_updates() {
        // Add product to cart
        WC()->cart->add_to_cart($this->test_products['simple'], 1);

        // Mock AJAX request for checkout update
        $_POST['action'] = 'woocommerce_update_order_review';
        $_POST['billing_country'] = 'UK';
        $_POST['shipping_country'] = 'UK';

        // Capture AJAX response
        ob_start();
        do_action('wp_ajax_woocommerce_update_order_review');
        do_action('wp_ajax_nopriv_woocommerce_update_order_review');
        $response = ob_get_clean();

        // Verify response contains updated pricing
        $this->assertStringContains('60.00', $response); // UK pricing
    }

    /**
     * Test checkout validation with country pricing
     */
    public function test_checkout_validation_with_country_pricing() {
        WC()->cart->add_to_cart($this->test_products['simple'], 1);
        $this->set_customer_country('CA');

        // Simulate checkout form submission
        $checkout_data = [
            'billing_first_name' => 'John',
            'billing_last_name' => 'Doe',
            'billing_email' => '<EMAIL>',
            'billing_country' => 'CA',
            'payment_method' => 'cod'
        ];

        $validation_result = $this->validate_checkout($checkout_data);
        
        $this->assertTrue($validation_result['valid']);
        $this->assertEquals(40.00, $validation_result['total']); // CA pricing
    }

    /**
     * Test order creation preserves country pricing information
     */
    public function test_order_creation_preserves_country_info() {
        WC()->cart->add_to_cart($this->test_products['simple'], 1);
        $this->set_customer_country('UK');

        $order = $this->complete_checkout();

        // Verify order meta contains pricing information
        $this->assertEquals('UK', $order->get_meta('_pbc_pricing_country'));
        $this->assertEquals('20', $order->get_meta('_pbc_adjustment_percentage'));
        $this->assertEquals('60.00', $order->get_total());

        // Verify order line items have correct prices
        $items = $order->get_items();
        foreach ($items as $item) {
            if ($item->get_product_id() == $this->test_products['simple']) {
                $this->assertEquals(60.00, $item->get_total());
            }
        }
    }

    /**
     * Test checkout with mixed pricing rules
     */
    public function test_checkout_with_mixed_pricing_rules() {
        // Create products with different rule types
        $category_id = wp_insert_term('Test Category', 'product_cat')['term_id'];
        $category_product = PBC_Test_Helper::create_test_product('80.00');
        wp_set_object_terms($category_product, $category_id, 'product_cat');

        // Add products to cart
        WC()->cart->add_to_cart($this->test_products['simple'], 1); // Product-specific rule
        WC()->cart->add_to_cart($category_product, 1); // Category rule
        WC()->cart->add_to_cart($this->test_products['variable'], 1); // Global rule fallback

        $this->set_customer_country('CA');
        WC()->cart->calculate_totals();

        $total = WC()->cart->get_total('raw');
        
        // Simple: 50 - 10 = 40 (product rule)
        // Category: 80 - 8 = 72 (category rule: -10%)
        // Variable: 75 + 15 = 90 (global rule: +20%)
        // Total: 40 + 72 + 90 = 202
        $expected_total = 40.00 + 72.00 + 90.00;
        $this->assertEquals($expected_total, $total);
    }

    /**
     * Set up pricing rules for testing
     */
    private function setup_pricing_rules() {
        // Product-specific rules
        $this->database->create_pricing_rule([
            'rule_type' => 'product',
            'object_id' => $this->test_products['simple'],
            'country_code' => 'CA',
            'adjustment_type' => 'fixed',
            'adjustment_value' => -10.00,
            'is_active' => 1
        ]);

        $this->database->create_pricing_rule([
            'rule_type' => 'product',
            'object_id' => $this->test_products['simple'],
            'country_code' => 'UK',
            'adjustment_type' => 'percentage',
            'adjustment_value' => 20.00,
            'is_active' => 1
        ]);

        $this->database->create_pricing_rule([
            'rule_type' => 'product',
            'object_id' => $this->test_products['simple'],
            'country_code' => 'AU',
            'adjustment_type' => 'fixed',
            'adjustment_value' => -5.00,
            'is_active' => 1
        ]);

        // Variable product rules
        $this->database->create_pricing_rule([
            'rule_type' => 'product',
            'object_id' => $this->test_products['variable'],
            'country_code' => 'CA',
            'adjustment_type' => 'percentage',
            'adjustment_value' => 20.00,
            'is_active' => 1
        ]);

        // Category rule
        $category_id = wp_insert_term('Test Category', 'product_cat')['term_id'];
        $this->database->create_pricing_rule([
            'rule_type' => 'category',
            'object_id' => $category_id,
            'country_code' => 'CA',
            'adjustment_type' => 'percentage',
            'adjustment_value' => -10.00,
            'is_active' => 1
        ]);

        // Global rule
        $this->database->create_pricing_rule([
            'rule_type' => 'global',
            'object_id' => null,
            'country_code' => 'CA',
            'adjustment_type' => 'percentage',
            'adjustment_value' => 20.00,
            'is_active' => 1
        ]);
    }

    /**
     * Mock checkout environment
     */
    private function mock_checkout_environment() {
        // Mock WC Customer
        if (!class_exists('WC_Customer')) {
            class WC_Customer {
                private $billing_country = '';
                private $shipping_country = '';

                public function get_billing_country() {
                    return $this->billing_country;
                }

                public function get_shipping_country() {
                    return $this->shipping_country;
                }

                public function set_billing_country($country) {
                    $this->billing_country = $country;
                }

                public function set_shipping_country($country) {
                    $this->shipping_country = $country;
                }
            }
        }

        // Initialize WC customer
        if (!isset(WC()->customer)) {
            WC()->customer = new WC_Customer();
        }
    }

    /**
     * Set customer country for testing
     */
    private function set_customer_country($country) {
        WC()->customer->set_billing_country($country);
        WC()->customer->set_shipping_country($country);
        $_SESSION['pbc_test_country'] = $country;
    }

    /**
     * Set customer addresses separately
     */
    private function set_customer_addresses($billing_country, $shipping_country) {
        WC()->customer->set_billing_country($billing_country);
        WC()->customer->set_shipping_country($shipping_country);
        $_SESSION['pbc_test_country'] = $shipping_country ?: $billing_country;
    }

    /**
     * Simulate address change during checkout
     */
    private function simulate_address_change($country) {
        $_POST['billing_country'] = $country;
        $_POST['shipping_country'] = $country;
        
        $this->set_customer_country($country);
        
        // Trigger address change hooks
        do_action('woocommerce_checkout_update_order_review');
    }

    /**
     * Simulate checkout form submission
     */
    private function simulate_checkout_form_submission() {
        do_action('woocommerce_checkout_process');
    }

    /**
     * Get order review data
     */
    private function get_order_review_data() {
        $cart_contents = WC()->cart->get_cart();
        $items = [];
        
        foreach ($cart_contents as $cart_item) {
            $items[] = [
                'product_id' => $cart_item['product_id'],
                'quantity' => $cart_item['quantity'],
                'price' => $cart_item['data']->get_price() * $cart_item['quantity']
            ];
        }

        return [
            'items' => $items,
            'total' => WC()->cart->get_total('raw')
        ];
    }

    /**
     * Validate checkout data
     */
    private function validate_checkout($data) {
        // Simple validation mock
        $required_fields = ['billing_first_name', 'billing_last_name', 'billing_email', 'billing_country'];
        
        foreach ($required_fields as $field) {
            if (empty($data[$field])) {
                return ['valid' => false, 'error' => "Missing {$field}"];
            }
        }

        return [
            'valid' => true,
            'total' => WC()->cart->get_total('raw')
        ];
    }

    /**
     * Complete checkout and create order
     */
    private function complete_checkout() {
        $order = wc_create_order([
            'customer_id' => $this->test_user_id
        ]);

        // Add cart items to order
        foreach (WC()->cart->get_cart() as $cart_item) {
            $order->add_product($cart_item['data'], $cart_item['quantity']);
        }

        // Set country meta
        $country = $_SESSION['pbc_test_country'] ?? 'US';
        $order->update_meta_data('_pbc_pricing_country', $country);

        // Calculate totals
        $order->calculate_totals();

        return $order;
    }

    /**
     * Clean up after tests
     */
    public function tearDown(): void {
        // Clear cart
        WC()->cart->empty_cart();
        
        // Clean up test data
        PBC_Test_Helper::cleanup_test_data();
        
        // Clear session
        unset($_SESSION['pbc_test_country']);
        
        parent::tearDown();
    }
}