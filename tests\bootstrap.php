<?php
/**
 * PHPUnit bootstrap file for Price by Country plugin tests
 *
 * @package PriceByCountry
 */

// Define test environment
define('PBC_TESTING', true);

// WordPress test environment setup
$_tests_dir = getenv('WP_TESTS_DIR');

if (!$_tests_dir) {
    $_tests_dir = rtrim(sys_get_temp_dir(), '/\\') . '/wordpress-tests-lib';
}

if (!file_exists($_tests_dir . '/includes/functions.php')) {
    echo "Could not find $_tests_dir/includes/functions.php, have you run bin/install-wp-tests.sh ?" . PHP_EOL;
    exit(1);
}

// Give access to tests_add_filter() function
require_once $_tests_dir . '/includes/functions.php';

/**
 * Manually load the plugin being tested
 */
function _manually_load_plugin() {
    // Load WooCommerce first (if available)
    if (file_exists(WP_PLUGIN_DIR . '/woocommerce/woocommerce.php')) {
        require_once WP_PLUGIN_DIR . '/woocommerce/woocommerce.php';
    }
    
    // Load our plugin
    require dirname(dirname(__FILE__)) . '/price-by-country.php';
}
tests_add_filter('muplugins_loaded', '_manually_load_plugin');

// Start up the WP testing environment
require $_tests_dir . '/includes/bootstrap.php';

// Load plugin classes for testing
require_once dirname(dirname(__FILE__)) . '/includes/class-pbc-error-handler.php';
require_once dirname(dirname(__FILE__)) . '/includes/class-pbc-logger.php';
require_once dirname(dirname(__FILE__)) . '/includes/class-pbc-database.php';
require_once dirname(dirname(__FILE__)) . '/includes/class-pbc-country-detector.php';
require_once dirname(dirname(__FILE__)) . '/includes/class-pbc-pricing-engine.php';

// Mock WooCommerce functions if not available
if (!function_exists('wc_get_product')) {
    function wc_get_product($product_id) {
        $product = new stdClass();
        $product->id = $product_id;
        
        // Mock get_regular_price method
        $product->get_regular_price = function() use ($product_id) {
            return get_post_meta($product_id, '_regular_price', true) ?: '100.00';
        };
        
        return $product;
    }
}

if (!function_exists('get_woocommerce_currency')) {
    function get_woocommerce_currency() {
        return 'USD';
    }
}

if (!class_exists('WC_Customer')) {
    class WC_Customer {
        private $shipping_country = '';
        private $billing_country = '';
        
        public function get_shipping_country() {
            return $this->shipping_country;
        }
        
        public function get_billing_country() {
            return $this->billing_country;
        }
        
        public function set_shipping_country($country) {
            $this->shipping_country = $country;
        }
        
        public function set_billing_country($country) {
            $this->billing_country = $country;
        }
    }
}

// Test helper functions
class PBC_Test_Helper {
    
    /**
     * Create test pricing rule
     */
    public static function create_test_pricing_rule($overrides = []) {
        $defaults = [
            'rule_type' => 'product',
            'object_id' => 123,
            'country_code' => 'US',
            'adjustment_type' => 'fixed',
            'adjustment_value' => -10.00,
            'is_active' => 1
        ];
        
        return array_merge($defaults, $overrides);
    }
    
    /**
     * Create test product
     */
    public static function create_test_product($price = '100.00') {
        $product_id = wp_insert_post([
            'post_type' => 'product',
            'post_status' => 'publish',
            'post_title' => 'Test Product'
        ]);
        
        update_post_meta($product_id, '_regular_price', $price);
        update_post_meta($product_id, '_price', $price);
        
        return $product_id;
    }
    
    /**
     * Clean up test data
     */
    public static function cleanup_test_data() {
        global $wpdb;
        
        // Clean up posts
        $wpdb->query("DELETE FROM {$wpdb->posts} WHERE post_type = 'product' AND post_title LIKE 'Test%'");
        
        // Clean up meta
        $wpdb->query("DELETE FROM {$wpdb->postmeta} WHERE post_id NOT IN (SELECT ID FROM {$wpdb->posts})");
        
        // Clean up plugin tables if they exist
        $pricing_table = $wpdb->prefix . 'pbc_pricing_rules';
        $cache_table = $wpdb->prefix . 'pbc_country_cache';
        
        if ($wpdb->get_var("SHOW TABLES LIKE '$pricing_table'") === $pricing_table) {
            $wpdb->query("TRUNCATE TABLE $pricing_table");
        }
        
        if ($wpdb->get_var("SHOW TABLES LIKE '$cache_table'") === $cache_table) {
            $wpdb->query("TRUNCATE TABLE $cache_table");
        }
    }
}

// Initialize error handler and logger singletons for testing
PBC_Error_Handler::get_instance();
PBC_Logger::get_instance();

echo "PBC Test Bootstrap Loaded\n";