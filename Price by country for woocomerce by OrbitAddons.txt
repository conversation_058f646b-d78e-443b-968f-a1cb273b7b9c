Project name: Price By Country for WooCommerce by OrbitAddons

Price by Country plugin for WooCommerce allows store owners to sell products with different prices based on user's country.

Price by Country for WooCommerce empowers merchants to sell products at different rates based on shoppers’ countries. Identical pricing across regions can be costly for store owners. With Price By Country, store owners can add multiple price options — the extension detects the shoppers’ country and modifies product prices accordingly.

Key features
-Modify products’ regular prices based on the customer’s country.
-Create country-based pricing rule sets at the product, category, and global level.
-Detect the customer’s country by IP address, billing address, and shipping address.
-Increase or decrease product prices by fixed amounts, or by percentage.
-Real-time updating shows location-specific pricing on the checkout page if a customer modifies their billing or shipping address.
-Create regional pricing by product, or create global rules set to apply pricing variations for the entire store.
-Compatible with Rest API.

Usage:
The extension provides easy way to enable the regional prices for the products individually at product level, in bulk at the category level or globally.

Country-based pricing at the product level:
Create a unique rule set for each product to offer different regional pricing. The extension determines the customer’s location, and the frontend of your store fetches the specifc price from the item’s rule set. You can also disable regional pricing at the product level or inherit.

Category based pricing for all products:
Admin can create the country based pricing for the products at the category level. The category pricing is applied to the related products; Consequently, it saves time of adding pricing for each product separately.

Define pricing globally for all products
This global rule is useful to apply pricing at single setting to the complete store products, if the all product & category level settings are inheriting.

Three ways to detect customer locations:
The extension allows to detect user’s country based on the three methods, such as:
-Billing address
-IP address (Uses WooCommerce geolocation function to detect user’s country)
-Shipping address