# Getting Started with Price by Country

## Welcome! 🎉

Congratulations on choosing Price by Country for WooCommerce! This guide will help you get up and running with country-specific pricing in just 15 minutes.

## What You'll Accomplish

By the end of this guide, you'll have:

- ✅ Installed and activated the plugin
- ✅ Configured country detection
- ✅ Created your first pricing rules
- ✅ Tested everything works correctly
- ✅ Learned where to go for advanced features

## Before You Start

### Prerequisites

- WordPress 5.0 or higher
- WooCommerce 5.0 or higher
- Administrator access to your WordPress site
- At least one product in your WooCommerce store

### What You'll Need

- 15 minutes of your time
- Your plugin license key (if applicable)
- A list of countries you want to target
- Basic pricing strategy ideas

## Step 1: Installation (2 minutes)

### Download and Install

1. **Download** the plugin zip file from your purchase confirmation email
2. **Go to** WordPress Admin → Plugins → Add New
3. **Click** "Upload Plugin"
4. **Choose** your downloaded zip file
5. **Click** "Install Now"
6. **Click** "Activate Plugin"

### Automatic Setup Launch

After activation, you'll be automatically redirected to the setup wizard. If not, go to **Dashboard → Price by Country Setup**.

## Step 2: Setup Wizard (5 minutes)

### Welcome Screen

The setup wizard will guide you through the essential configuration. Click **"Let's Get Started!"**

### Country Detection Configuration

This is the most important step - how will the plugin detect your customers' countries?

**Recommended Choice: Automatic Detection**

- Uses shipping address → billing address → IP address priority
- Most accurate for returning customers
- Works immediately for new visitors

**Settings to Configure:**

- **Detection Method**: Choose "Automatic" (recommended)
- **Default Country**: Select your store's base country
- **Cache Duration**: Leave at "1 hour" (good balance)

### Sample Rules Creation

The wizard can create example pricing rules to get you started quickly.

**Recommended Settings:**

- **Create Sample Rules**: ✅ Enable this
- **Rule Type**: Choose "Percentage adjustments"
- **Countries**: The wizard will suggest common international markets

**Sample Rules Created:**

- United Kingdom: +15% (accounts for VAT and shipping)
- Canada: +5% (similar market, slight premium)
- Australia: +20% (distance and shipping costs)
- European Union: +12% (average for EU markets)

### Completion

Review the summary and click **"Complete Setup"**. You'll be taken to the plugin dashboard.

## Step 3: Review Your Setup (3 minutes)

### Check Your Rules

Go to **WooCommerce → Price by Country → Global Rules** to see what was created:

- **GB (United Kingdom)**: +15% percentage adjustment
- **CA (Canada)**: +5% percentage adjustment
- **AU (Australia)**: +20% percentage adjustment
- **EU (European Union)**: +12% percentage adjustment

These are global rules, meaning they apply to all products unless you create specific overrides.

### Understand the Dashboard

Your plugin dashboard shows:

- **Active Rules**: How many pricing rules you have
- **Countries Covered**: How many countries have specific pricing
- **Recent Activity**: Latest changes to your rules
- **Quick Actions**: Common tasks like adding rules or importing data

## Step 4: Test Your Setup (3 minutes)

### Test with VPN (Recommended)

1. **Get a VPN service** (many free options available)
2. **Connect to UK server**
3. **Visit your store** in incognito/private browsing mode
4. **Check product prices** - they should be 15% higher
5. **Test checkout process** - pricing should carry through

### Test with Browser Tools (Alternative)

1. **Open browser developer tools** (F12)
2. **Go to Console tab**
3. **Type**: `localStorage.setItem('pbc_test_country', 'GB')`
4. **Refresh the page**
5. **Check prices** - should show UK pricing

### What to Look For

- ✅ Prices increase by the expected percentage
- ✅ Currency symbol stays the same (this plugin adjusts amounts, not currencies)
- ✅ Pricing carries through to cart and checkout
- ✅ No error messages or broken functionality

## Step 5: Next Steps (2 minutes)

### Immediate Actions

1. **Test all sample countries** to ensure everything works
2. **Add more countries** if needed for your target markets
3. **Adjust sample percentages** based on your business needs
4. **Create product-specific rules** for special cases

### Recommended Learning Path

1. **Read the User Manual** - Comprehensive guide to all features
2. **Watch Video Tutorials** - Visual step-by-step instructions
3. **Explore Advanced Features** - Import/export, performance optimization
4. **Join the Community** - Connect with other users for tips and tricks

## Quick Reference Card

### Essential Links

- **Plugin Dashboard**: WooCommerce → Price by Country
- **Global Rules**: WooCommerce → Price by Country → Global Rules
- **Settings**: WooCommerce → Settings → Price by Country
- **Import/Export**: WooCommerce → Price by Country → Import/Export

### Common Tasks

- **Add New Country**: Global Rules → Add New Global Rule
- **Edit Product Pricing**: Products → Edit Product → Price by Country tab
- **Test Different Countries**: Use VPN or browser developer tools
- **Clear Cache**: Settings → Performance → Clear All Caches

### Getting Help

- **Documentation**: Complete guides and references
- **Video Tutorials**: Step-by-step visual guides
- **FAQ**: Answers to common questions
- **Support Forum**: Community help and discussions

## Troubleshooting Quick Fixes

### Prices Not Changing?

1. **Check country detection** - Enable debug mode to see detected country
2. **Verify rules exist** - Make sure you have rules for the test country
3. **Clear caches** - Clear plugin cache and browser cache
4. **Check rule status** - Ensure rules are active/enabled

### Admin Interface Not Loading?

1. **Check for plugin conflicts** - Deactivate other plugins temporarily
2. **Switch themes** - Try a default WordPress theme
3. **Check browser console** - Look for JavaScript errors
4. **Verify user permissions** - Ensure you have administrator access

### Performance Issues?

1. **Enable caching** - Turn on all caching options in settings
2. **Optimize rules** - Use fewer, more general rules when possible
3. **Check server resources** - Ensure adequate PHP memory and execution time
4. **Monitor performance** - Use debug mode to track impact

## Success Checklist

Before you consider your setup complete, make sure you can check off these items:

### Basic Functionality

- [ ] Plugin installed and activated successfully
- [ ] Setup wizard completed without errors
- [ ] Sample rules created and visible in dashboard
- [ ] Country detection working (tested with VPN or browser tools)
- [ ] Prices changing correctly for different countries
- [ ] Checkout process working with adjusted pricing

### Configuration

- [ ] Default country set to your store's base location
- [ ] Detection method appropriate for your business model
- [ ] Cache duration set appropriately
- [ ] Sample rules adjusted for your business needs

### Testing

- [ ] Tested at least 2-3 different countries
- [ ] Verified pricing calculations are correct
- [ ] Confirmed no broken functionality
- [ ] Tested on both desktop and mobile devices

### Documentation

- [ ] Bookmarked essential documentation links
- [ ] Located video tutorials for future reference
- [ ] Identified support channels for questions
- [ ] Planned next steps for advanced configuration

## What's Next?

### Immediate Priorities (This Week)

1. **Fine-tune your pricing rules** based on your business strategy
2. **Add more countries** that are important to your business
3. **Test thoroughly** with real customer scenarios
4. **Monitor performance** and customer feedback

### Short-term Goals (This Month)

1. **Learn advanced features** like category and product-specific rules
2. **Optimize performance** if you have a high-traffic site
3. **Set up monitoring** to track the impact on your business
4. **Consider seasonal adjustments** if applicable to your products

### Long-term Strategy (Ongoing)

1. **Analyze results** - track conversion rates and revenue by country
2. **Refine pricing strategy** based on performance data
3. **Expand to new markets** as your business grows
4. **Stay updated** with plugin updates and new features

## Congratulations! 🎊

You've successfully set up Price by Country for WooCommerce! Your store can now automatically show different prices to customers based on their location, helping you optimize for different markets and increase your international sales.

### Need Help?

- **Quick Questions**: Check the [FAQ](faq.md)
- **Detailed Guides**: Read the [User Manual](user-manual.md)
- **Visual Learning**: Watch our [Video Tutorials](video-tutorials/tutorial-scripts.md)
- **Community Support**: Join our user forum
- **Direct Support**: Contact our support team (for license holders)

**Happy selling!** 🚀
