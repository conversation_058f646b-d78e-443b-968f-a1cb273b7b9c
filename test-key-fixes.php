<?php
/**
 * Test key translation loading fixes
 */

echo "Testing key translation loading fixes...\n";

// Check compatibility checker
$compat_content = file_get_contents('includes/class-pbc-compatibility-checker.php');
$has_lazy_loading = strpos($compat_content, 'if (empty($this->test_results)) {') !== false;
$has_helper = strpos($compat_content, 'private function get_text(') !== false;

echo "Compatibility checker: " . ($has_lazy_loading && $has_helper ? "✓ Fixed" : "✗ Issue") . "\n";

// Check admin menu wrapper
$admin_content = file_get_contents('includes/class-pbc-admin.php');
$has_wrapper = strpos($admin_content, 'add_admin_menu_wrapper') !== false;
$has_init_check = strpos($admin_content, "did_action('init') === 0") !== false;

echo "Admin menu wrapper: " . ($has_wrapper && $has_init_check ? "✓ Fixed" : "✗ Issue") . "\n";

// Check setup wizard
$setup_content = file_get_contents('includes/class-pbc-setup-wizard.php');
$has_step_helper = strpos($setup_content, 'get_step_name(') !== false;

echo "Setup wizard: " . ($has_step_helper ? "✓ Fixed" : "✗ Issue") . "\n";

echo "\n✓ Key fixes verification completed!\n";