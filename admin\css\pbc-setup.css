/**
 * Setup Wizard Styles
 */

.pbc-setup {
  background: #f1f1f1;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
}

.pbc-setup *,
.pbc-setup *::before,
.pbc-setup *::after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

#pbc-logo {
  border: 0;
  margin: 0 0 24px 0;
  padding: 0;
  text-align: center;
}

#pbc-logo a {
  background: #96588a;
  color: #fff;
  text-decoration: none;
  display: inline-block;
  padding: 1em 2em;
  font-weight: 600;
  font-size: 18px;
  line-height: 1;
  border-radius: 3px;
}

.pbc-setup-steps {
  list-style: none outside;
  overflow: hidden;
  margin: 0 0 24px 0;
  padding: 0;
  display: table;
  width: 100%;
  table-layout: fixed;
  border-spacing: 0;
  border-collapse: separate;
}

.pbc-setup-steps li {
  display: table-cell;
  width: 25%;
  float: none;
  padding: 0;
  margin: 0;
  text-align: center;
  position: relative;
  background-color: #fff;
  color: #999;
  border: 4px solid #f1f1f1;
  border-right-width: 0;
  line-height: 1.4em;
}

.pbc-setup-steps li::before {
  content: "";
  border: 4px solid #f1f1f1;
  border-left-color: #fff;
  border-right-color: transparent;
  border-top-color: transparent;
  border-bottom-color: transparent;
  position: absolute;
  top: -4px;
  right: -8px;
  z-index: 2;
  width: 0;
  height: 0;
  border-width: 27px 0 27px 27px;
}

.pbc-setup-steps li::after {
  content: "";
  border: 4px solid #f1f1f1;
  border-left-color: #f1f1f1;
  border-right-color: transparent;
  border-top-color: transparent;
  border-bottom-color: transparent;
  position: absolute;
  top: -4px;
  right: -12px;
  z-index: 1;
  width: 0;
  height: 0;
  border-width: 27px 0 27px 27px;
}

.pbc-setup-steps li:first-child {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.pbc-setup-steps li:last-child {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
  border-right-width: 4px;
}

.pbc-setup-steps li:last-child::before,
.pbc-setup-steps li:last-child::after {
  display: none;
}

.pbc-setup-steps li a {
  display: inline-block;
  padding: 1em;
  text-decoration: none;
  color: inherit;
  width: 100%;
}

.pbc-setup-steps .active {
  background: #96588a;
  color: #fff;
  border-color: #96588a;
}

.pbc-setup-steps .active::before {
  border-left-color: #96588a;
}

.pbc-setup-steps .done {
  background: #a16696;
  color: #fff;
  border-color: #a16696;
}

.pbc-setup-steps .done::before {
  border-left-color: #a16696;
}

.pbc-setup-content {
  background: #fff;
  overflow: hidden;
  padding: 2em;
  margin: 0 0 24px 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.13);
  border-radius: 3px;
}

.pbc-setup-content h1,
.pbc-setup-content h2,
.pbc-setup-content h3 {
  line-height: 1.4em;
  margin: 0 0 1em 0;
  color: #666;
}

.pbc-setup-content h1 {
  font-size: 2em;
  line-height: 1.2em;
  color: #96588a;
}

.pbc-setup-content p {
  font-size: 1em;
  line-height: 1.75em;
  color: #666;
  margin: 0 0 1.75em 0;
}

.pbc-setup-content table {
  border: 0;
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
  margin: 0 0 24px 0;
}

.pbc-setup-content table th {
  width: 25%;
  vertical-align: top;
  padding: 12px 0;
  line-height: 1.4em;
  font-weight: 600;
}

.pbc-setup-content table td {
  vertical-align: top;
  padding: 12px 0;
  line-height: 1.4em;
}

.pbc-setup-content table td input,
.pbc-setup-content table td select {
  width: 100%;
  max-width: 400px;
}

.pbc-setup-content .form-table th {
  padding-left: 0;
  padding-right: 24px;
}

.pbc-setup-actions {
  overflow: hidden;
  line-height: 1em;
  margin: 0;
  padding: 0;
  text-align: right;
}

.pbc-setup-actions .button {
  margin-left: 0.5em;
  margin-right: 0;
  margin-bottom: 0;
}

.pbc-setup-actions .button-primary {
  background: #96588a;
  border-color: #96588a;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.25), 0 1px 0 #96588a;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.25), 0 1px 0 #96588a;
}

.pbc-setup-actions .button-primary:hover,
.pbc-setup-actions .button-primary:focus {
  background: #a16696;
  border-color: #a16696;
}

.pbc-return-to-dashboard {
  margin: 0;
  padding: 0;
  color: #a16696;
  text-decoration: none;
  line-height: 54px;
}

.pbc-return-to-dashboard:hover,
.pbc-return-to-dashboard:focus {
  color: #96588a;
}

.pbc-sample-preview {
  background: #f9f9f9;
  border: 1px solid #e1e1e1;
  border-radius: 3px;
  padding: 20px;
  margin: 20px 0;
}

.pbc-sample-preview h3 {
  margin-top: 0;
  color: #96588a;
}

.pbc-sample-preview table {
  margin: 0;
}

.pbc-sample-preview table th,
.pbc-sample-preview table td {
  padding: 8px 12px;
  border-bottom: 1px solid #e1e1e1;
}

.pbc-sample-preview table th {
  background: #f1f1f1;
  font-weight: 600;
}

.pbc-setup-next-steps {
  overflow: hidden;
  margin: 0 0 24px 0;
}

.pbc-setup-next-steps-first {
  float: left;
  width: 50%;
  padding-right: 24px;
}

.pbc-setup-next-steps-last {
  float: right;
  width: 50%;
  padding-left: 24px;
}

.pbc-setup-next-steps ul {
  padding: 0;
  margin: 0;
  list-style: none outside;
}

.pbc-setup-next-steps li {
  margin: 0 0 24px 0;
  padding: 0;
  list-style: none outside;
}

.pbc-setup-next-steps h3 {
  margin: 8px 0 4px 0;
  font-size: 1.1em;
}

.pbc-setup-next-steps p {
  margin: 0 0 8px 0;
  font-size: 0.9em;
  line-height: 1.5em;
}

.pbc-setup-next-steps .button {
  margin-bottom: 4px;
}

/* Responsive */
@media screen and (max-width: 782px) {
  .pbc-setup-steps {
    display: block;
  }

  .pbc-setup-steps li {
    display: block;
    width: 100%;
    border-right-width: 4px;
    border-bottom-width: 0;
  }

  .pbc-setup-steps li::before,
  .pbc-setup-steps li::after {
    display: none;
  }

  .pbc-setup-steps li:first-child {
    border-radius: 6px 6px 0 0;
  }

  .pbc-setup-steps li:last-child {
    border-radius: 0 0 6px 6px;
    border-bottom-width: 4px;
  }

  .pbc-setup-content {
    padding: 1em;
  }

  .pbc-setup-content table th,
  .pbc-setup-content table td {
    display: block;
    width: 100%;
    padding: 6px 0;
  }

  .pbc-setup-content .form-table th {
    padding-right: 0;
  }

  .pbc-setup-next-steps-first,
  .pbc-setup-next-steps-last {
    float: none;
    width: 100%;
    padding: 0;
  }

  .pbc-setup-next-steps-last {
    margin-top: 24px;
  }
}

@media screen and (max-width: 480px) {
  .pbc-setup-actions {
    text-align: center;
  }

  .pbc-setup-actions .button {
    display: block;
    width: 100%;
    margin: 0 0 1em 0;
  }
}
