<?php
/**
 * REST API extensions for Price by Country
 *
 * @package PriceByCountry
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PBC API Class
 */
class PBC_API {

    /**
     * Pricing engine instance
     *
     * @var PBC_Pricing_Engine
     */
    private $pricing_engine;

    /**
     * Database instance
     *
     * @var PBC_Database
     */
    private $database;

    /**
     * Constructor
     *
     * @param PBC_Pricing_Engine $pricing_engine Pricing engine instance
     * @param PBC_Database $database Database instance
     */
    public function __construct($pricing_engine, $database) {
        $this->pricing_engine = $pricing_engine;
        $this->database = $database;
        
        $this->init_hooks();
    }

    /**
     * Initialize API hooks
     */
    private function init_hooks() {
        // Extend WooCommerce REST API product endpoints
        add_filter('woocommerce_rest_prepare_product_object', array($this, 'add_country_pricing_to_product_response'), 10, 3);
        add_filter('woocommerce_rest_prepare_product_variation_object', array($this, 'add_country_pricing_to_product_response'), 10, 3);
        
        // Add country parameter support to product requests
        add_filter('woocommerce_rest_product_object_query', array($this, 'handle_country_parameter'), 10, 2);
        
        // Register custom REST API endpoints
        add_action('rest_api_init', array($this, 'register_custom_endpoints'));
    }

    /**
     * Add country-based pricing data to product API responses
     *
     * @param WP_REST_Response $response The response object
     * @param WC_Product $product Product object
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response
     */
    public function add_country_pricing_to_product_response($response, $product, $request) {
        $data = $response->get_data();
        
        try {
            // Get country from request parameter or use default
            $country = $request->get_param('country');
            
            if ($country) {
                // Validate country code
                if (strlen($country) === 2 && preg_match('/^[A-Z]{2}$/', strtoupper($country))) {
                    $country = strtoupper($country);
                    
                    // Add country-specific pricing
                    $country_pricing = $this->get_country_pricing_for_product($product->get_id(), $country);
                    $data['country_pricing'] = $country_pricing;
                    
                    // Override regular price and sale price with country-specific values
                    if (!empty($country_pricing['adjusted_price']) && $country_pricing['adjusted_price'] != $country_pricing['original_price']) {
                        $data['price'] = $country_pricing['adjusted_price'];
                        $data['regular_price'] = $country_pricing['adjusted_price'];
                    }
                } else {
                    // Invalid country code - add error info but don't break the response
                    $data['country_pricing_error'] = 'Invalid country code provided';
                }
            } else {
                // Add all available country pricing rules
                $all_country_pricing = $this->get_all_country_pricing_for_product($product->get_id());
                if (!empty($all_country_pricing)) {
                    $data['country_pricing_rules'] = $all_country_pricing;
                }
            }
            
        } catch (Exception $e) {
            // Log error but don't break the product response
            error_log('PBC API Error: ' . $e->getMessage());
            $data['country_pricing_error'] = 'Error loading country pricing data';
        }
        
        $response->set_data($data);
        return $response;
    }

    /**
     * Handle country parameter in product queries
     *
     * @param array $args Query arguments
     * @param WP_REST_Request $request Request object
     * @return array
     */
    public function handle_country_parameter($args, $request) {
        $country = $request->get_param('country');
        
        if ($country) {
            // Store country in request for later use
            $request->set_param('_pbc_country', strtoupper($country));
        }
        
        return $args;
    }

    /**
     * Register custom REST API endpoints for pricing rule management
     */
    public function register_custom_endpoints() {
        // Pricing rules endpoint
        register_rest_route('pbc/v1', '/pricing-rules', array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'get_pricing_rules'),
                'permission_callback' => array($this, 'check_admin_permissions'),
                'args' => array(
                    'rule_type' => array(
                        'description' => 'Type of pricing rule (global, category, product)',
                        'type' => 'string',
                        'enum' => array('global', 'category', 'product'),
                    ),
                    'object_id' => array(
                        'description' => 'Object ID for category or product rules',
                        'type' => 'integer',
                    ),
                    'country_code' => array(
                        'description' => 'Country code filter',
                        'type' => 'string',
                    ),
                ),
            ),
            array(
                'methods' => WP_REST_Server::CREATABLE,
                'callback' => array($this, 'create_pricing_rule'),
                'permission_callback' => array($this, 'check_admin_permissions'),
                'args' => $this->get_pricing_rule_schema(),
            ),
        ));

        // Individual pricing rule endpoint
        register_rest_route('pbc/v1', '/pricing-rules/(?P<id>\d+)', array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'get_pricing_rule'),
                'permission_callback' => array($this, 'check_admin_permissions'),
            ),
            array(
                'methods' => WP_REST_Server::EDITABLE,
                'callback' => array($this, 'update_pricing_rule'),
                'permission_callback' => array($this, 'check_admin_permissions'),
                'args' => $this->get_pricing_rule_schema(),
            ),
            array(
                'methods' => WP_REST_Server::DELETABLE,
                'callback' => array($this, 'delete_pricing_rule'),
                'permission_callback' => array($this, 'check_admin_permissions'),
            ),
        ));

        // Product country pricing endpoint
        register_rest_route('pbc/v1', '/products/(?P<id>\d+)/country-pricing', array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'get_product_country_pricing'),
                'permission_callback' => array($this, 'check_read_permissions'),
                'args' => array(
                    'country' => array(
                        'description' => 'Country code',
                        'type' => 'string',
                        'required' => false,
                        'validate_callback' => array($this, 'validate_country_code'),
                        'sanitize_callback' => array($this, 'sanitize_country_code'),
                    ),
                ),
            ),
        ));
    }

    /**
     * Get country pricing for a specific product and country
     *
     * @param int $product_id Product ID
     * @param string $country_code Country code
     * @return array
     */
    private function get_country_pricing_for_product($product_id, $country_code) {
        $product = wc_get_product($product_id);
        if (!$product) {
            return array();
        }

        $original_price = $product->get_regular_price();
        $adjusted_price = $this->pricing_engine->get_country_price($product_id, $country_code);
        
        $pricing_data = array(
            'country_code' => strtoupper($country_code),
            'original_price' => $original_price,
            'adjusted_price' => $adjusted_price,
        );

        // Add adjustment details if price was modified
        if ($adjusted_price != $original_price) {
            $rule = $this->database->get_applicable_rule($product_id, $country_code);
            if ($rule) {
                $pricing_data['adjustment'] = array(
                    'type' => $rule->adjustment_type,
                    'value' => $rule->adjustment_value,
                    'rule_source' => $rule->rule_type,
                    'rule_id' => $rule->id,
                );
            }
        }

        // Ensure response consistency
        return $this->ensure_response_consistency($pricing_data);
    }

    /**
     * Get all country pricing rules for a product
     *
     * @param int $product_id Product ID
     * @return array
     */
    private function get_all_country_pricing_for_product($product_id) {
        $rules = $this->database->get_product_pricing_rules($product_id);
        $country_pricing = array();

        foreach ($rules as $rule) {
            $country_pricing[] = array(
                'country_code' => $rule->country_code,
                'adjustment_type' => $rule->adjustment_type,
                'adjustment_value' => $rule->adjustment_value,
                'rule_type' => $rule->rule_type,
                'rule_id' => $rule->id,
                'is_active' => (bool) $rule->is_active,
            );
        }

        return $country_pricing;
    }

    /**
     * Get pricing rules via REST API
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response|WP_Error
     */
    public function get_pricing_rules($request) {
        $rule_type = $request->get_param('rule_type');
        $object_id = $request->get_param('object_id');
        $country_code = $request->get_param('country_code');

        $rules = $this->database->get_pricing_rules($rule_type, $object_id, $country_code);
        
        $formatted_rules = array();
        foreach ($rules as $rule) {
            $formatted_rules[] = $this->format_pricing_rule_for_response($rule);
        }

        return rest_ensure_response($formatted_rules);
    }

    /**
     * Get single pricing rule via REST API
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response|WP_Error
     */
    public function get_pricing_rule($request) {
        $rule_id = (int) $request['id'];
        $rule = $this->database->get_pricing_rule($rule_id);

        if (!$rule) {
            return new WP_Error('pbc_rule_not_found', 'Pricing rule not found', array('status' => 404));
        }

        return rest_ensure_response($this->format_pricing_rule_for_response($rule));
    }

    /**
     * Create pricing rule via REST API
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response|WP_Error
     */
    public function create_pricing_rule($request) {
        // Validate required parameters
        $validation = $this->validate_request_data($request, array('rule_type', 'country_code', 'adjustment_type', 'adjustment_value'));
        if (is_wp_error($validation)) {
            return $validation;
        }

        // Check for duplicate rules
        $existing_rule = $this->database->get_pricing_rule_by_criteria(
            $request->get_param('rule_type'),
            $request->get_param('object_id'),
            strtoupper($request->get_param('country_code'))
        );

        if ($existing_rule) {
            return $this->create_api_error(
                'pbc_duplicate_rule',
                'A pricing rule already exists for this combination of rule type, object, and country',
                409,
                array('existing_rule_id' => $existing_rule->id)
            );
        }

        $rule_data = array(
            'rule_type' => $request->get_param('rule_type'),
            'object_id' => $request->get_param('object_id'),
            'country_code' => strtoupper($request->get_param('country_code')),
            'adjustment_type' => $request->get_param('adjustment_type'),
            'adjustment_value' => $request->get_param('adjustment_value'),
            'is_active' => $request->get_param('is_active') !== false,
        );

        $rule_id = $this->database->save_pricing_rule($rule_data);
        
        if (!$rule_id) {
            return $this->create_api_error(
                'pbc_rule_creation_failed',
                'Failed to create pricing rule. Please check your data and try again.',
                500
            );
        }

        $rule = $this->database->get_pricing_rule($rule_id);
        
        $response = rest_ensure_response($this->format_pricing_rule_for_response($rule));
        $response->set_status(201); // Created
        
        return $response;
    }

    /**
     * Update pricing rule via REST API
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response|WP_Error
     */
    public function update_pricing_rule($request) {
        $rule_id = (int) $request['id'];
        $existing_rule = $this->database->get_pricing_rule($rule_id);

        if (!$existing_rule) {
            return $this->create_api_error(
                'pbc_rule_not_found',
                'Pricing rule not found',
                404
            );
        }

        // Check for conflicts if key fields are being changed
        $new_rule_type = $request->get_param('rule_type') ?: $existing_rule->rule_type;
        $new_object_id = $request->get_param('object_id') !== null ? $request->get_param('object_id') : $existing_rule->object_id;
        $new_country_code = $request->get_param('country_code') ? strtoupper($request->get_param('country_code')) : $existing_rule->country_code;

        // Check if this would create a duplicate
        if ($new_rule_type !== $existing_rule->rule_type || 
            $new_object_id != $existing_rule->object_id || 
            $new_country_code !== $existing_rule->country_code) {
            
            $conflicting_rule = $this->database->get_pricing_rule_by_criteria($new_rule_type, $new_object_id, $new_country_code);
            if ($conflicting_rule && $conflicting_rule->id != $rule_id) {
                return $this->create_api_error(
                    'pbc_duplicate_rule',
                    'Update would create a duplicate rule',
                    409,
                    array('conflicting_rule_id' => $conflicting_rule->id)
                );
            }
        }

        $rule_data = array(
            'id' => $rule_id,
            'rule_type' => $new_rule_type,
            'object_id' => $new_object_id,
            'country_code' => $new_country_code,
            'adjustment_type' => $request->get_param('adjustment_type') ?: $existing_rule->adjustment_type,
            'adjustment_value' => $request->get_param('adjustment_value') !== null ? $request->get_param('adjustment_value') : $existing_rule->adjustment_value,
            'is_active' => $request->get_param('is_active') !== null ? $request->get_param('is_active') : $existing_rule->is_active,
        );

        $updated = $this->database->save_pricing_rule($rule_data);
        
        if (!$updated) {
            return $this->create_api_error(
                'pbc_rule_update_failed',
                'Failed to update pricing rule. Please check your data and try again.',
                500
            );
        }

        $rule = $this->database->get_pricing_rule($rule_id);
        return rest_ensure_response($this->format_pricing_rule_for_response($rule));
    }

    /**
     * Delete pricing rule via REST API
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response|WP_Error
     */
    public function delete_pricing_rule($request) {
        $rule_id = (int) $request['id'];
        $rule = $this->database->get_pricing_rule($rule_id);

        if (!$rule) {
            return $this->create_api_error(
                'pbc_rule_not_found',
                'Pricing rule not found',
                404
            );
        }

        $deleted = $this->database->delete_pricing_rule($rule_id);
        
        if (!$deleted) {
            return $this->create_api_error(
                'pbc_rule_deletion_failed',
                'Failed to delete pricing rule. The rule may be in use or protected.',
                500
            );
        }

        return rest_ensure_response(array(
            'deleted' => true, 
            'previous' => $this->format_pricing_rule_for_response($rule),
            'message' => 'Pricing rule deleted successfully'
        ));
    }

    /**
     * Get product country pricing via REST API
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response|WP_Error
     */
    public function get_product_country_pricing($request) {
        $product_id = (int) $request['id'];
        $country = $request->get_param('country');

        $product = wc_get_product($product_id);
        if (!$product) {
            return $this->create_api_error(
                'pbc_product_not_found',
                'Product not found',
                404
            );
        }

        try {
            if ($country) {
                $pricing_data = $this->get_country_pricing_for_product($product_id, $country);
                
                // Add product context
                $pricing_data['product'] = array(
                    'id' => $product_id,
                    'name' => $product->get_name(),
                    'sku' => $product->get_sku(),
                    'type' => $product->get_type(),
                );
            } else {
                $pricing_data = $this->get_all_country_pricing_for_product($product_id);
                
                // Add product context
                $pricing_data = array(
                    'product' => array(
                        'id' => $product_id,
                        'name' => $product->get_name(),
                        'sku' => $product->get_sku(),
                        'type' => $product->get_type(),
                    ),
                    'pricing_rules' => $pricing_data,
                );
            }

            return rest_ensure_response($pricing_data);
            
        } catch (Exception $e) {
            return $this->create_api_error(
                'pbc_pricing_calculation_error',
                'Error calculating pricing data: ' . $e->getMessage(),
                500
            );
        }
    }

    /**
     * Check admin permissions for API endpoints
     *
     * @param WP_REST_Request $request Request object
     * @return bool|WP_Error
     */
    public function check_admin_permissions($request = null) {
        // Check if user has WooCommerce management capabilities
        if (!current_user_can('manage_woocommerce')) {
            return new WP_Error(
                'pbc_insufficient_permissions',
                'You do not have permission to manage pricing rules.',
                array('status' => 403)
            );
        }

        // Additional validation for specific operations
        if ($request) {
            $method = $request->get_method();
            
            // Require higher permissions for destructive operations
            if (in_array($method, array('DELETE', 'POST')) && !current_user_can('manage_options')) {
                return new WP_Error(
                    'pbc_insufficient_permissions',
                    'You do not have permission to create or delete pricing rules.',
                    array('status' => 403)
                );
            }
        }

        return true;
    }

    /**
     * Check read permissions for public API endpoints
     *
     * @param WP_REST_Request $request Request object
     * @return bool|WP_Error
     */
    public function check_read_permissions($request = null) {
        // Allow public read access for product pricing data
        return true;
    }

    /**
     * Get pricing rule schema for API validation
     *
     * @return array
     */
    private function get_pricing_rule_schema() {
        return array(
            'rule_type' => array(
                'description' => 'Type of pricing rule',
                'type' => 'string',
                'enum' => array('global', 'category', 'product'),
                'required' => true,
                'validate_callback' => array($this, 'validate_rule_type'),
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'object_id' => array(
                'description' => 'Object ID (null for global, category ID or product ID)',
                'type' => 'integer',
                'validate_callback' => array($this, 'validate_object_id'),
                'sanitize_callback' => 'absint',
            ),
            'country_code' => array(
                'description' => 'ISO 2-letter country code',
                'type' => 'string',
                'pattern' => '^[A-Z]{2}$',
                'required' => true,
                'validate_callback' => array($this, 'validate_country_code'),
                'sanitize_callback' => array($this, 'sanitize_country_code'),
            ),
            'adjustment_type' => array(
                'description' => 'Type of price adjustment',
                'type' => 'string',
                'enum' => array('fixed', 'percentage'),
                'required' => true,
                'validate_callback' => array($this, 'validate_adjustment_type'),
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'adjustment_value' => array(
                'description' => 'Adjustment value',
                'type' => 'number',
                'required' => true,
                'validate_callback' => array($this, 'validate_adjustment_value'),
                'sanitize_callback' => array($this, 'sanitize_adjustment_value'),
            ),
            'is_active' => array(
                'description' => 'Whether the rule is active',
                'type' => 'boolean',
                'default' => true,
                'sanitize_callback' => 'rest_sanitize_boolean',
            ),
        );
    }

    // ========================================
    // VALIDATION METHODS
    // ========================================

    /**
     * Validate rule type
     *
     * @param string $value Rule type value
     * @param WP_REST_Request $request Request object
     * @param string $param Parameter name
     * @return bool|WP_Error
     */
    public function validate_rule_type($value, $request, $param) {
        $valid_types = array('global', 'category', 'product');
        
        if (!in_array($value, $valid_types)) {
            return new WP_Error(
                'pbc_invalid_rule_type',
                sprintf('Rule type must be one of: %s', implode(', ', $valid_types)),
                array('status' => 400)
            );
        }

        return true;
    }

    /**
     * Validate object ID
     *
     * @param int $value Object ID value
     * @param WP_REST_Request $request Request object
     * @param string $param Parameter name
     * @return bool|WP_Error
     */
    public function validate_object_id($value, $request, $param) {
        $rule_type = $request->get_param('rule_type');

        if ($rule_type === 'global') {
            // Global rules should not have object_id
            if ($value !== null && $value !== 0) {
                return new WP_Error(
                    'pbc_invalid_object_id',
                    'Global rules cannot have an object_id',
                    array('status' => 400)
                );
            }
        } elseif ($rule_type === 'product') {
            // Product rules must have valid product ID
            if (!$value || !wc_get_product($value)) {
                return new WP_Error(
                    'pbc_invalid_product_id',
                    'Invalid product ID provided',
                    array('status' => 400)
                );
            }
        } elseif ($rule_type === 'category') {
            // Category rules must have valid category ID
            if (!$value || !term_exists($value, 'product_cat')) {
                return new WP_Error(
                    'pbc_invalid_category_id',
                    'Invalid category ID provided',
                    array('status' => 400)
                );
            }
        }

        return true;
    }

    /**
     * Validate country code
     *
     * @param string $value Country code value
     * @param WP_REST_Request $request Request object
     * @param string $param Parameter name
     * @return bool|WP_Error
     */
    public function validate_country_code($value, $request, $param) {
        if (strlen($value) !== 2) {
            return new WP_Error(
                'pbc_invalid_country_code',
                'Country code must be exactly 2 characters',
                array('status' => 400)
            );
        }

        if (!preg_match('/^[A-Z]{2}$/', $value)) {
            return new WP_Error(
                'pbc_invalid_country_code',
                'Country code must contain only uppercase letters',
                array('status' => 400)
            );
        }

        // Validate against WooCommerce countries list
        $wc_countries = WC()->countries->get_countries();
        if (!array_key_exists($value, $wc_countries)) {
            return new WP_Error(
                'pbc_unsupported_country',
                'Country code is not supported by WooCommerce',
                array('status' => 400)
            );
        }

        return true;
    }

    /**
     * Validate adjustment type
     *
     * @param string $value Adjustment type value
     * @param WP_REST_Request $request Request object
     * @param string $param Parameter name
     * @return bool|WP_Error
     */
    public function validate_adjustment_type($value, $request, $param) {
        $valid_types = array('fixed', 'percentage');
        
        if (!in_array($value, $valid_types)) {
            return new WP_Error(
                'pbc_invalid_adjustment_type',
                sprintf('Adjustment type must be one of: %s', implode(', ', $valid_types)),
                array('status' => 400)
            );
        }

        return true;
    }

    /**
     * Validate adjustment value
     *
     * @param mixed $value Adjustment value
     * @param WP_REST_Request $request Request object
     * @param string $param Parameter name
     * @return bool|WP_Error
     */
    public function validate_adjustment_value($value, $request, $param) {
        if (!is_numeric($value)) {
            return new WP_Error(
                'pbc_invalid_adjustment_value',
                'Adjustment value must be a number',
                array('status' => 400)
            );
        }

        $adjustment_type = $request->get_param('adjustment_type');
        
        if ($adjustment_type === 'percentage') {
            // Percentage adjustments should be reasonable
            if ($value < -100 || $value > 1000) {
                return new WP_Error(
                    'pbc_invalid_percentage',
                    'Percentage adjustment must be between -100 and 1000',
                    array('status' => 400)
                );
            }
        } elseif ($adjustment_type === 'fixed') {
            // Fixed adjustments should not be extremely large negative values
            if ($value < -999999) {
                return new WP_Error(
                    'pbc_invalid_fixed_amount',
                    'Fixed adjustment value is too large',
                    array('status' => 400)
                );
            }
        }

        return true;
    }

    // ========================================
    // SANITIZATION METHODS
    // ========================================

    /**
     * Sanitize country code
     *
     * @param string $value Country code value
     * @return string
     */
    public function sanitize_country_code($value) {
        return strtoupper(sanitize_text_field($value));
    }

    /**
     * Sanitize adjustment value
     *
     * @param mixed $value Adjustment value
     * @return float
     */
    public function sanitize_adjustment_value($value) {
        return floatval($value);
    }

    /**
     * Format pricing rule for API response
     *
     * @param object $rule Pricing rule object
     * @return array
     */
    private function format_pricing_rule_for_response($rule) {
        $formatted_rule = array(
            'id' => (int) $rule->id,
            'rule_type' => $rule->rule_type,
            'object_id' => $rule->object_id ? (int) $rule->object_id : null,
            'country_code' => $rule->country_code,
            'adjustment_type' => $rule->adjustment_type,
            'adjustment_value' => (float) $rule->adjustment_value,
            'is_active' => (bool) $rule->is_active,
            'created_at' => $rule->created_at,
            'updated_at' => $rule->updated_at,
        );

        // Add object details for better API response
        if ($rule->rule_type === 'product' && $rule->object_id) {
            $product = wc_get_product($rule->object_id);
            if ($product) {
                $formatted_rule['object_details'] = array(
                    'name' => $product->get_name(),
                    'sku' => $product->get_sku(),
                    'type' => $product->get_type(),
                );
            }
        } elseif ($rule->rule_type === 'category' && $rule->object_id) {
            $term = get_term($rule->object_id, 'product_cat');
            if ($term && !is_wp_error($term)) {
                $formatted_rule['object_details'] = array(
                    'name' => $term->name,
                    'slug' => $term->slug,
                    'count' => $term->count,
                );
            }
        }

        // Add country details
        $wc_countries = WC()->countries->get_countries();
        if (isset($wc_countries[$rule->country_code])) {
            $formatted_rule['country_name'] = $wc_countries[$rule->country_code];
        }

        return $formatted_rule;
    }

    // ========================================
    // ERROR HANDLING METHODS
    // ========================================

    /**
     * Handle API errors consistently
     *
     * @param string $error_code Error code
     * @param string $error_message Error message
     * @param int $status_code HTTP status code
     * @param array $additional_data Additional error data
     * @return WP_Error
     */
    private function create_api_error($error_code, $error_message, $status_code = 400, $additional_data = array()) {
        $error_data = array_merge(array(
            'status' => $status_code,
            'timestamp' => current_time('mysql'),
        ), $additional_data);

        return new WP_Error($error_code, $error_message, $error_data);
    }

    /**
     * Validate request data before processing
     *
     * @param WP_REST_Request $request Request object
     * @param array $required_params Required parameters
     * @return bool|WP_Error
     */
    private function validate_request_data($request, $required_params = array()) {
        foreach ($required_params as $param) {
            if (!$request->has_param($param) || $request->get_param($param) === null) {
                return $this->create_api_error(
                    'pbc_missing_parameter',
                    sprintf('Missing required parameter: %s', $param),
                    400
                );
            }
        }

        return true;
    }

    /**
     * Ensure response consistency with frontend pricing display
     *
     * @param array $pricing_data Pricing data
     * @return array Formatted pricing data
     */
    private function ensure_response_consistency($pricing_data) {
        // Ensure prices are formatted consistently
        if (isset($pricing_data['original_price'])) {
            $pricing_data['original_price'] = wc_format_decimal($pricing_data['original_price'], wc_get_price_decimals());
        }

        if (isset($pricing_data['adjusted_price'])) {
            $pricing_data['adjusted_price'] = wc_format_decimal($pricing_data['adjusted_price'], wc_get_price_decimals());
        }

        // Add currency information
        $pricing_data['currency'] = get_woocommerce_currency();
        $pricing_data['currency_symbol'] = get_woocommerce_currency_symbol();

        // Add formatting information
        $pricing_data['price_format'] = array(
            'decimals' => wc_get_price_decimals(),
            'decimal_separator' => wc_get_price_decimal_separator(),
            'thousand_separator' => wc_get_price_thousand_separator(),
            'currency_position' => get_option('woocommerce_currency_pos'),
        );

        return $pricing_data;
    }
}