<?php
/**
 * Autoloader for Price by Country plugin classes
 *
 * @package PriceByCountry
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PBC Autoloader Class
 */
class PBC_Autoloader {

    /**
     * Register the autoloader
     */
    public static function register() {
        spl_autoload_register(array(__CLASS__, 'autoload'));
    }

    /**
     * Autoload classes
     *
     * @param string $class_name The class name to load
     */
    public static function autoload($class_name) {
        // Only autoload PBC classes
        if (strpos($class_name, 'PBC_') !== 0) {
            return;
        }

        // Convert class name to file name
        $file_name = 'class-' . str_replace('_', '-', strtolower($class_name)) . '.php';
        
        // Define possible directories
        $directories = array(
            PBC_PLUGIN_DIR . 'includes/',
            PBC_PLUGIN_DIR . 'admin/',
            PBC_PLUGIN_DIR . 'public/',
        );

        // Try to find and include the file
        foreach ($directories as $directory) {
            $file_path = $directory . $file_name;
            if (file_exists($file_path)) {
                require_once $file_path;
                return;
            }
        }
    }
}

// Register the autoloader
PBC_Autoloader::register();