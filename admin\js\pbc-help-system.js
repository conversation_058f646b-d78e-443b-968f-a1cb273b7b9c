/**
 * Interactive Help System for Price by Country Plugin
 * Provides contextual tooltips and guidance throughout the admin interface
 */

(function ($) {
  "use strict";

  // Help system configuration
  const PBC_Help = {
    tooltips: {},
    tours: {},
    initialized: false,

    init: function () {
      if (this.initialized) return;

      this.loadTooltips();
      this.initializeTours();
      this.bindEvents();
      this.initialized = true;
    },

    // Load contextual tooltips
    loadTooltips: function () {
      this.tooltips = {
        "country-detection-method": {
          title: "Country Detection Method",
          content:
            "Choose how to detect customer location:<br>" +
            "<strong>Automatic:</strong> Uses shipping → billing → IP priority<br>" +
            "<strong>IP Only:</strong> Uses visitor IP address<br>" +
            "<strong>Billing:</strong> Uses billing country<br>" +
            "<strong>Shipping:</strong> Uses shipping country",
          position: "right",
        },

        "adjustment-type": {
          title: "Adjustment Type",
          content:
            "<strong>Fixed Amount:</strong> Add/subtract specific dollar amount<br>" +
            "<strong>Percentage:</strong> Increase/decrease by percentage<br>" +
            "Example: +$5 (fixed) or +15% (percentage)",
          position: "top",
        },

        "rule-priority": {
          title: "Rule Priority",
          content:
            "Higher numbers take precedence when multiple rules apply.<br>" +
            "Product rules (highest) → Category rules → Global rules (lowest)",
          position: "left",
        },
      };
    },

    // Initialize guided tours
    initializeTours: function () {
      this.tours = {
        "first-setup": [
          {
            element: "#pbc-setup-wizard",
            title: "Welcome to Price by Country!",
            content:
              "Let's set up your first pricing rules in just a few steps.",
          },
          {
            element: "#country-detection-settings",
            title: "Country Detection",
            content:
              "This determines how we identify where your customers are located.",
          },
        ],
      };
    },
  };

  // Initialize when document is ready
  $(document).ready(function () {
    PBC_Help.init();
  });
})(jQuery);
        // Bind help system events
        bindEvents: function() {
            const self = this;
            
            // Show tooltips on hover
            $(document).on('mouseenter', '[data-pbc-help]', function() {
                const helpKey = $(this).data('pbc-help');
                if (self.tooltips[helpKey]) {
                    self.showTooltip($(this), self.tooltips[helpKey]);
                }
            });

            // Hide tooltips on mouse leave
            $(document).on('mouseleave', '[data-pbc-help]', function() {
                self.hideTooltip();
            });

            // Start guided tour
            $(document).on('click', '[data-pbc-tour]', function(e) {
                e.preventDefault();
                const tourKey = $(this).data('pbc-tour');
                if (self.tours[tourKey]) {
                    self.startTour(tourKey);
                }
            });

            // Help button clicks
            $(document).on('click', '.pbc-help-button', function(e) {
                e.preventDefault();
                self.showContextualHelp($(this));
            });
        },

        // Show tooltip
        showTooltip: function($element, tooltip) {
            const $tooltip = $('<div class="pbc-tooltip">')
                .html('<div class="pbc-tooltip-title">' + tooltip.title + '</div>' +
                      '<div class="pbc-tooltip-content">' + tooltip.content + '</div>');
            
            $('body').append($tooltip);
            
            // Position tooltip
            const elementPos = $element.offset();
            const elementWidth = $element.outerWidth();
            const elementHeight = $element.outerHeight();
            const tooltipWidth = $tooltip.outerWidth();
            const tooltipHeight = $tooltip.outerHeight();
            
            let top, left;
            
            switch (tooltip.position) {
                case 'right':
                    top = elementPos.top + (elementHeight / 2) - (tooltipHeight / 2);
                    left = elementPos.left + elementWidth + 10;
                    break;
                case 'left':
                    top = elementPos.top + (elementHeight / 2) - (tooltipHeight / 2);
                    left = elementPos.left - tooltipWidth - 10;
                    break;
                case 'bottom':
                    top = elementPos.top + elementHeight + 10;
                    left = elementPos.left + (elementWidth / 2) - (tooltipWidth / 2);
                    break;
                default: // top
                    top = elementPos.top - tooltipHeight - 10;
                    left = elementPos.left + (elementWidth / 2) - (tooltipWidth / 2);
            }
            
            $tooltip.css({
                top: top + 'px',
                left: left + 'px'
            }).fadeIn(200);
        },

        // Hide tooltip
        hideTooltip: function() {
            $('.pbc-tooltip').fadeOut(200, function() {
                $(this).remove();
            });
        },

        // Start guided tour
        startTour: function(tourKey) {
            const tour = this.tours[tourKey];
            if (!tour || tour.length === 0) return;
            
            let currentStep = 0;
            const self = this;
            
            function showStep(stepIndex) {
                if (stepIndex >= tour.length) {
                    // Tour complete
                    self.hideTourOverlay();
                    return;
                }
                
                const step = tour[stepIndex];
                const $element = $(step.element);
                
                if ($element.length === 0) {
                    // Element not found, skip to next step
                    showStep(stepIndex + 1);
                    return;
                }
                
                self.showTourStep($element, step, stepIndex, tour.length, function() {
                    showStep(stepIndex + 1);
                });
            }
            
            showStep(0);
        },

        // Show individual tour step
        showTourStep: function($element, step, stepIndex, totalSteps, nextCallback) {
            // Create overlay
            const $overlay = $('<div class="pbc-tour-overlay">');
            const $spotlight = $('<div class="pbc-tour-spotlight">');
            const $popup = $('<div class="pbc-tour-popup">');
            
            // Position spotlight on target element
            const elementPos = $element.offset();
            const elementWidth = $element.outerWidth();
            const elementHeight = $element.outerHeight();
            
            $spotlight.css({
                top: elementPos.top - 5,
                left: elementPos.left - 5,
                width: elementWidth + 10,
                height: elementHeight + 10
            });
            
            // Create popup content
            $popup.html(
                '<div class="pbc-tour-header">' +
                    '<h3>' + step.title + '</h3>' +
                    '<span class="pbc-tour-progress">' + (stepIndex + 1) + ' of ' + totalSteps + '</span>' +
                '</div>' +
                '<div class="pbc-tour-content">' + step.content + '</div>' +
                '<div class="pbc-tour-actions">' +
                    '<button class="button pbc-tour-skip">Skip Tour</button>' +
                    '<button class="button button-primary pbc-tour-next">Next</button>' +
                '</div>'
            );
            
            // Position popup
            $popup.css({
                top: elementPos.top + elementHeight + 20,
                left: elementPos.left
            });
            
            // Add to page
            $overlay.append($spotlight).append($popup);
            $('body').append($overlay);
            
            // Bind events
            $popup.find('.pbc-tour-next').on('click', function() {
                $overlay.remove();
                nextCallback();
            });
            
            $popup.find('.pbc-tour-skip').on('click', function() {
                $overlay.remove();
            });
        },

        // Show contextual help
        showContextualHelp: function($button) {
            const helpType = $button.data('help-type');
            const helpContent = this.getContextualHelpContent(helpType);
            
            if (!helpContent) return;
            
            // Create help modal
            const $modal = $('<div class="pbc-help-modal">');
            const $backdrop = $('<div class="pbc-help-backdrop">');
            const $content = $('<div class="pbc-help-content">');
            
            $content.html(
                '<div class="pbc-help-header">' +
                    '<h2>' + helpContent.title + '</h2>' +
                    '<button class="pbc-help-close">&times;</button>' +
                '</div>' +
                '<div class="pbc-help-body">' + helpContent.content + '</div>' +
                '<div class="pbc-help-footer">' +
                    '<a href="' + helpContent.docLink + '" target="_blank" class="button">View Documentation</a>' +
                    '<button class="button button-primary pbc-help-close">Got It</button>' +
                '</div>'
            );
            
            $modal.append($backdrop).append($content);
            $('body').append($modal);
            
            // Show modal
            $modal.fadeIn(200);
            
            // Bind close events
            $modal.find('.pbc-help-close, .pbc-help-backdrop').on('click', function() {
                $modal.fadeOut(200, function() {
                    $(this).remove();
                });
            });
        },

        // Get contextual help content
        getContextualHelpContent: function(helpType) {
            const helpContent = {
                'pricing-rules': {
                    title: 'Understanding Pricing Rules',
                    content: '<p>Pricing rules allow you to set different prices for different countries. There are three types:</p>' +
                            '<ul>' +
                            '<li><strong>Global Rules:</strong> Apply to all products unless overridden</li>' +
                            '<li><strong>Category Rules:</strong> Apply to all products in a specific category</li>' +
                            '<li><strong>Product Rules:</strong> Apply to individual products only</li>' +
                            '</ul>' +
                            '<p>Rules follow a priority system: Product > Category > Global</p>',
                    docLink: '/docs/user-manual.html#pricing-rules'
                },
                
                'country-detection': {
                    title: 'How Country Detection Works',
                    content: '<p>The plugin can detect customer countries using several methods:</p>' +
                            '<ul>' +
                            '<li><strong>IP Address:</strong> Uses geolocation services to identify location</li>' +
                            '<li><strong>Billing Address:</strong> Uses the country from customer billing info</li>' +
                            '<li><strong>Shipping Address:</strong> Uses the shipping destination country</li>' +
                            '<li><strong>Automatic:</strong> Combines multiple methods for best accuracy</li>' +
                            '</ul>' +
                            '<p>Choose the method that best fits your business model.</p>',
                    docLink: '/docs/user-manual.html#country-detection'
                },
                
                'performance': {
                    title: 'Performance Optimization',
                    content: '<p>Keep your store fast with these optimization tips:</p>' +
                            '<ul>' +
                            '<li><strong>Enable Caching:</strong> Cache pricing rules and calculations</li>' +
                            '<li><strong>Optimize Rules:</strong> Use fewer, more general rules when possible</li>' +
                            '<li><strong>Monitor Performance:</strong> Watch for slow queries and high memory usage</li>' +
                            '<li><strong>Use Object Cache:</strong> Redis or Memcached for high-traffic sites</li>' +
                            '</ul>' +
                            '<p>Well-optimized setups add less than 50ms to page load times.</p>',
                    docLink: '/docs/user-manual.html#performance'
                }
            };
            
            return helpContent[helpType] || null;
        },

        // Hide tour overlay
        hideTourOverlay: function() {
            $('.pbc-tour-overlay').fadeOut(200, function() {
                $(this).remove();
            });
        }
    };

    // Export to global scope
    window.PBC_Help = PBC_Help;

})(jQuery);