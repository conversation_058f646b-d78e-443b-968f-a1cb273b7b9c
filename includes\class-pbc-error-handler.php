<?php
/**
 * Error handling framework for Price by Country
 *
 * @package PriceByCountry
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PBC Error Handler Class
 */
class PBC_Error_Handler {

    /**
     * Error types
     */
    const ERROR_TYPE_PRICING = 'pricing';
    const ERROR_TYPE_COUNTRY_DETECTION = 'country_detection';
    const ERROR_TYPE_DATABASE = 'database';
    const ERROR_TYPE_ADMIN = 'admin';
    const ERROR_TYPE_API = 'api';
    const ERROR_TYPE_GENERAL = 'general';

    /**
     * Error severity levels
     */
    const SEVERITY_LOW = 'low';
    const SEVERITY_MEDIUM = 'medium';
    const SEVERITY_HIGH = 'high';
    const SEVERITY_CRITICAL = 'critical';

    /**
     * Single instance of the class
     *
     * @var PBC_Error_Handler
     */
    private static $instance = null;

    /**
     * Error log entries
     *
     * @var array
     */
    private $error_log = [];

    /**
     * Error counters
     *
     * @var array
     */
    private $error_counters = [];

    /**
     * Get single instance of the class
     *
     * @return PBC_Error_Handler
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->init_error_counters();
        $this->init_hooks();
    }

    /**
     * Initialize error counters
     */
    private function init_error_counters() {
        $this->error_counters = [
            self::ERROR_TYPE_PRICING => 0,
            self::ERROR_TYPE_COUNTRY_DETECTION => 0,
            self::ERROR_TYPE_DATABASE => 0,
            self::ERROR_TYPE_ADMIN => 0,
            self::ERROR_TYPE_API => 0,
            self::ERROR_TYPE_GENERAL => 0,
        ];
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Register shutdown function to handle fatal errors
        register_shutdown_function([$this, 'handle_fatal_error']);

        // Hook into WordPress error handling
        add_action('wp_die_handler', [$this, 'handle_wp_die'], 10, 1);
    }

    /**
     * Handle pricing calculation errors
     *
     * @param Exception|string $error Error object or message
     * @param array $context Error context data
     * @return array Safe fallback price result
     */
    public function handle_pricing_error($error, $context = []) {
        $error_message = is_string($error) ? $error : $error->getMessage();
        
        // Log the error
        $this->log_error(
            self::ERROR_TYPE_PRICING,
            $error_message,
            self::SEVERITY_MEDIUM,
            $context
        );

        // Increment error counter
        $this->increment_error_counter(self::ERROR_TYPE_PRICING);

        // Return safe fallback price
        return $this->get_fallback_price($context);
    }

    /**
     * Handle country detection errors
     *
     * @param Exception|string $error Error object or message
     * @param array $context Error context data
     * @return string Safe fallback country code
     */
    public function handle_country_detection_error($error, $context = []) {
        $error_message = is_string($error) ? $error : $error->getMessage();
        
        // Log the error
        $this->log_error(
            self::ERROR_TYPE_COUNTRY_DETECTION,
            $error_message,
            self::SEVERITY_LOW,
            $context
        );

        // Increment error counter
        $this->increment_error_counter(self::ERROR_TYPE_COUNTRY_DETECTION);

        // Try cached country first
        if (isset($context['cached_country']) && $context['cached_country']) {
            return $context['cached_country'];
        }

        // Fallback to store default country
        return $this->get_fallback_country();
    }

    /**
     * Handle database errors
     *
     * @param Exception|string $error Error object or message
     * @param array $context Error context data
     * @return mixed Safe fallback data or false
     */
    public function handle_database_error($error, $context = []) {
        $error_message = is_string($error) ? $error : $error->getMessage();
        
        // Log the error
        $this->log_error(
            self::ERROR_TYPE_DATABASE,
            $error_message,
            self::SEVERITY_HIGH,
            $context
        );

        // Increment error counter
        $this->increment_error_counter(self::ERROR_TYPE_DATABASE);

        // Return appropriate fallback based on operation
        if (isset($context['operation'])) {
            switch ($context['operation']) {
                case 'get_pricing_rule':
                    return null; // No rule found
                case 'save_pricing_rule':
                    return false; // Save failed
                case 'delete_pricing_rule':
                    return false; // Delete failed
                default:
                    return false;
            }
        }

        return false;
    }

    /**
     * Handle admin interface errors
     *
     * @param Exception|string $error Error object or message
     * @param array $context Error context data
     * @return array Error response for admin interface
     */
    public function handle_admin_error($error, $context = []) {
        $error_message = is_string($error) ? $error : $error->getMessage();
        
        // Log the error
        $this->log_error(
            self::ERROR_TYPE_ADMIN,
            $error_message,
            self::SEVERITY_MEDIUM,
            $context
        );

        // Increment error counter
        $this->increment_error_counter(self::ERROR_TYPE_ADMIN);

        // Return user-friendly error message
        return [
            'success' => false,
            'message' => $this->get_user_friendly_message($error_message, $context),
            'error_code' => $this->generate_error_code(self::ERROR_TYPE_ADMIN)
        ];
    }

    /**
     * Handle API errors
     *
     * @param Exception|string $error Error object or message
     * @param array $context Error context data
     * @return WP_Error API error response
     */
    public function handle_api_error($error, $context = []) {
        $error_message = is_string($error) ? $error : $error->getMessage();
        
        // Log the error
        $this->log_error(
            self::ERROR_TYPE_API,
            $error_message,
            self::SEVERITY_MEDIUM,
            $context
        );

        // Increment error counter
        $this->increment_error_counter(self::ERROR_TYPE_API);

        // Return WP_Error for API responses
        return new WP_Error(
            'pbc_api_error',
            $this->get_user_friendly_message($error_message, $context),
            ['status' => 500]
        );
    }

    /**
     * Handle general errors
     *
     * @param Exception|string $error Error object or message
     * @param array $context Error context data
     * @param string $severity Error severity
     */
    public function handle_general_error($error, $context = [], $severity = self::SEVERITY_LOW) {
        $error_message = is_string($error) ? $error : $error->getMessage();
        
        // Log the error
        $this->log_error(
            self::ERROR_TYPE_GENERAL,
            $error_message,
            $severity,
            $context
        );

        // Increment error counter
        $this->increment_error_counter(self::ERROR_TYPE_GENERAL);
    }

    /**
     * Log error with context
     *
     * @param string $type Error type
     * @param string $message Error message
     * @param string $severity Error severity
     * @param array $context Error context data
     */
    private function log_error($type, $message, $severity, $context = []) {
        $log_entry = [
            'timestamp' => current_time('timestamp'),
            'type' => $type,
            'severity' => $severity,
            'message' => $message,
            'context' => $context,
            'user_id' => get_current_user_id(),
            'ip_address' => $this->get_client_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
            'trace' => $this->get_stack_trace()
        ];

        // Add to in-memory log
        $this->error_log[] = $log_entry;

        // Write to WordPress error log if enabled
        if ($this->is_logging_enabled()) {
            $this->write_to_error_log($log_entry);
        }

        // Store in database for admin viewing
        $this->store_error_in_database($log_entry);

        // Send notifications for critical errors
        if ($severity === self::SEVERITY_CRITICAL) {
            $this->send_critical_error_notification($log_entry);
        }
    }

    /**
     * Get fallback price result
     *
     * @param array $context Price calculation context
     * @return array Fallback price result
     */
    private function get_fallback_price($context) {
        $base_price = isset($context['base_price']) ? floatval($context['base_price']) : 0;
        $product_id = isset($context['product_id']) ? intval($context['product_id']) : 0;
        $country_code = isset($context['country_code']) ? $context['country_code'] : $this->get_fallback_country();

        // Try to get base price from product if not provided
        if (!$base_price && $product_id) {
            $base_price = $this->get_product_base_price($product_id);
        }

        return [
            'original_price' => $base_price,
            'adjusted_price' => $base_price,
            'adjustment_amount' => 0,
            'adjustment_type' => 'none',
            'country_code' => $country_code,
            'rule_source' => 'fallback',
            'currency_code' => get_woocommerce_currency(),
            'calculated_at' => current_time('timestamp'),
            'error_fallback' => true
        ];
    }

    /**
     * Get fallback country code
     *
     * @return string Fallback country code
     */
    private function get_fallback_country() {
        $default_country = get_option('woocommerce_default_country', 'US');
        
        // Extract country code if it includes state
        if (strpos($default_country, ':') !== false) {
            $default_country = explode(':', $default_country)[0];
        }

        return strtoupper($default_country);
    }

    /**
     * Get product base price for fallback
     *
     * @param int $product_id Product ID
     * @return float Base price
     */
    private function get_product_base_price($product_id) {
        if (!function_exists('wc_get_product')) {
            return 0;
        }

        try {
            $product = wc_get_product($product_id);
            if (!$product) {
                return 0;
            }

            $price = $product->get_regular_price();
            return floatval($price);
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * Get user-friendly error message
     *
     * @param string $error_message Original error message
     * @param array $context Error context
     * @return string User-friendly message
     */
    private function get_user_friendly_message($error_message, $context = []) {
        // Map technical errors to user-friendly messages
        $error_mappings = [
            'database' => __('A database error occurred. Please try again later.', 'price-by-country'),
            'connection' => __('Connection error. Please check your internet connection.', 'price-by-country'),
            'permission' => __('You do not have permission to perform this action.', 'price-by-country'),
            'validation' => __('The data you entered is not valid. Please check and try again.', 'price-by-country'),
            'not_found' => __('The requested item was not found.', 'price-by-country'),
            'timeout' => __('The request timed out. Please try again.', 'price-by-country'),
        ];

        // Check for known error patterns
        foreach ($error_mappings as $pattern => $friendly_message) {
            if (stripos($error_message, $pattern) !== false) {
                return $friendly_message;
            }
        }

        // Default friendly message
        return __('An unexpected error occurred. Please try again or contact support if the problem persists.', 'price-by-country');
    }

    /**
     * Generate unique error code
     *
     * @param string $type Error type
     * @return string Error code
     */
    private function generate_error_code($type) {
        return 'PBC_' . strtoupper($type) . '_' . time() . '_' . wp_rand(1000, 9999);
    }

    /**
     * Increment error counter
     *
     * @param string $type Error type
     */
    private function increment_error_counter($type) {
        if (isset($this->error_counters[$type])) {
            $this->error_counters[$type]++;
        }
    }

    /**
     * Get error statistics
     *
     * @return array Error statistics
     */
    public function get_error_stats() {
        return [
            'counters' => $this->error_counters,
            'total_errors' => array_sum($this->error_counters),
            'recent_errors' => count($this->error_log),
            'last_error' => !empty($this->error_log) ? end($this->error_log) : null
        ];
    }

    /**
     * Get recent error log entries
     *
     * @param int $limit Number of entries to return
     * @param string $type Filter by error type (optional)
     * @return array Recent error log entries
     */
    public function get_recent_errors($limit = 50, $type = null) {
        $errors = $this->error_log;

        // Filter by type if specified
        if ($type) {
            $errors = array_filter($errors, function($error) use ($type) {
                return $error['type'] === $type;
            });
        }

        // Sort by timestamp (newest first)
        usort($errors, function($a, $b) {
            return $b['timestamp'] - $a['timestamp'];
        });

        // Limit results
        return array_slice($errors, 0, $limit);
    }

    /**
     * Clear error log
     *
     * @param string $type Optional error type to clear (clears all if not specified)
     */
    public function clear_error_log($type = null) {
        if ($type) {
            $this->error_log = array_filter($this->error_log, function($error) use ($type) {
                return $error['type'] !== $type;
            });
            $this->error_counters[$type] = 0;
        } else {
            $this->error_log = [];
            $this->init_error_counters();
        }

        // Also clear database stored errors
        $this->clear_database_errors($type);
    }

    /**
     * Check if logging is enabled
     *
     * @return bool True if logging is enabled
     */
    private function is_logging_enabled() {
        $core = PBC_Core::get_instance();
        return $core->get_setting('enable_logging', false);
    }

    /**
     * Write error to WordPress error log
     *
     * @param array $log_entry Log entry data
     */
    private function write_to_error_log($log_entry) {
        $log_message = sprintf(
            '[PBC %s] %s - %s (User: %d, IP: %s)',
            strtoupper($log_entry['severity']),
            strtoupper($log_entry['type']),
            $log_entry['message'],
            $log_entry['user_id'],
            $log_entry['ip_address']
        );

        error_log($log_message);
    }

    /**
     * Store error in database for admin viewing
     *
     * @param array $log_entry Log entry data
     */
    private function store_error_in_database($log_entry) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pbc_error_log';

        // Create table if it doesn't exist
        $this->create_error_log_table();

        // Insert error log entry
        $wpdb->insert(
            $table_name,
            [
                'timestamp' => date('Y-m-d H:i:s', $log_entry['timestamp']),
                'type' => $log_entry['type'],
                'severity' => $log_entry['severity'],
                'message' => $log_entry['message'],
                'context' => maybe_serialize($log_entry['context']),
                'user_id' => $log_entry['user_id'],
                'ip_address' => $log_entry['ip_address'],
                'user_agent' => $log_entry['user_agent'],
                'request_uri' => $log_entry['request_uri'],
                'stack_trace' => $log_entry['trace']
            ],
            [
                '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s', '%s', '%s'
            ]
        );
    }

    /**
     * Create error log table
     */
    private function create_error_log_table() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pbc_error_log';
        
        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            timestamp datetime NOT NULL,
            type varchar(50) NOT NULL,
            severity varchar(20) NOT NULL,
            message text NOT NULL,
            context longtext,
            user_id bigint(20) DEFAULT 0,
            ip_address varchar(45) DEFAULT '',
            user_agent text DEFAULT '',
            request_uri text DEFAULT '',
            stack_trace longtext DEFAULT '',
            PRIMARY KEY (id),
            KEY idx_timestamp (timestamp),
            KEY idx_type (type),
            KEY idx_severity (severity),
            KEY idx_user_id (user_id)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Clear database stored errors
     *
     * @param string $type Optional error type to clear
     */
    private function clear_database_errors($type = null) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pbc_error_log';

        if ($type) {
            $wpdb->delete($table_name, ['type' => $type], ['%s']);
        } else {
            $wpdb->query("TRUNCATE TABLE $table_name");
        }
    }

    /**
     * Send critical error notification
     *
     * @param array $log_entry Log entry data
     */
    private function send_critical_error_notification($log_entry) {
        // Get admin email
        $admin_email = get_option('admin_email');
        
        if (!$admin_email) {
            return;
        }

        $subject = sprintf(
            __('[%s] Critical Error in Price by Country Plugin', 'price-by-country'),
            get_bloginfo('name')
        );

        $message = sprintf(
            __("A critical error occurred in the Price by Country plugin:\n\nError Type: %s\nMessage: %s\nTime: %s\nUser: %d\nIP: %s\nURL: %s\n\nPlease check the plugin error logs for more details.", 'price-by-country'),
            $log_entry['type'],
            $log_entry['message'],
            date('Y-m-d H:i:s', $log_entry['timestamp']),
            $log_entry['user_id'],
            $log_entry['ip_address'],
            $log_entry['request_uri']
        );

        wp_mail($admin_email, $subject, $message);
    }

    /**
     * Get client IP address
     *
     * @return string Client IP address
     */
    private function get_client_ip() {
        $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                // Handle comma-separated IPs (from proxies)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }

    /**
     * Get stack trace
     *
     * @return string Stack trace
     */
    private function get_stack_trace() {
        $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 10);
        $formatted_trace = [];

        foreach ($trace as $frame) {
            if (isset($frame['file']) && isset($frame['line'])) {
                $formatted_trace[] = sprintf(
                    '%s:%d %s%s%s()',
                    basename($frame['file']),
                    $frame['line'],
                    $frame['class'] ?? '',
                    $frame['type'] ?? '',
                    $frame['function'] ?? ''
                );
            }
        }

        return implode("\n", $formatted_trace);
    }

    /**
     * Handle fatal errors
     */
    public function handle_fatal_error() {
        $error = error_get_last();
        
        if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
            $this->handle_general_error(
                $error['message'],
                [
                    'file' => $error['file'],
                    'line' => $error['line'],
                    'type' => $error['type']
                ],
                self::SEVERITY_CRITICAL
            );
        }
    }

    /**
     * Handle WordPress wp_die calls
     *
     * @param callable $handler Original handler
     * @return callable Modified handler
     */
    public function handle_wp_die($handler) {
        return function($message, $title = '', $args = []) use ($handler) {
            // Log wp_die calls that might be plugin-related
            if (is_string($message) && (
                strpos($message, 'pbc') !== false || 
                strpos($message, 'price-by-country') !== false
            )) {
                $this->handle_general_error(
                    $message,
                    ['title' => $title, 'args' => $args],
                    self::SEVERITY_HIGH
                );
            }
            
            return call_user_func($handler, $message, $title, $args);
        };
    }

    /**
     * Get error handler instance
     *
     * @return PBC_Error_Handler
     */
    public static function instance() {
        return self::get_instance();
    }
}