# Price by Country for WooCommerce - Troubleshooting Guide

## Table of Contents

1. [Common Issues](#common-issues)
2. [Debugging Steps](#debugging-steps)
3. [Performance Issues](#performance-issues)
4. [Compatibility Problems](#compatibility-problems)
5. [API Issues](#api-issues)
6. [Database Issues](#database-issues)
7. [Logging and Diagnostics](#logging-and-diagnostics)
8. [Support Resources](#support-resources)

## Common Issues

### 1. Prices Not Changing for Different Countries

**Symptoms:**

- Product prices remain the same regardless of customer location
- Country-specific pricing rules appear to be ignored

**Possible Causes:**

- Country detection is failing
- Pricing rules are not properly configured
- Caching is preventing price updates
- WooCommerce hooks are not firing

**Solutions:**

#### Check Country Detection

```php
// Add this to your theme's functions.php for debugging
add_action('wp_footer', function() {
    if (current_user_can('manage_options')) {
        $pbc = PBC_Core::get_instance();
        $detected_country = $pbc->country_detector->detect_country();
        echo "<!-- Detected Country: {$detected_country} -->";
    }
});
```

#### Verify Pricing Rules

1. Go to WooCommerce → Price by Country → Pricing Rules
2. Check if rules exist for the target country
3. Verify rules are active and properly configured
4. Test with a specific product that has rules

#### Clear Cache

```php
// Clear all plugin cache
$pbc = PBC_Core::get_instance();
$pbc->clear_cache();

// Or via WP-CLI
wp transient delete --all
```

#### Check Hook Priority

```php
// Increase hook priority if other plugins interfere
add_filter('woocommerce_product_get_price', 'your_price_function', 999, 2);
```

### 2. Country Detection Not Working

**Symptoms:**

- Always shows default country
- IP-based detection fails
- Address-based detection not updating

**Possible Causes:**

- Geolocation service unavailable
- Caching preventing detection updates
- Proxy/CDN masking real IP
- User data not available

**Solutions:**

#### Test Detection Methods

```php
// Test all detection methods
$pbc = PBC_Core::get_instance();
$detector = $pbc->country_detector;

echo "IP Detection: " . $detector->get_country_from_ip($_SERVER['REMOTE_ADDR']) . "\n";
echo "Billing Detection: " . $detector->get_country_from_billing(get_current_user_id()) . "\n";
echo "Shipping Detection: " . $detector->get_country_from_shipping(get_current_user_id()) . "\n";
```

#### Configure Detection Priority

1. Go to WooCommerce → Settings → Price by Country
2. Set detection method priority
3. Enable fallback methods

#### Handle Proxy/CDN Issues

```php
// Add to wp-config.php for CloudFlare
if (isset($_SERVER['HTTP_CF_CONNECTING_IP'])) {
    $_SERVER['REMOTE_ADDR'] = $_SERVER['HTTP_CF_CONNECTING_IP'];
}

// For other CDNs, check appropriate headers
if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
    $ips = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
    $_SERVER['REMOTE_ADDR'] = trim($ips[0]);
}
```

### 3. Admin Interface Not Loading

**Symptoms:**

- Pricing fields not appearing in product edit
- Admin pages showing errors
- JavaScript errors in console

**Possible Causes:**

- Plugin conflicts
- Theme compatibility issues
- JavaScript errors
- Insufficient permissions

**Solutions:**

#### Check Plugin Conflicts

1. Deactivate all other plugins
2. Test if admin interface loads
3. Reactivate plugins one by one to identify conflicts

#### Check JavaScript Errors

1. Open browser developer tools
2. Check console for JavaScript errors
3. Look for 404 errors on script files

#### Verify Permissions

```php
// Check user capabilities
if (current_user_can('manage_woocommerce')) {
    echo "User has WooCommerce management permissions";
} else {
    echo "User lacks required permissions";
}
```

#### Force Script Loading

```php
// Add to functions.php if scripts not loading
add_action('admin_enqueue_scripts', function() {
    wp_enqueue_script('pbc-admin', PBC_PLUGIN_URL . 'admin/js/pbc-admin.js', array('jquery'), PBC_VERSION, true);
});
```

### 4. Checkout Price Updates Not Working

**Symptoms:**

- Prices don't update when address changes
- Cart totals incorrect after address change
- AJAX errors during checkout

**Possible Causes:**

- AJAX handlers not registered
- Checkout scripts not loading
- Session/cookie issues
- Theme compatibility

**Solutions:**

#### Check AJAX Registration

```php
// Verify AJAX handlers are registered
add_action('wp_ajax_woocommerce_checkout_update_order_review', 'your_handler');
add_action('wp_ajax_nopriv_woocommerce_checkout_update_order_review', 'your_handler');
```

#### Debug AJAX Requests

```javascript
// Add to checkout page for debugging
jQuery(document).ajaxComplete(function (event, xhr, settings) {
  if (settings.url.indexOf("wc-ajax=update_order_review") !== -1) {
    console.log("Checkout update response:", xhr.responseText);
  }
});
```

#### Clear Session Data

```php
// Clear WooCommerce session
WC()->session->destroy_session();

// Clear plugin session data
$pbc = PBC_Core::get_instance();
$pbc->country_detector->clear_session_cache();
```

## Debugging Steps

### 1. Enable Debug Logging

#### WordPress Debug Mode

Add to `wp-config.php`:

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

#### Plugin Debug Logging

```php
// Enable plugin logging
$pbc = PBC_Core::get_instance();
$pbc->update_setting('enable_logging', true);
$pbc->update_setting('log_level', 'debug');
```

#### View Debug Logs

```bash
# WordPress debug log
tail -f /path/to/wordpress/wp-content/debug.log

# Plugin specific logs
tail -f /path/to/wordpress/wp-content/uploads/pbc-logs/pbc-debug.log
```

### 2. Database Debugging

#### Check Table Creation

```sql
-- Verify tables exist
SHOW TABLES LIKE 'wp_pbc_%';

-- Check table structure
DESCRIBE wp_pbc_pricing_rules;
DESCRIBE wp_pbc_country_cache;
```

#### Verify Data Integrity

```sql
-- Check for orphaned rules
SELECT pr.*, p.post_title
FROM wp_pbc_pricing_rules pr
LEFT JOIN wp_posts p ON pr.object_id = p.ID
WHERE pr.rule_type = 'product' AND p.ID IS NULL;

-- Check rule counts
SELECT rule_type, COUNT(*) as count
FROM wp_pbc_pricing_rules
GROUP BY rule_type;
```

### 3. Performance Debugging

#### Query Analysis

```php
// Enable query debugging
define('SAVEQUERIES', true);

// Check slow queries
add_action('wp_footer', function() {
    if (current_user_can('manage_options')) {
        global $wpdb;
        echo '<pre>';
        foreach ($wpdb->queries as $query) {
            if ($query[1] > 0.1) { // Queries taking more than 0.1 seconds
                print_r($query);
            }
        }
        echo '</pre>';
    }
});
```

#### Cache Analysis

```php
// Check cache hit rates
$pbc = PBC_Core::get_instance();
$stats = $pbc->pricing_engine->get_pricing_stats();
echo "Cache entries: " . $stats['cache_entries']['total'];
```

### 4. Memory Usage Debugging

```php
// Monitor memory usage
add_action('wp_footer', function() {
    if (current_user_can('manage_options')) {
        echo "<!-- Memory Usage: " . memory_get_peak_usage(true) / 1024 / 1024 . " MB -->";
    }
});
```

## Performance Issues

### 1. Slow Price Calculations

**Symptoms:**

- Page load times increased
- Timeout errors
- High server resource usage

**Solutions:**

#### Optimize Database Queries

```sql
-- Add indexes if missing
ALTER TABLE wp_pbc_pricing_rules ADD INDEX idx_rule_lookup (rule_type, object_id, country_code);
ALTER TABLE wp_pbc_pricing_rules ADD INDEX idx_country (country_code);
ALTER TABLE wp_pbc_pricing_rules ADD INDEX idx_active (is_active);
```

#### Implement Batch Processing

```php
// Use batch price calculations
$pbc = PBC_Core::get_instance();
$product_ids = array(123, 124, 125);
$prices = $pbc->pricing_engine->get_batch_price_adjustments($product_ids, 'CA');
```

#### Optimize Cache Settings

```php
// Increase cache duration
$pbc = PBC_Core::get_instance();
$pbc->update_setting('cache_duration', 7200); // 2 hours
```

### 2. High Memory Usage

**Solutions:**

#### Limit Batch Sizes

```php
// Process in smaller batches
$batch_size = 50;
$product_chunks = array_chunk($product_ids, $batch_size);
foreach ($product_chunks as $chunk) {
    $prices = $pbc->pricing_engine->get_batch_price_adjustments($chunk, $country);
    // Process chunk
    unset($prices); // Free memory
}
```

#### Clear Object Cache

```php
// Clear object cache periodically
wp_cache_flush();
```

### 3. Database Performance

**Solutions:**

#### Optimize Queries

```php
// Use prepared statements
global $wpdb;
$rules = $wpdb->get_results($wpdb->prepare(
    "SELECT * FROM {$wpdb->prefix}pbc_pricing_rules
     WHERE rule_type = %s AND country_code = %s AND is_active = 1",
    $rule_type, $country_code
));
```

#### Clean Up Old Data

```php
// Clean up expired cache entries
$pbc = PBC_Core::get_instance();
$pbc->database->cleanup_expired_cache();
```

## Compatibility Problems

### 1. Theme Conflicts

**Common Issues:**

- Admin styles not loading correctly
- Frontend price display issues
- JavaScript conflicts

**Solutions:**

#### Check Theme Compatibility

```php
// Test with default theme
switch_theme('twentytwentyfour');
```

#### Override Theme Styles

```css
/* Add to plugin CSS */
.pbc-admin-field {
  display: block !important;
  visibility: visible !important;
}
```

### 2. Plugin Conflicts

**Common Conflicting Plugins:**

- Other pricing plugins
- Caching plugins
- Currency switchers
- Geolocation plugins

**Solutions:**

#### Identify Conflicts

```php
// Check active plugins
$active_plugins = get_option('active_plugins');
foreach ($active_plugins as $plugin) {
    if (strpos($plugin, 'price') !== false || strpos($plugin, 'currency') !== false) {
        echo "Potential conflict: " . $plugin . "\n";
    }
}
```

#### Plugin Load Order

```php
// Ensure plugin loads after WooCommerce
add_action('plugins_loaded', function() {
    if (class_exists('WooCommerce')) {
        // Initialize plugin
        PBC_Core::get_instance();
    }
}, 20);
```

### 3. WooCommerce Version Issues

**Solutions:**

#### Check Version Compatibility

```php
// Check WooCommerce version
if (version_compare(WC_VERSION, '5.0.0', '<')) {
    add_action('admin_notices', function() {
        echo '<div class="notice notice-error"><p>Price by Country requires WooCommerce 5.0.0 or higher.</p></div>';
    });
}
```

#### Handle Deprecated Functions

```php
// Use compatibility functions
if (function_exists('wc_get_product')) {
    $product = wc_get_product($product_id);
} else {
    $product = new WC_Product($product_id);
}
```

## API Issues

### 1. Authentication Failures

**Solutions:**

#### Check API Keys

```bash
# Test API authentication
curl -X GET "https://yoursite.com/wp-json/wc/v3/products" \
  -u "consumer_key:consumer_secret"
```

#### Verify Permissions

```php
// Check user capabilities
if (!current_user_can('manage_woocommerce')) {
    return new WP_Error('insufficient_permissions', 'Access denied');
}
```

### 2. API Response Issues

**Solutions:**

#### Debug API Responses

```php
// Add debug info to API responses
add_filter('pbc_api_product_response', function($response, $product, $country) {
    if (WP_DEBUG) {
        $response['debug'] = array(
            'country_detected' => $country,
            'rules_applied' => $product->get_meta('_pbc_rules_applied'),
            'cache_hit' => $product->get_meta('_pbc_cache_hit')
        );
    }
    return $response;
}, 10, 3);
```

#### Handle API Errors

```php
// Implement proper error handling
try {
    $result = $api_call();
} catch (Exception $e) {
    return new WP_Error('api_error', $e->getMessage(), array('status' => 500));
}
```

## Database Issues

### 1. Table Creation Failures

**Solutions:**

#### Manual Table Creation

```sql
-- Create pricing rules table manually
CREATE TABLE wp_pbc_pricing_rules (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    rule_type enum('global','category','product') NOT NULL,
    object_id bigint(20) DEFAULT NULL,
    country_code varchar(2) NOT NULL,
    adjustment_type enum('fixed','percentage') NOT NULL,
    adjustment_value decimal(10,4) NOT NULL,
    is_active tinyint(1) DEFAULT 1,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_rule_lookup (rule_type, object_id, country_code),
    KEY idx_country (country_code),
    KEY idx_active (is_active)
);
```

#### Check Database Permissions

```php
// Test database write permissions
global $wpdb;
$test_result = $wpdb->query("CREATE TEMPORARY TABLE test_pbc_permissions (id INT)");
if ($test_result === false) {
    echo "Database write permissions issue: " . $wpdb->last_error;
}
```

### 2. Data Corruption

**Solutions:**

#### Repair Tables

```sql
-- Repair corrupted tables
REPAIR TABLE wp_pbc_pricing_rules;
REPAIR TABLE wp_pbc_country_cache;
```

#### Validate Data Integrity

```php
// Check for data inconsistencies
$pbc = PBC_Core::get_instance();
$validation_results = $pbc->database->validate_data_integrity();
foreach ($validation_results as $issue) {
    echo "Data issue: " . $issue['message'] . "\n";
}
```

## Logging and Diagnostics

### 1. Enable Comprehensive Logging

```php
// Enable all logging levels
$pbc = PBC_Core::get_instance();
$pbc->update_setting('enable_logging', true);
$pbc->update_setting('log_level', 'debug');
$pbc->update_setting('log_retention_days', 7);
```

### 2. Custom Debug Functions

```php
// Add debug helper functions
function pbc_debug($message, $data = null) {
    if (WP_DEBUG) {
        error_log("PBC Debug: " . $message);
        if ($data) {
            error_log("PBC Debug Data: " . print_r($data, true));
        }
    }
}

// Usage
pbc_debug("Price calculation started", array(
    'product_id' => $product_id,
    'country' => $country_code
));
```

### 3. System Information

```php
// Generate system information report
function pbc_system_info() {
    $info = array(
        'WordPress Version' => get_bloginfo('version'),
        'WooCommerce Version' => WC_VERSION,
        'Plugin Version' => PBC_VERSION,
        'PHP Version' => PHP_VERSION,
        'MySQL Version' => $GLOBALS['wpdb']->db_version(),
        'Active Theme' => wp_get_theme()->get('Name'),
        'Active Plugins' => count(get_option('active_plugins')),
        'Memory Limit' => ini_get('memory_limit'),
        'Max Execution Time' => ini_get('max_execution_time')
    );

    return $info;
}
```

### 4. Performance Monitoring

```php
// Monitor performance metrics
function pbc_performance_monitor() {
    $start_time = microtime(true);
    $start_memory = memory_get_usage();

    // Your code here

    $end_time = microtime(true);
    $end_memory = memory_get_usage();

    $execution_time = ($end_time - $start_time) * 1000; // milliseconds
    $memory_used = ($end_memory - $start_memory) / 1024; // KB

    error_log("PBC Performance: {$execution_time}ms, {$memory_used}KB");
}
```

## Support Resources

### 1. Log File Locations

- **WordPress Debug Log**: `/wp-content/debug.log`
- **Plugin Debug Log**: `/wp-content/uploads/pbc-logs/pbc-debug.log`
- **Error Log**: `/wp-content/uploads/pbc-logs/pbc-error.log`
- **Performance Log**: `/wp-content/uploads/pbc-logs/pbc-performance.log`

### 2. Useful WP-CLI Commands

```bash
# Clear all transients
wp transient delete --all

# Check plugin status
wp plugin status price-by-country

# Flush rewrite rules
wp rewrite flush

# Check database
wp db check

# Export/import pricing rules
wp pbc export-rules --file=rules.csv
wp pbc import-rules --file=rules.csv
```

### 3. Browser Developer Tools

#### Console Commands

```javascript
// Check for JavaScript errors
console.log("PBC Debug: Checking for errors");

// Monitor AJAX requests
jQuery(document).ajaxComplete(function (event, xhr, settings) {
  if (settings.url.indexOf("pbc") !== -1) {
    console.log("PBC AJAX:", settings.url, xhr.responseText);
  }
});
```

#### Network Tab

- Check for 404 errors on CSS/JS files
- Monitor API request/response times
- Verify AJAX requests are completing

### 4. Database Queries for Diagnostics

```sql
-- Check pricing rules distribution
SELECT rule_type, country_code, COUNT(*) as count
FROM wp_pbc_pricing_rules
GROUP BY rule_type, country_code
ORDER BY count DESC;

-- Find products with most rules
SELECT object_id, COUNT(*) as rule_count
FROM wp_pbc_pricing_rules
WHERE rule_type = 'product'
GROUP BY object_id
ORDER BY rule_count DESC
LIMIT 10;

-- Check cache performance
SELECT COUNT(*) as total_cache_entries
FROM wp_options
WHERE option_name LIKE '_transient_pbc_%';
```

### 5. When to Contact Support

Contact support when:

- Multiple troubleshooting steps have been attempted
- System information and logs are available
- Specific error messages can be provided
- Steps to reproduce the issue are documented

**Information to Include:**

- WordPress and WooCommerce versions
- Plugin version
- Active theme and plugins
- Error messages and logs
- Steps to reproduce
- System information report
