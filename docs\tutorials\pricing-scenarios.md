# Pricing Rule Setup Guides for Different Business Scenarios

## Table of Contents

1. [Basic Store Setup](#basic-store-setup)
2. [Regional Market Pricing](#regional-market-pricing)
3. [VAT and Tax Handling](#vat-and-tax-handling)
4. [Competitive Market Pricing](#competitive-market-pricing)
5. [B2B International Pricing](#b2b-international-pricing)
6. [Seasonal and Promotional Pricing](#seasonal-and-promotional-pricing)
7. [Currency Fluctuation Management](#currency-fluctuation-management)
8. [Market Penetration Strategy](#market-penetration-strategy)

## Basic Store Setup

### Scenario: Small Online Store Going International

**Business Context**:

- US-based store selling handmade crafts
- Expanding to Canada, UK, and Australia
- Simple pricing strategy needed
- Limited technical expertise

**Step-by-Step Setup**:

#### Step 1: Run the Setup Wizard

1. After plugin activation, you'll see the setup wizard
2. Choose "Automatic" country detection
3. Set "United States" as your default country
4. Enable "Create sample rules" option

#### Step 2: Configure Basic Global Rules

1. Go to `WooCommerce → Price by Country → Global Rules`
2. Add these rules:

| Country             | Adjustment Type | Value  | Reasoning             |
| ------------------- | --------------- | ------ | --------------------- |
| Canada (CA)         | Fixed           | +$3.00 | Higher shipping costs |
| United Kingdom (GB) | Percentage      | +15%   | VAT and shipping      |
| Australia (AU)      | Percentage      | +20%   | Distance and shipping |

#### Step 3: Test Your Setup

1. Use a VPN or browser tools to test different countries
2. Check that prices update correctly on product pages
3. Test the checkout process with different countries

#### Step 4: Monitor and Adjust

1. Enable basic analytics in plugin settings
2. Monitor conversion rates by country
3. Adjust pricing based on performance

**Expected Results**:

- $50 US product becomes:
  - Canada: $53.00
  - UK: $57.50
  - Australia: $60.00

## Regional Market Pricing

### Scenario: Electronics Store with Regional Strategy

**Business Context**:

- Electronics retailer with global reach
- Different pricing strategies for different regions
- Accounts for local competition and purchasing power
- Multiple product categories with different margins

**Step-by-Step Setup**:

#### Step 1: Define Your Regions

Create regional groupings:

- **North America**: US (base), Canada, Mexico
- **Europe**: UK, Germany, France, Netherlands, etc.
- **Asia-Pacific**: Australia, Japan, Singapore, etc.
- **Emerging Markets**: India, Brazil, South Africa, etc.

#### Step 2: Set Up Global Regional Rules

1. Go to `WooCommerce → Price by Country → Global Rules`
2. Add rules for each region:

**North America**:

```
US: Base price (no adjustment)
CA: +5% (currency and shipping)
MX: -15% (market positioning)
```

**Europe**:

```
GB: +12% (VAT and market premium)
DE: +10% (strong market, VAT included)
FR: +10% (similar to Germany)
NL: +8% (competitive market)
```

**Asia-Pacific**:

```
AU: +18% (shipping and market premium)
JP: +15% (premium market positioning)
SG: +12% (regional hub pricing)
```

**Emerging Markets**:

```
IN: -25% (price sensitivity)
BR: -20% (market penetration)
ZA: -30% (purchasing power adjustment)
```

#### Step 3: Category-Specific Adjustments

For different product categories, add category rules:

**Smartphones Category**:

1. Go to `Products → Categories → Smartphones → Edit`
2. Enable "Price by Country" for this category
3. Add category-specific adjustments:

```
GB: +20% (higher VAT on electronics)
AU: +25% (import duties and shipping)
IN: -35% (highly competitive market)
```

**Accessories Category**:

1. Lower adjustments due to higher margins:

```
GB: +15%
AU: +20%
IN: -20%
```

#### Step 4: Product-Level Fine-Tuning

For flagship products, set specific rules:

1. Edit your best-selling smartphone
2. Go to "Price by Country" tab
3. Override category rules for key markets:

```
GB: +18% (slightly lower than category for competitiveness)
DE: +15% (aggressive pricing for market share)
JP: +25% (premium positioning)
```

**Expected Results**:

- $1000 smartphone in different markets:
  - US: $1,000 (base)
  - Canada: $1,050
  - UK: $1,180 (product-specific rule)
  - Germany: $1,150 (product-specific rule)
  - Japan: $1,250 (product-specific rule)
  - India: $650 (category rule: -35%)

## VAT and Tax Handling

### Scenario: EU Compliance with VAT-Inclusive Pricing

**Business Context**:

- Store selling to EU customers
- Need to handle different VAT rates
- Want to show VAT-inclusive prices
- Compliance with EU regulations

**Step-by-Step Setup**:

#### Step 1: Configure WooCommerce Tax Settings

1. Go to `WooCommerce → Settings → Tax`
2. Enable "Enable taxes"
3. Set "Prices entered with tax" to "Yes, I will enter prices inclusive of tax"
4. Set up tax rates for EU countries

#### Step 2: Set Base Prices (VAT-Inclusive)

Set your product prices including your home country's VAT:

- If you're in Germany (19% VAT): €119 for a €100 product
- This becomes your base price in the system

#### Step 3: Create VAT Adjustment Rules

Calculate adjustments for different VAT rates:

**Formula**: New Price = Base Price × (1 + New VAT Rate) ÷ (1 + Base VAT Rate)

For a German store (19% base VAT):

| Country     | VAT Rate | Calculation       | Adjustment |
| ----------- | -------- | ----------------- | ---------- |
| France      | 20%      | (1.20 ÷ 1.19) - 1 | +0.84%     |
| UK          | 20%      | (1.20 ÷ 1.19) - 1 | +0.84%     |
| Netherlands | 21%      | (1.21 ÷ 1.19) - 1 | +1.68%     |
| Luxembourg  | 17%      | (1.17 ÷ 1.19) - 1 | -1.68%     |
| Denmark     | 25%      | (1.25 ÷ 1.19) - 1 | +5.04%     |

#### Step 4: Implement the Rules

1. Go to `WooCommerce → Price by Country → Global Rules`
2. Add percentage adjustments:

```
FR: +0.84%
GB: +0.84%
NL: +1.68%
LU: -1.68%
DK: +5.04%
```

#### Step 5: Handle Non-EU Countries

For non-EU countries, remove VAT:

```
US: -15.97% (removes 19% VAT: (1 ÷ 1.19) - 1)
CA: -15.97%
AU: -15.97%
```

**Expected Results**:

- €119 German product (including 19% VAT):
  - France: €120.00 (includes 20% VAT)
  - Netherlands: €121.00 (includes 21% VAT)
  - US: $100.00 (VAT removed, converted to USD)

## Competitive Market Pricing

### Scenario: Highly Competitive International Markets

**Business Context**:

- Fashion retailer facing strong competition
- Need to match local market pricing
- Different competitive landscapes in each country
- Regular price monitoring and adjustments needed

**Step-by-Step Setup**:

#### Step 1: Market Research and Competitor Analysis

Research competitors in each target market:

- **US**: Baseline market, moderate competition
- **UK**: High competition, price-sensitive
- **Germany**: Quality-focused, less price-sensitive
- **France**: Fashion-conscious, premium acceptable
- **Australia**: Limited competition, higher prices acceptable

#### Step 2: Set Competitive Positioning Rules

Based on research, create positioning strategy:

**Aggressive Markets (High Competition)**:

```
GB: -5% (below competitor average)
IT: -8% (very competitive market)
ES: -10% (price-sensitive market)
```

**Premium Markets (Quality Focus)**:

```
DE: +8% (quality positioning)
CH: +15% (premium market)
NO: +12% (high purchasing power)
```

**Balanced Markets**:

```
FR: +3% (slight premium for fashion)
NL: +2% (competitive but quality-focused)
BE: +2% (similar to Netherlands)
```

#### Step 3: Category-Specific Competition Rules

Different categories face different competition levels:

**High Competition Categories (Basic Items)**:

1. Go to `Products → Categories → Basic Tees → Edit`
2. Set aggressive pricing:

```
GB: -8% (very competitive)
DE: +2% (less aggressive due to quality focus)
FR: -2% (competitive but fashion-focused)
```

**Low Competition Categories (Premium Items)**:

1. Go to `Products → Categories → Designer Dresses → Edit`
2. Set premium pricing:

```
GB: +5% (less competition in premium)
DE: +12% (premium quality market)
FR: +8% (fashion-conscious premium)
```

#### Step 4: Dynamic Pricing for Key Products

For your best-selling items, set specific competitive rules:

1. Edit your top-selling dress
2. Research specific competitor prices
3. Set product-specific rules:

```
GB: -3% (specific competitor undercut)
DE: +10% (position above main competitor)
FR: +6% (match premium competitor)
```

#### Step 5: Set Up Monitoring and Alerts

1. Enable detailed logging for price changes
2. Set up regular competitor price monitoring
3. Create a schedule for price reviews:
   - Weekly: High-competition markets
   - Monthly: Balanced markets
   - Quarterly: Premium markets

**Expected Results**:

- $100 dress with competitive positioning:
  - US: $100 (base)
  - UK: $97 (competitive undercut)
  - Germany: $110 (premium positioning)
  - France: $106 (fashion premium)

## B2B International Pricing

### Scenario: B2B Manufacturer with Global Distributors

**Business Context**:

- Industrial equipment manufacturer
- Different distributor agreements by region
- Volume-based pricing considerations
- Long-term contract pricing
- Currency risk management

**Step-by-Step Setup**:

#### Step 1: Define Distributor Regions

Set up regional distributor pricing:

- **North America**: Direct sales and major distributors
- **Europe**: Regional distributor network
- **Asia-Pacific**: Country-specific distributors
- **Latin America**: Regional distributor
- **Africa/Middle East**: Emerging market strategy

#### Step 2: Set Base B2B Pricing Structure

1. Configure WooCommerce for B2B:
   - Use role-based pricing plugin if needed
   - Set up wholesale customer roles
   - Configure minimum order quantities

#### Step 3: Regional Distributor Pricing

Set up pricing based on distributor agreements:

**Established Markets (Lower Margins)**:

```
US: Base price (direct sales)
CA: -5% (established distributor)
GB: -8% (volume distributor agreement)
DE: -6% (strong market distributor)
```

**Growth Markets (Higher Margins)**:

```
AU: +10% (exclusive distributor premium)
JP: +15% (specialized market premium)
SG: +12% (regional hub pricing)
```

**Emerging Markets (Market Development)**:

```
BR: -15% (market development pricing)
IN: -20% (volume growth strategy)
ZA: -18% (market penetration)
MX: -12% (NAFTA advantage)
```

#### Step 4: Product Category Adjustments

Different product lines have different regional strategies:

**Standard Equipment Category**:

1. Go to `Products → Categories → Standard Equipment`
2. Set conservative adjustments:

```
GB: -5% (competitive market)
DE: -3% (quality market)
JP: +8% (specialized requirements)
```

**Specialized Equipment Category**:

1. Higher margins due to specialization:

```
GB: +2% (less competition)
DE: +5% (engineering focus)
JP: +18% (high specialization value)
```

#### Step 5: Currency Risk Management

Add currency risk buffers:

**Volatile Currency Countries**:

```
BR: Additional -3% (currency risk buffer)
TR: Additional -5% (high volatility)
AR: Additional -8% (currency instability)
```

**Stable Currency Countries**:

```
CH: Additional +2% (currency stability premium)
NO: Additional +3% (stable currency)
```

#### Step 6: Contract-Based Pricing

For major distributors, set specific product rules:

1. Edit high-volume products
2. Set distributor-specific pricing:

```
GB (Major Distributor): -12% (volume agreement)
DE (Exclusive Partner): -10% (partnership agreement)
JP (Technical Partner): +5% (value-added services)
```

**Expected Results**:

- $10,000 equipment unit:
  - US: $10,000 (base)
  - UK: $8,800 (major distributor rate)
  - Germany: $9,000 (exclusive partner rate)
  - Japan: $10,500 (technical partner premium)
  - Brazil: $8,200 (market development + currency risk)

## Seasonal and Promotional Pricing

### Scenario: Fashion Retailer with Seasonal Collections

**Business Context**:

- Fashion retailer with seasonal collections
- Different seasons in different hemispheres
- Holiday and promotional periods vary by country
- Need to coordinate with country-specific pricing

**Step-by-Step Setup**:

#### Step 1: Plan Seasonal Strategy

Map out seasonal differences:

- **Northern Hemisphere**: Spring (Mar-May), Summer (Jun-Aug), Fall (Sep-Nov), Winter (Dec-Feb)
- **Southern Hemisphere**: Opposite seasons
- **Tropical Regions**: Wet/dry seasons instead of traditional seasons

#### Step 2: Base Country Pricing Setup

Set up your base pricing structure:

```
US: Base pricing (Northern Hemisphere reference)
GB: +15% (VAT and market premium)
AU: +20% (shipping and opposite seasons)
BR: -10% (market positioning)
```

#### Step 3: Seasonal Adjustment Strategy

Create seasonal pricing layers:

**Winter Collection Launch (October)**:

- Northern markets: Full price
- Southern markets: 20% discount (entering summer)

**Summer Collection Launch (March)**:

- Northern markets: Full price
- Southern markets: Full price (entering winter)

#### Step 4: Implement Seasonal Rules

Use WordPress scheduling or manual updates:

**October - Winter Collection**:

```
US: Base price
GB: +15% (base country rule)
AU: +20% base, -20% seasonal = 0% total
BR: -10% base, -20% seasonal = -28% total
```

**March - Summer Collection**:

```
US: Base price
GB: +15% (base country rule)
AU: +20% (base country rule, no seasonal discount)
BR: -10% (base country rule, no seasonal discount)
```

#### Step 5: Holiday and Promotional Periods

Layer promotional pricing on top of country and seasonal pricing:

**Black Friday (US/CA focus)**:

```
US: -25% promotional
CA: -25% promotional
GB: +15% country, -15% promotional = -2% total
AU: +20% country, -10% promotional = +8% total
```

**Boxing Day (Commonwealth focus)**:

```
GB: +15% country, -30% promotional = -19% total
AU: +20% country, -30% promotional = -16% total
CA: -30% promotional
US: -15% promotional (smaller focus)
```

#### Step 6: Automate Seasonal Changes

Set up automation for seasonal transitions:

1. **WordPress Cron Jobs**:

```php
// Schedule seasonal price changes
wp_schedule_event(strtotime('March 1'), 'yearly', 'pbc_summer_collection_pricing');
wp_schedule_event(strtotime('October 1'), 'yearly', 'pbc_winter_collection_pricing');
```

2. **Manual Bulk Updates**:
   - Export current rules before seasonal change
   - Modify CSV with seasonal adjustments
   - Import updated rules
   - Schedule reversal for end of season

**Expected Results**:

- $100 winter coat in October:
  - US: $100 (base, full season price)
  - UK: $115 (country premium, full season)
  - Australia: $100 (country premium offset by seasonal discount)
  - Brazil: $72 (country discount + seasonal discount)

## Currency Fluctuation Management

### Scenario: Global Retailer Managing Currency Risk

**Business Context**:

- US-based retailer selling globally
- Significant currency fluctuations affecting margins
- Need to maintain competitive pricing while protecting margins
- Different risk tolerance for different markets

**Step-by-Step Setup**:

#### Step 1: Establish Currency Risk Tolerance

Define acceptable margin ranges:

- **Low Risk Markets**: ±2% margin variance acceptable
- **Medium Risk Markets**: ±5% margin variance acceptable
- **High Risk Markets**: ±10% margin variance acceptable

#### Step 2: Set Up Base Currency Buffers

Add currency risk buffers to base pricing:

**Stable Currency Countries (Low Risk)**:

```
GB: +15% market + 2% currency buffer = +17%
DE: +12% market + 2% currency buffer = +14%
CH: +18% market + 1% currency buffer = +19%
```

**Moderate Volatility Countries (Medium Risk)**:

```
CA: +5% market + 3% currency buffer = +8%
AU: +20% market + 4% currency buffer = +24%
JP: +15% market + 3% currency buffer = +18%
```

**High Volatility Countries (High Risk)**:

```
BR: -10% market + 8% currency buffer = -2%
TR: -5% market + 10% currency buffer = +5%
MX: -8% market + 6% currency buffer = -2%
```

#### Step 3: Set Up Regular Review Schedule

Create a systematic review process:

**Weekly Reviews** (High volatility):

- Monitor exchange rates for high-risk countries
- Check if rates exceed tolerance bands
- Prepare adjustment recommendations

**Monthly Reviews** (Medium volatility):

- Review medium-risk country performance
- Analyze margin impact
- Adjust buffers if needed

**Quarterly Reviews** (Low volatility):

- Comprehensive review of all markets
- Strategic pricing adjustments
- Buffer optimization

#### Step 4: Implement Dynamic Adjustment Rules

Create rules for automatic adjustments:

**Trigger-Based Adjustments**:

```
If USD/BRL > 5.5: Increase Brazil pricing by 5%
If USD/BRL < 4.5: Decrease Brazil pricing by 3%
If USD/TRY > 25: Increase Turkey pricing by 8%
If USD/TRY < 20: Decrease Turkey pricing by 5%
```

#### Step 5: Set Up Monitoring and Alerts

1. **Currency Rate Monitoring**:

   - Use financial APIs to track rates
   - Set up alerts for significant movements
   - Create dashboard for rate tracking

2. **Margin Impact Analysis**:
   - Track actual margins by country
   - Compare to target margins
   - Identify adjustment needs

#### Step 6: Emergency Adjustment Procedures

For extreme currency movements:

1. **Immediate Response** (>15% currency movement):

   - Suspend sales to affected country
   - Review and adjust pricing
   - Resume sales with new pricing

2. **Gradual Adjustment** (5-15% currency movement):
   - Implement adjustment over 2-4 weeks
   - Monitor customer response
   - Fine-tune as needed

**Expected Results**:

- $100 product with currency management:
  - Normal conditions: Brazil $98 (-2% with buffer)
  - Strong USD (5% adverse): Brazil $103 (+3% emergency adjustment)
  - Weak USD (5% favorable): Brazil $93 (-7% competitive advantage)

## Market Penetration Strategy

### Scenario: New Market Entry with Aggressive Pricing

**Business Context**:

- Established US brand entering European markets
- Strong competition from local brands
- Need to build market share quickly
- Plan to gradually increase prices as brand recognition grows

**Step-by-Step Setup**:

#### Step 1: Market Entry Analysis

Research each target market:

- **UK**: Mature market, strong local competition
- **Germany**: Quality-focused, premium acceptable
- **France**: Fashion-conscious, brand-sensitive
- **Netherlands**: Price-conscious, value-focused
- **Spain**: Price-sensitive, growing market

#### Step 2: Penetration Pricing Strategy

Set aggressive initial pricing:

**Phase 1: Market Entry (Months 1-6)**:

```
GB: -20% (aggressive entry vs. local competition)
DE: -15% (quality market, less aggressive needed)
FR: -18% (brand building focus)
NL: -25% (very price-conscious market)
ES: -30% (most aggressive for market share)
```

**Phase 2: Brand Building (Months 7-12)**:

```
GB: -15% (reduce discount as brand recognition grows)
DE: -10% (move toward premium positioning)
FR: -12% (maintain competitive edge)
NL: -20% (gradual price increase)
ES: -25% (maintain aggressive positioning)
```

**Phase 3: Margin Recovery (Months 13-18)**:

```
GB: -8% (approach normal market pricing)
DE: -5% (near-premium positioning)
FR: -8% (competitive but profitable)
NL: -12% (value positioning)
ES: -18% (still competitive but improved margins)
```

#### Step 3: Product Category Prioritization

Focus penetration strategy on key categories:

**Hero Products (Maximum Penetration)**:

1. Select 5-10 flagship products
2. Apply maximum discounts:

```
GB: -25% (hero product focus)
DE: -20% (quality hero positioning)
FR: -22% (fashion hero focus)
```

**Supporting Products (Moderate Penetration)**:

1. Apply standard penetration pricing
2. Use category-level rules as defined above

**Premium Products (Minimal Penetration)**:

1. Maintain brand positioning
2. Smaller discounts:

```
GB: -10% (maintain premium perception)
DE: -5% (quality market acceptance)
FR: -8% (fashion premium maintained)
```

#### Step 4: Implement Gradual Price Increases

Set up scheduled price adjustments:

**Month 6 Transition**:

1. Export current pricing rules
2. Modify discounts for Phase 2
3. Import updated rules
4. Monitor customer response

**Month 12 Transition**:

1. Repeat process for Phase 3
2. Analyze market share gains
3. Adjust strategy based on performance

#### Step 5: Performance Monitoring

Track key metrics:

- **Market Share**: Growth in each country
- **Brand Recognition**: Surveys and analytics
- **Customer Acquisition**: New customer rates
- **Margin Impact**: Profitability by market
- **Competitive Response**: Competitor price changes

#### Step 6: Exit Strategy Planning

Plan for mature market pricing:

**Final Target Pricing (Month 18+)**:

```
GB: +5% (slight premium for established brand)
DE: +8% (quality premium positioning)
FR: +6% (fashion brand premium)
NL: -2% (value brand positioning)
ES: -5% (competitive market positioning)
```

**Expected Results**:

- $100 US product market penetration journey:
  - **Phase 1**: UK £64 (aggressive entry)
  - **Phase 2**: UK £68 (brand building)
  - **Phase 3**: UK £74 (margin recovery)
  - **Mature**: UK £84 (established brand premium)

---

## Implementation Tips

### General Best Practices

1. **Start Simple**: Begin with basic global rules, add complexity gradually
2. **Test Thoroughly**: Use staging environments and VPN testing
3. **Monitor Performance**: Track both technical performance and business metrics
4. **Document Changes**: Keep records of pricing strategy changes
5. **Regular Reviews**: Schedule periodic pricing strategy reviews

### Common Pitfalls to Avoid

1. **Over-Complexity**: Don't create too many overlapping rules
2. **Ignoring Taxes**: Remember to account for local tax implications
3. **Currency Confusion**: Keep currency and pricing adjustments separate
4. **Cache Issues**: Always clear caches after pricing changes
5. **Customer Communication**: Be transparent about regional pricing

### Tools and Resources

1. **Currency APIs**: For real-time exchange rate monitoring
2. **Competitor Monitoring**: Tools for tracking competitor prices
3. **Analytics Platforms**: For measuring pricing strategy effectiveness
4. **A/B Testing**: For optimizing pricing strategies
5. **Customer Feedback**: Surveys and feedback collection

Remember: Pricing strategy is an ongoing process. Start with these frameworks and continuously optimize based on your specific market conditions and business goals.
