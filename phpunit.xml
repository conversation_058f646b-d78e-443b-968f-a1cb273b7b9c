<?xml version="1.0" encoding="UTF-8"?>
<phpunit
    bootstrap="tests/bootstrap.php"
    backupGlobals="false"
    colors="true"
    convertErrorsToExceptions="true"
    convertNoticesToExceptions="true"
    convertWarningsToExceptions="true"
    processIsolation="false"
    stopOnFailure="false"
    verbose="true"
>
    <testsuites>
        <testsuite name="unit">
            <directory prefix="test-" suffix=".php">./tests/unit/</directory>
        </testsuite>
        <testsuite name="integration">
            <directory prefix="test-" suffix=".php">./tests/integration/</directory>
        </testsuite>
    </testsuites>
    
    <filter>
        <whitelist processUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">./includes/</directory>
            <exclude>
                <file>./includes/index.php</file>
            </exclude>
        </whitelist>
    </filter>
    
    <logging>
        <log type="coverage-html" target="./tests/coverage/html"/>
        <log type="coverage-clover" target="./tests/coverage/clover.xml"/>
        <log type="junit" target="./tests/coverage/junit.xml"/>
    </logging>
    
    <php>
        <const name="WP_TESTS_MULTISITE" value="0" />
        <server name="SERVER_NAME" value="localhost" />
    </php>
</phpunit>