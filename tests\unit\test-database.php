<?php
/**
 * Unit tests for PBC_Database class
 *
 * @package PriceByCountry
 */

class Test_PBC_Database extends WP_UnitTestCase {

    /**
     * Database instance
     *
     * @var PBC_Database
     */
    private $database;

    /**
     * Test pricing rule data
     *
     * @var array
     */
    private $test_rule_data;

    /**
     * Set up test environment
     */
    public function setUp(): void {
        parent::setUp();

        $this->database = new PBC_Database();
        $this->database->init();

        $this->test_rule_data = [
            'rule_type' => 'product',
            'object_id' => 123,
            'country_code' => 'US',
            'adjustment_type' => 'fixed',
            'adjustment_value' => -10.00,
            'is_active' => 1
        ];
    }

    /**
     * Test database table creation
     */
    public function test_tables_exist() {
        $this->assertTrue($this->database->tables_exist());
    }

    /**
     * Test creating a pricing rule
     */
    public function test_create_pricing_rule() {
        $rule_id = $this->database->create_pricing_rule($this->test_rule_data);

        $this->assertIsInt($rule_id);
        $this->assertGreaterThan(0, $rule_id);

        // Verify rule was created
        $rule = $this->database->get_pricing_rule($rule_id);
        $this->assertNotNull($rule);
        $this->assertEquals('product', $rule->rule_type);
        $this->assertEquals(123, $rule->object_id);
        $this->assertEquals('US', $rule->country_code);
        $this->assertEquals('fixed', $rule->adjustment_type);
        $this->assertEquals(-10.00, $rule->adjustment_value);
        $this->assertEquals(1, $rule->is_active);
    }

    /**
     * Test creating duplicate pricing rule (should update existing)
     */
    public function test_create_duplicate_pricing_rule() {
        // Create first rule
        $rule_id1 = $this->database->create_pricing_rule($this->test_rule_data);
        $this->assertIsInt($rule_id1);

        // Create duplicate rule with different value
        $duplicate_data = $this->test_rule_data;
        $duplicate_data['adjustment_value'] = -20.00;
        
        $rule_id2 = $this->database->create_pricing_rule($duplicate_data);
        
        // Should return same ID (updated existing rule)
        $this->assertEquals($rule_id1, $rule_id2);

        // Verify rule was updated
        $rule = $this->database->get_pricing_rule($rule_id1);
        $this->assertEquals(-20.00, $rule->adjustment_value);
    }

    /**
     * Test getting pricing rule by ID
     */
    public function test_get_pricing_rule() {
        $rule_id = $this->database->create_pricing_rule($this->test_rule_data);
        $rule = $this->database->get_pricing_rule($rule_id);

        $this->assertNotNull($rule);
        $this->assertEquals($rule_id, $rule->id);
        $this->assertEquals('product', $rule->rule_type);

        // Test non-existent rule
        $non_existent = $this->database->get_pricing_rule(99999);
        $this->assertNull($non_existent);
    }

    /**
     * Test getting pricing rule by criteria
     */
    public function test_get_pricing_rule_by_criteria() {
        $rule_id = $this->database->create_pricing_rule($this->test_rule_data);

        $rule = $this->database->get_pricing_rule_by_criteria('product', 123, 'US');
        $this->assertNotNull($rule);
        $this->assertEquals($rule_id, $rule->id);

        // Test non-matching criteria
        $no_rule = $this->database->get_pricing_rule_by_criteria('product', 123, 'CA');
        $this->assertNull($no_rule);
    }

    /**
     * Test getting pricing rules by type
     */
    public function test_get_pricing_rules_by_type() {
        // Create multiple rules
        $this->database->create_pricing_rule($this->test_rule_data);
        
        $category_rule = [
            'rule_type' => 'category',
            'object_id' => 5,
            'country_code' => 'CA',
            'adjustment_type' => 'percentage',
            'adjustment_value' => 15.00,
            'is_active' => 1
        ];
        $this->database->create_pricing_rule($category_rule);

        $global_rule = [
            'rule_type' => 'global',
            'object_id' => null,
            'country_code' => 'UK',
            'adjustment_type' => 'fixed',
            'adjustment_value' => 5.00,
            'is_active' => 1
        ];
        $this->database->create_pricing_rule($global_rule);

        // Test getting product rules
        $product_rules = $this->database->get_pricing_rules_by_type('product', 123);
        $this->assertCount(1, $product_rules);
        $this->assertEquals('product', $product_rules[0]->rule_type);

        // Test getting category rules
        $category_rules = $this->database->get_pricing_rules_by_type('category', 5);
        $this->assertCount(1, $category_rules);
        $this->assertEquals('category', $category_rules[0]->rule_type);

        // Test getting global rules
        $global_rules = $this->database->get_pricing_rules_by_type('global');
        $this->assertCount(1, $global_rules);
        $this->assertEquals('global', $global_rules[0]->rule_type);
    }

    /**
     * Test getting pricing rules by country
     */
    public function test_get_pricing_rules_by_country() {
        // Create rules for different countries
        $this->database->create_pricing_rule($this->test_rule_data); // US

        $ca_rule = $this->test_rule_data;
        $ca_rule['country_code'] = 'CA';
        $ca_rule['rule_type'] = 'category';
        $ca_rule['object_id'] = 5;
        $this->database->create_pricing_rule($ca_rule);

        $us_global = $this->test_rule_data;
        $us_global['rule_type'] = 'global';
        $us_global['object_id'] = null;
        $this->database->create_pricing_rule($us_global);

        $us_rules = $this->database->get_pricing_rules_by_country('US');
        
        $this->assertArrayHasKey('global', $us_rules);
        $this->assertArrayHasKey('category', $us_rules);
        $this->assertArrayHasKey('product', $us_rules);
        
        $this->assertCount(1, $us_rules['product']);
        $this->assertCount(1, $us_rules['global']);
        $this->assertCount(0, $us_rules['category']);

        $ca_rules = $this->database->get_pricing_rules_by_country('CA');
        $this->assertCount(1, $ca_rules['category']);
        $this->assertCount(0, $ca_rules['product']);
        $this->assertCount(0, $ca_rules['global']);
    }

    /**
     * Test updating pricing rule
     */
    public function test_update_pricing_rule() {
        $rule_id = $this->database->create_pricing_rule($this->test_rule_data);

        $updated_data = [
            'rule_type' => 'product',
            'object_id' => 123,
            'country_code' => 'US',
            'adjustment_type' => 'percentage',
            'adjustment_value' => 25.00,
            'is_active' => 0
        ];

        $result = $this->database->update_pricing_rule($rule_id, $updated_data);
        $this->assertTrue($result);

        // Verify update
        $rule = $this->database->get_pricing_rule($rule_id);
        $this->assertEquals('percentage', $rule->adjustment_type);
        $this->assertEquals(25.00, $rule->adjustment_value);
        $this->assertEquals(0, $rule->is_active);
    }

    /**
     * Test deleting pricing rule
     */
    public function test_delete_pricing_rule() {
        $rule_id = $this->database->create_pricing_rule($this->test_rule_data);

        $result = $this->database->delete_pricing_rule($rule_id);
        $this->assertTrue($result);

        // Verify deletion
        $rule = $this->database->get_pricing_rule($rule_id);
        $this->assertNull($rule);
    }

    /**
     * Test deleting pricing rules by object
     */
    public function test_delete_pricing_rules_by_object() {
        // Create multiple rules for same product
        $this->database->create_pricing_rule($this->test_rule_data);
        
        $ca_rule = $this->test_rule_data;
        $ca_rule['country_code'] = 'CA';
        $this->database->create_pricing_rule($ca_rule);

        $uk_rule = $this->test_rule_data;
        $uk_rule['country_code'] = 'UK';
        $this->database->create_pricing_rule($uk_rule);

        // Delete all rules for product 123
        $deleted_count = $this->database->delete_pricing_rules_by_object('product', 123);
        $this->assertEquals(3, $deleted_count);

        // Verify deletion
        $remaining_rules = $this->database->get_pricing_rules_by_type('product', 123);
        $this->assertCount(0, $remaining_rules);
    }

    /**
     * Test country cache operations
     */
    public function test_country_cache_operations() {
        $session_id = 'test_session_123';
        $ip_address = '*************';
        $country_code = 'US';
        $detection_method = 'ip';

        // Save to cache
        $result = $this->database->save_country_cache($session_id, $ip_address, $country_code, $detection_method);
        $this->assertTrue($result);

        // Get from cache by session
        $cached = $this->database->get_cached_country($session_id);
        $this->assertNotNull($cached);
        $this->assertEquals($session_id, $cached->session_id);
        $this->assertEquals($ip_address, $cached->ip_address);
        $this->assertEquals($country_code, $cached->country_code);
        $this->assertEquals($detection_method, $cached->detection_method);

        // Get from cache by IP
        $cached_by_ip = $this->database->get_cached_country_by_ip($ip_address);
        $this->assertNotNull($cached_by_ip);
        $this->assertEquals($country_code, $cached_by_ip->country_code);

        // Delete cache entry
        $delete_result = $this->database->delete_cached_country($session_id);
        $this->assertTrue($delete_result);

        // Verify deletion
        $deleted_cache = $this->database->get_cached_country($session_id);
        $this->assertNull($deleted_cache);
    }

    /**
     * Test invalid country cache data
     */
    public function test_invalid_country_cache_data() {
        // Invalid country code (too long)
        $result = $this->database->save_country_cache('session1', '***********', 'USA', 'ip');
        $this->assertFalse($result);

        // Invalid detection method
        $result = $this->database->save_country_cache('session2', '***********', 'US', 'invalid');
        $this->assertFalse($result);
    }

    /**
     * Test expired cache cleanup
     */
    public function test_cleanup_expired_cache() {
        // Create cache entry that will expire immediately
        $session_id = 'test_expired';
        $result = $this->database->save_country_cache($session_id, '***********', 'US', 'ip', -1);
        $this->assertTrue($result);

        // Run cleanup
        $deleted_count = $this->database->cleanup_expired_cache();
        $this->assertGreaterThanOrEqual(1, $deleted_count);

        // Verify expired entry was removed
        $cached = $this->database->get_cached_country($session_id);
        $this->assertNull($cached);
    }

    /**
     * Test pricing rule data sanitization
     */
    public function test_pricing_rule_data_sanitization() {
        // Test with invalid rule type
        $invalid_data = $this->test_rule_data;
        $invalid_data['rule_type'] = 'invalid_type';
        
        $result = $this->database->create_pricing_rule($invalid_data);
        $this->assertFalse($result);

        // Test with invalid country code
        $invalid_data = $this->test_rule_data;
        $invalid_data['country_code'] = 'INVALID';
        
        $result = $this->database->create_pricing_rule($invalid_data);
        $this->assertFalse($result);

        // Test with invalid adjustment type
        $invalid_data = $this->test_rule_data;
        $invalid_data['adjustment_type'] = 'invalid_adjustment';
        
        $result = $this->database->create_pricing_rule($invalid_data);
        $this->assertFalse($result);

        // Test missing required fields
        $invalid_data = $this->test_rule_data;
        unset($invalid_data['adjustment_value']);
        
        $result = $this->database->create_pricing_rule($invalid_data);
        $this->assertFalse($result);
    }

    /**
     * Test global rule handling (null object_id)
     */
    public function test_global_rule_handling() {
        $global_rule = [
            'rule_type' => 'global',
            'object_id' => 999, // Should be ignored for global rules
            'country_code' => 'US',
            'adjustment_type' => 'fixed',
            'adjustment_value' => 5.00,
            'is_active' => 1
        ];

        $rule_id = $this->database->create_pricing_rule($global_rule);
        $this->assertIsInt($rule_id);

        $rule = $this->database->get_pricing_rule($rule_id);
        $this->assertNull($rule->object_id); // Should be null for global rules
    }

    /**
     * Test paginated pricing rules retrieval
     */
    public function test_get_pricing_rules_paginated() {
        // Create multiple rules
        for ($i = 1; $i <= 25; $i++) {
            $rule_data = $this->test_rule_data;
            $rule_data['object_id'] = $i;
            $this->database->create_pricing_rule($rule_data);
        }

        // Test first page
        $page1 = $this->database->get_pricing_rules_paginated([
            'per_page' => 10,
            'page' => 1
        ]);

        $this->assertCount(10, $page1['items']);
        $this->assertEquals(25, $page1['total_items']);
        $this->assertEquals(1, $page1['current_page']);
        $this->assertEquals(3, $page1['total_pages']);

        // Test second page
        $page2 = $this->database->get_pricing_rules_paginated([
            'per_page' => 10,
            'page' => 2
        ]);

        $this->assertCount(10, $page2['items']);
        $this->assertEquals(2, $page2['current_page']);

        // Test filtering
        $filtered = $this->database->get_pricing_rules_paginated([
            'rule_type' => 'product',
            'country_code' => 'US'
        ]);

        $this->assertEquals(25, $filtered['total_items']);
        foreach ($filtered['items'] as $rule) {
            $this->assertEquals('product', $rule->rule_type);
            $this->assertEquals('US', $rule->country_code);
        }
    }

    /**
     * Test database statistics
     */
    public function test_get_stats() {
        // Create some test data
        $this->database->create_pricing_rule($this->test_rule_data);
        $this->database->save_country_cache('session1', '***********', 'US', 'ip');

        $stats = $this->database->get_stats();

        $this->assertArrayHasKey('pricing_rules', $stats);
        $this->assertArrayHasKey('cache_entries', $stats);
        $this->assertArrayHasKey('expired_cache', $stats);
        $this->assertArrayHasKey('db_version', $stats);

        $this->assertGreaterThanOrEqual(1, $stats['pricing_rules']);
        $this->assertGreaterThanOrEqual(1, $stats['cache_entries']);
        $this->assertEquals(PBC_Database::DB_VERSION, $stats['db_version']);
    }

    /**
     * Test active rules count
     */
    public function test_get_active_rules_count() {
        // Create active rule
        $this->database->create_pricing_rule($this->test_rule_data);

        // Create inactive rule
        $inactive_rule = $this->test_rule_data;
        $inactive_rule['object_id'] = 456;
        $inactive_rule['is_active'] = 0;
        $this->database->create_pricing_rule($inactive_rule);

        $active_count = $this->database->get_active_rules_count();
        $this->assertEquals(1, $active_count);
    }

    /**
     * Test countries in use
     */
    public function test_get_countries_in_use() {
        // Create rules for different countries
        $this->database->create_pricing_rule($this->test_rule_data); // US

        $ca_rule = $this->test_rule_data;
        $ca_rule['country_code'] = 'CA';
        $ca_rule['object_id'] = 456;
        $this->database->create_pricing_rule($ca_rule);

        $uk_rule = $this->test_rule_data;
        $uk_rule['country_code'] = 'UK';
        $uk_rule['object_id'] = 789;
        $this->database->create_pricing_rule($uk_rule);

        $countries = $this->database->get_countries_in_use();
        
        $this->assertContains('US', $countries);
        $this->assertContains('CA', $countries);
        $this->assertContains('UK', $countries);
        $this->assertCount(3, $countries);
    }

    /**
     * Test edge case: very large adjustment values
     */
    public function test_large_adjustment_values() {
        $large_value_rule = $this->test_rule_data;
        $large_value_rule['adjustment_value'] = 999999.9999;

        $rule_id = $this->database->create_pricing_rule($large_value_rule);
        $this->assertIsInt($rule_id);

        $rule = $this->database->get_pricing_rule($rule_id);
        $this->assertEquals(999999.9999, $rule->adjustment_value);
    }

    /**
     * Test edge case: negative adjustment values
     */
    public function test_negative_adjustment_values() {
        $negative_rule = $this->test_rule_data;
        $negative_rule['adjustment_value'] = -999.99;

        $rule_id = $this->database->create_pricing_rule($negative_rule);
        $this->assertIsInt($rule_id);

        $rule = $this->database->get_pricing_rule($rule_id);
        $this->assertEquals(-999.99, $rule->adjustment_value);
    }

    /**
     * Clean up after tests
     */
    public function tearDown(): void {
        // Clean up test data
        global $wpdb;
        $wpdb->query("TRUNCATE TABLE " . $this->database->get_pricing_rules_table());
        $wpdb->query("TRUNCATE TABLE " . $this->database->get_country_cache_table());
        
        parent::tearDown();
    }
}