<?php
/**
 * Integration tests for HPOS compatibility
 *
 * @package PriceByCountry
 */

class Test_HPOS_Compatibility extends WP_UnitTestCase {

    /**
     * PBC Core instance
     *
     * @var PBC_Core
     */
    private $core;

    /**
     * Set up test environment
     */
    public function setUp(): void {
        parent::setUp();

        $this->core = PBC_Core::get_instance();
    }

    /**
     * Test HPOS compatibility declaration
     */
    public function test_hpos_compatibility_declaration() {
        // Check if the compatibility declaration hook is registered
        $this->assertTrue(has_action('before_woocommerce_init'));
        
        // Verify the plugin declares HPOS compatibility
        if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
            // Mock the FeaturesUtil to test compatibility declaration
            $this->assertTrue(true); // Plugin declares compatibility in main file
        } else {
            $this->markTestSkipped('WooCommerce FeaturesUtil not available');
        }
    }

    /**
     * Test HPOS detection method
     */
    public function test_hpos_detection() {
        // Test the HPOS detection method
        $is_hpos_enabled = $this->core->is_hpos_enabled();
        
        // Should return boolean
        $this->assertIsBool($is_hpos_enabled);
        
        // If OrderUtil is available, test with mock
        if (class_exists('\Automattic\WooCommerce\Utilities\OrderUtil')) {
            // This would be true if HPOS is actually enabled
            $this->assertIsBool($is_hpos_enabled);
        } else {
            // If OrderUtil is not available, should return false
            $this->assertFalse($is_hpos_enabled);
        }
    }

    /**
     * Test HPOS-compatible order meta operations
     */
    public function test_hpos_order_meta_operations() {
        // Create a mock order
        $order = $this->create_mock_order();
        
        // Test getting order meta
        $meta_value = $this->core->get_order_meta($order, '_test_meta_key');
        $this->assertEquals('', $meta_value); // Should return empty string for non-existent meta
        
        // Test updating order meta
        $result = $this->core->update_order_meta($order, '_test_meta_key', 'test_value');
        $this->assertTrue($result);
        
        // Test getting the updated meta
        $updated_value = $this->core->get_order_meta($order, '_test_meta_key');
        $this->assertEquals('test_value', $updated_value);
    }

    /**
     * Test order meta operations with order ID
     */
    public function test_order_meta_operations_with_id() {
        // Create a mock order
        $order = $this->create_mock_order();
        $order_id = $order->get_id();
        
        // Test with order ID instead of object
        $result = $this->core->update_order_meta($order_id, '_test_meta_key', 'test_value_by_id');
        $this->assertTrue($result);
        
        $meta_value = $this->core->get_order_meta($order_id, '_test_meta_key');
        $this->assertEquals('test_value_by_id', $meta_value);
    }

    /**
     * Test order meta operations with invalid order
     */
    public function test_order_meta_operations_invalid_order() {
        // Test with invalid order ID
        $result = $this->core->update_order_meta(99999, '_test_meta_key', 'test_value');
        $this->assertFalse($result);
        
        $meta_value = $this->core->get_order_meta(99999, '_test_meta_key');
        $this->assertEquals('', $meta_value);
        
        // Test with null order
        $result = $this->core->update_order_meta(null, '_test_meta_key', 'test_value');
        $this->assertFalse($result);
        
        $meta_value = $this->core->get_order_meta(null, '_test_meta_key');
        $this->assertEquals('', $meta_value);
    }

    /**
     * Test HPOS compatibility check
     */
    public function test_hpos_compatibility_check() {
        // Mock admin environment
        set_current_screen('woocommerce_page_wc-settings');
        $_GET['page'] = 'wc-settings';
        
        // Create admin user
        $admin_user = $this->factory->user->create(['role' => 'administrator']);
        wp_set_current_user($admin_user);
        
        // Test compatibility check doesn't throw errors
        $this->core->check_hpos_compatibility();
        
        // Should not throw any exceptions
        $this->assertTrue(true);
    }

    /**
     * Test HPOS info notice
     */
    public function test_hpos_info_notice() {
        // Mock admin environment
        set_current_screen('woocommerce_page_wc-settings');
        $_GET['page'] = 'wc-settings';
        
        // Create admin user
        $admin_user = $this->factory->user->create(['role' => 'administrator']);
        wp_set_current_user($admin_user);
        
        // Capture notice output
        ob_start();
        $this->core->hpos_info_notice();
        $notice_output = ob_get_clean();
        
        // Should contain HPOS information
        $this->assertStringContains('High-Performance Order Storage', $notice_output);
        $this->assertStringContains('fully compatible', $notice_output);
    }

    /**
     * Test plugin works without HPOS utilities
     */
    public function test_plugin_works_without_hpos_utilities() {
        // Test that plugin methods work even if HPOS utilities are not available
        
        // Create mock order without HPOS
        $order = $this->create_mock_order();
        
        // These should work regardless of HPOS availability
        $result = $this->core->update_order_meta($order, '_test_key', 'test_value');
        $this->assertTrue($result);
        
        $value = $this->core->get_order_meta($order, '_test_key');
        $this->assertEquals('test_value', $value);
    }

    /**
     * Test pricing engine works with HPOS
     */
    public function test_pricing_engine_hpos_compatibility() {
        // Create test product and pricing rule
        $product_id = PBC_Test_Helper::create_test_product('100.00');
        
        $database = new PBC_Database();
        $database->init();
        $database->create_pricing_rule([
            'rule_type' => 'product',
            'object_id' => $product_id,
            'country_code' => 'CA',
            'adjustment_type' => 'fixed',
            'adjustment_value' => -20.00,
            'is_active' => 1
        ]);

        // Create order with the product
        $order = $this->create_mock_order();
        $product = wc_get_product($product_id);
        
        // Mock country detection
        $_SESSION['pbc_test_country'] = 'CA';
        
        // Add product to order (this should work with HPOS)
        $order->add_product($product, 1);
        
        // Store pricing information in order meta (HPOS-compatible way)
        $this->core->update_order_meta($order, '_pbc_pricing_country', 'CA');
        $this->core->update_order_meta($order, '_pbc_original_total', '100.00');
        $this->core->update_order_meta($order, '_pbc_adjusted_total', '80.00');
        
        // Verify meta was stored correctly
        $this->assertEquals('CA', $this->core->get_order_meta($order, '_pbc_pricing_country'));
        $this->assertEquals('100.00', $this->core->get_order_meta($order, '_pbc_original_total'));
        $this->assertEquals('80.00', $this->core->get_order_meta($order, '_pbc_adjusted_total'));
    }

    /**
     * Test database operations are HPOS-compatible
     */
    public function test_database_operations_hpos_compatible() {
        $database = new PBC_Database();
        $database->init();
        
        // Test that database operations don't rely on post meta
        $rule_id = $database->create_pricing_rule([
            'rule_type' => 'global',
            'object_id' => null,
            'country_code' => 'UK',
            'adjustment_type' => 'percentage',
            'adjustment_value' => 10.00,
            'is_active' => 1
        ]);
        
        $this->assertIsInt($rule_id);
        $this->assertGreaterThan(0, $rule_id);
        
        // Verify rule can be retrieved
        $rule = $database->get_pricing_rule($rule_id);
        $this->assertNotNull($rule);
        $this->assertEquals('UK', $rule['country_code']);
    }

    /**
     * Test country detection works with HPOS
     */
    public function test_country_detection_hpos_compatible() {
        $country_detector = new PBC_Country_Detector();
        
        // Test various detection methods
        $methods = ['auto', 'ip', 'billing', 'shipping'];
        
        foreach ($methods as $method) {
            $country = $country_detector->detect_country($method);
            
            // Should return a valid country code or default
            $this->assertIsString($country);
            $this->assertEquals(2, strlen($country)); // Country codes are 2 characters
        }
    }

    /**
     * Create a mock WC_Order for testing
     *
     * @return WC_Order Mock order object
     */
    private function create_mock_order() {
        // Create a simple mock order
        $order = new class {
            private $meta = [];
            private $id = 123;
            
            public function get_id() {
                return $this->id;
            }
            
            public function get_meta($key, $single = true) {
                return $this->meta[$key] ?? ($single ? '' : []);
            }
            
            public function update_meta_data($key, $value) {
                $this->meta[$key] = $value;
            }
            
            public function save() {
                return true;
            }
            
            public function add_product($product, $quantity = 1) {
                // Mock add product functionality
                return true;
            }
        };
        
        return $order;
    }

    /**
     * Clean up after tests
     */
    public function tearDown(): void {
        // Clean up test data
        PBC_Test_Helper::cleanup_test_data();
        
        // Clear session
        unset($_SESSION['pbc_test_country']);
        
        // Reset GET parameters
        unset($_GET['page']);
        
        parent::tearDown();
    }
}