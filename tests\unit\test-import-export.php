<?php
/**
 * Test Import/Export functionality
 *
 * @package PriceByCountry
 */

class Test_PBC_Import_Export extends WP_UnitTestCase {

    private $database;
    private $import_export;

    public function setUp(): void {
        parent::setUp();
        
        $this->database = new PBC_Database();
        $this->database->init();
        $this->import_export = new PBC_Import_Export($this->database);
    }

    public function tearDown(): void {
        // Clean up test data
        global $wpdb;
        $wpdb->query("DELETE FROM {$wpdb->prefix}pbc_pricing_rules WHERE rule_type = 'test'");
        
        parent::tearDown();
    }

    /**
     * Test export functionality
     */
    public function test_export_pricing_rules() {
        // Create test rules
        $this->database->create_pricing_rule(array(
            'rule_type' => 'global',
            'object_id' => null,
            'country_code' => 'US',
            'adjustment_type' => 'percentage',
            'adjustment_value' => -10,
            'is_active' => 1
        ));

        $this->database->create_pricing_rule(array(
            'rule_type' => 'global',
            'object_id' => null,
            'country_code' => 'CA',
            'adjustment_type' => 'fixed',
            'adjustment_value' => 5,
            'is_active' => 1
        ));

        // Test export
        $result = $this->import_export->export_pricing_rules();

        $this->assertTrue($result['success']);
        $this->assertGreaterThanOrEqual(2, $result['rule_count']);
        $this->assertFileExists($result['file_path']);
        
        // Verify CSV content
        $csv_content = file_get_contents($result['file_path']);
        $this->assertStringContainsString('US', $csv_content);
        $this->assertStringContainsString('CA', $csv_content);
        $this->assertStringContainsString('percentage', $csv_content);
        $this->assertStringContainsString('fixed', $csv_content);
        
        // Clean up
        unlink($result['file_path']);
    }

    /**
     * Test export with filters
     */
    public function test_export_with_filters() {
        // Create test rules
        $this->database->create_pricing_rule(array(
            'rule_type' => 'global',
            'object_id' => null,
            'country_code' => 'US',
            'adjustment_type' => 'percentage',
            'adjustment_value' => -10,
            'is_active' => 1
        ));

        $this->database->create_pricing_rule(array(
            'rule_type' => 'global',
            'object_id' => null,
            'country_code' => 'CA',
            'adjustment_type' => 'fixed',
            'adjustment_value' => 5,
            'is_active' => 0
        ));

        // Test export with active filter
        $result = $this->import_export->export_pricing_rules(array(
            'is_active' => 1
        ));

        $this->assertTrue($result['success']);
        
        // Verify only active rules are exported
        $csv_content = file_get_contents($result['file_path']);
        $this->assertStringContainsString('US', $csv_content);
        $this->assertStringNotContainsString('CA', $csv_content);
        
        // Clean up
        unlink($result['file_path']);
    }

    /**
     * Test import preview functionality
     */
    public function test_import_preview() {
        // Create test CSV file
        $csv_content = "Rule Type,Object ID,Country Code,Adjustment Type,Adjustment Value,Is Active\n";
        $csv_content .= "global,,US,percentage,-15,Yes\n";
        $csv_content .= "global,,CA,fixed,10,No\n";
        $csv_content .= "invalid,,XX,percentage,abc,Yes\n"; // Invalid row
        
        $temp_file = tempnam(sys_get_temp_dir(), 'pbc_test_');
        file_put_contents($temp_file, $csv_content);

        // Test preview
        $result = $this->import_export->import_pricing_rules($temp_file, array(
            'preview_only' => true
        ));

        $this->assertTrue($result['success']);
        $this->assertTrue($result['preview']);
        $this->assertEquals(3, $result['total_rows']);
        $this->assertEquals(2, $result['valid_rows']);
        $this->assertEquals(1, $result['error_rows']);
        $this->assertGreaterThan(0, count($result['validation_errors']));

        // Clean up
        unlink($temp_file);
    }

    /**
     * Test import execution
     */
    public function test_import_execution() {
        // Create test CSV file
        $csv_content = "Rule Type,Object ID,Country Code,Adjustment Type,Adjustment Value,Is Active\n";
        $csv_content .= "global,,US,percentage,-15,Yes\n";
        $csv_content .= "global,,CA,fixed,10,No\n";
        
        $temp_file = tempnam(sys_get_temp_dir(), 'pbc_test_');
        file_put_contents($temp_file, $csv_content);

        // Test import
        $result = $this->import_export->import_pricing_rules($temp_file, array(
            'conflict_resolution' => 'skip'
        ));

        $this->assertTrue($result['success']);
        $this->assertEquals(2, $result['total_rows']);
        $this->assertEquals(2, $result['created']);
        $this->assertEquals(0, $result['updated']);
        $this->assertEquals(0, $result['errors']);

        // Verify rules were created
        $rules = $this->database->get_pricing_rules_by_type('global');
        $us_rule = null;
        $ca_rule = null;
        
        foreach ($rules as $rule) {
            if ($rule->country_code === 'US') {
                $us_rule = $rule;
            } elseif ($rule->country_code === 'CA') {
                $ca_rule = $rule;
            }
        }

        $this->assertNotNull($us_rule);
        $this->assertEquals('percentage', $us_rule->adjustment_type);
        $this->assertEquals(-15, $us_rule->adjustment_value);
        $this->assertEquals(1, $us_rule->is_active);

        $this->assertNotNull($ca_rule);
        $this->assertEquals('fixed', $ca_rule->adjustment_type);
        $this->assertEquals(10, $ca_rule->adjustment_value);
        $this->assertEquals(0, $ca_rule->is_active);

        // Clean up
        unlink($temp_file);
    }

    /**
     * Test conflict resolution
     */
    public function test_conflict_resolution() {
        // Create existing rule
        $existing_rule_id = $this->database->create_pricing_rule(array(
            'rule_type' => 'global',
            'object_id' => null,
            'country_code' => 'US',
            'adjustment_type' => 'percentage',
            'adjustment_value' => -10,
            'is_active' => 1
        ));

        // Create test CSV file with conflicting rule
        $csv_content = "Rule Type,Object ID,Country Code,Adjustment Type,Adjustment Value,Is Active\n";
        $csv_content .= "global,,US,percentage,-20,Yes\n";
        
        $temp_file = tempnam(sys_get_temp_dir(), 'pbc_test_');
        file_put_contents($temp_file, $csv_content);

        // Test update conflict resolution
        $result = $this->import_export->import_pricing_rules($temp_file, array(
            'conflict_resolution' => 'update'
        ));

        $this->assertTrue($result['success']);
        $this->assertEquals(0, $result['created']);
        $this->assertEquals(1, $result['updated']);

        // Verify rule was updated
        $updated_rule = $this->database->get_pricing_rule($existing_rule_id);
        $this->assertEquals(-20, $updated_rule->adjustment_value);

        // Clean up
        unlink($temp_file);
    }

    /**
     * Test rollback functionality
     */
    public function test_rollback_functionality() {
        // Create initial rules
        $rule1_id = $this->database->create_pricing_rule(array(
            'rule_type' => 'global',
            'object_id' => null,
            'country_code' => 'US',
            'adjustment_type' => 'percentage',
            'adjustment_value' => -10,
            'is_active' => 1
        ));

        $rule2_id = $this->database->create_pricing_rule(array(
            'rule_type' => 'global',
            'object_id' => null,
            'country_code' => 'CA',
            'adjustment_type' => 'fixed',
            'adjustment_value' => 5,
            'is_active' => 1
        ));

        // Create rollback point
        $rollback_id = $this->import_export->create_rollback_point();
        $this->assertNotEmpty($rollback_id);

        // Modify rules
        $this->database->update_pricing_rule($rule1_id, array('adjustment_value' => -20));
        $this->database->delete_pricing_rule($rule2_id);

        // Add new rule
        $this->database->create_pricing_rule(array(
            'rule_type' => 'global',
            'object_id' => null,
            'country_code' => 'GB',
            'adjustment_type' => 'percentage',
            'adjustment_value' => 15,
            'is_active' => 1
        ));

        // Execute rollback
        $result = $this->import_export->execute_rollback($rollback_id);
        $this->assertTrue($result['success']);

        // Verify rollback worked
        $rules = $this->database->get_pricing_rules_by_type('global');
        $this->assertEquals(2, count($rules));

        $us_rule = null;
        $ca_rule = null;
        $gb_rule = null;

        foreach ($rules as $rule) {
            if ($rule->country_code === 'US') {
                $us_rule = $rule;
            } elseif ($rule->country_code === 'CA') {
                $ca_rule = $rule;
            } elseif ($rule->country_code === 'GB') {
                $gb_rule = $rule;
            }
        }

        // Original rules should be restored
        $this->assertNotNull($us_rule);
        $this->assertEquals(-10, $us_rule->adjustment_value);
        $this->assertNotNull($ca_rule);
        $this->assertEquals(5, $ca_rule->adjustment_value);
        
        // New rule should be gone
        $this->assertNull($gb_rule);

        // Clean up rollback point
        delete_option($rollback_id);
    }

    /**
     * Test CSV validation
     */
    public function test_csv_validation() {
        // Test invalid CSV structure
        $csv_content = "Invalid,Headers\n";
        $csv_content .= "data,here\n";
        
        $temp_file = tempnam(sys_get_temp_dir(), 'pbc_test_');
        file_put_contents($temp_file, $csv_content);

        $result = $this->import_export->import_pricing_rules($temp_file, array(
            'preview_only' => true
        ));

        $this->assertFalse($result['success']);
        $this->assertStringContainsString('Missing required column', $result['message']);

        // Clean up
        unlink($temp_file);
    }

    /**
     * Test scheduled export
     */
    public function test_scheduled_export() {
        $schedule_config = array(
            'frequency' => 'weekly',
            'email_recipients' => array('<EMAIL>'),
            'retention_days' => 30,
            'filters' => array(
                'rule_type' => 'all',
                'is_active' => 1
            )
        );

        $result = $this->import_export->schedule_export($schedule_config);
        $this->assertTrue($result);

        // Verify scheduled event exists
        $next_run = wp_next_scheduled('pbc_scheduled_export', array($schedule_config));
        $this->assertNotFalse($next_run);

        // Clean up
        wp_clear_scheduled_hook('pbc_scheduled_export', array($schedule_config));
    }
}