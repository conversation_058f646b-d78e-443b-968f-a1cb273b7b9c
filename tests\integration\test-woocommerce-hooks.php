<?php
/**
 * Integration tests for WooCommerce hook integration
 *
 * @package PriceByCountry
 */

class Test_WooCommerce_Hooks extends WP_UnitTestCase {

    /**
     * PBC Hooks instance
     *
     * @var PBC_Hooks
     */
    private $hooks;

    /**
     * Test product ID
     *
     * @var int
     */
    private $test_product_id;

    /**
     * Test user ID
     *
     * @var int
     */
    private $test_user_id;

    /**
     * Set up test environment
     */
    public function setUp(): void {
        parent::setUp();

        // Initialize plugin components
        $database = new PBC_Database();
        $database->init();
        
        $country_detector = new PBC_Country_Detector();
        $pricing_engine = new PBC_Pricing_Engine($database, $country_detector);
        
        $this->hooks = new PBC_Hooks($pricing_engine, $country_detector);

        // Create test product
        $this->test_product_id = PBC_Test_Helper::create_test_product('100.00');

        // Create test user
        $this->test_user_id = $this->factory->user->create([
            'role' => 'customer'
        ]);

        // Set up test pricing rule
        $database->create_pricing_rule([
            'rule_type' => 'product',
            'object_id' => $this->test_product_id,
            'country_code' => 'CA',
            'adjustment_type' => 'fixed',
            'adjustment_value' => -20.00,
            'is_active' => 1
        ]);

        // Mock WooCommerce environment
        $this->mock_woocommerce_environment();
    }

    /**
     * Test WooCommerce product price modification
     */
    public function test_woocommerce_product_price_modification() {
        // Mock country detection to return CA
        $_SESSION['pbc_test_country'] = 'CA';

        // Get product
        $product = wc_get_product($this->test_product_id);
        $this->assertNotNull($product);

        // Apply price filter
        $original_price = 100.00;
        $modified_price = apply_filters('woocommerce_product_get_price', $original_price, $product);

        // Price should be modified for CA (100 - 20 = 80)
        $this->assertEquals(80.00, $modified_price);
    }

    /**
     * Test price modification with different countries
     */
    public function test_price_modification_different_countries() {
        $product = wc_get_product($this->test_product_id);
        $original_price = 100.00;

        // Test with CA (has rule)
        $_SESSION['pbc_test_country'] = 'CA';
        $ca_price = apply_filters('woocommerce_product_get_price', $original_price, $product);
        $this->assertEquals(80.00, $ca_price);

        // Test with US (no rule)
        $_SESSION['pbc_test_country'] = 'US';
        $us_price = apply_filters('woocommerce_product_get_price', $original_price, $product);
        $this->assertEquals(100.00, $us_price);
    }

    /**
     * Test cart item price modification
     */
    public function test_cart_item_price_modification() {
        // Mock cart item
        $cart_item = [
            'product_id' => $this->test_product_id,
            'variation_id' => 0,
            'quantity' => 2,
            'data' => wc_get_product($this->test_product_id)
        ];

        // Mock country detection
        $_SESSION['pbc_test_country'] = 'CA';

        // Apply cart item price filter
        do_action('woocommerce_before_calculate_totals', new WC_Cart());

        // Verify cart item price was modified
        $product_price = $cart_item['data']->get_price();
        $this->assertEquals(80.00, $product_price);
    }

    /**
     * Test checkout price display
     */
    public function test_checkout_price_display() {
        // Create order with test product
        $order = wc_create_order();
        $order->add_product(wc_get_product($this->test_product_id), 1);

        // Mock country detection
        $_SESSION['pbc_test_country'] = 'CA';

        // Apply checkout filters
        $order_total = $order->get_total();
        
        // Order total should reflect country pricing
        $this->assertEquals(80.00, $order_total);
    }

    /**
     * Test AJAX country change handling
     */
    public function test_ajax_country_change() {
        // Mock AJAX request
        $_POST['action'] = 'pbc_update_country';
        $_POST['country'] = 'CA';
        $_POST['nonce'] = wp_create_nonce('pbc_ajax_nonce');

        // Set up AJAX environment
        add_action('wp_ajax_pbc_update_country', [$this->hooks, 'handle_country_change_ajax']);
        add_action('wp_ajax_nopriv_pbc_update_country', [$this->hooks, 'handle_country_change_ajax']);

        // Capture output
        ob_start();
        do_action('wp_ajax_pbc_update_country');
        $response = ob_get_clean();

        // Verify response
        $data = json_decode($response, true);
        $this->assertTrue($data['success']);
        $this->assertEquals('CA', $data['country']);
    }

    /**
     * Test product variation price modification
     */
    public function test_product_variation_price_modification() {
        // Create variable product
        $variable_product_id = PBC_Test_Helper::create_test_product('150.00');
        wp_set_object_terms($variable_product_id, 'variable', 'product_type');

        // Create variation
        $variation_id = wp_insert_post([
            'post_type' => 'product_variation',
            'post_parent' => $variable_product_id,
            'post_status' => 'publish'
        ]);

        update_post_meta($variation_id, '_regular_price', '150.00');
        update_post_meta($variation_id, '_price', '150.00');

        // Create pricing rule for variation
        $database = new PBC_Database();
        $database->create_pricing_rule([
            'rule_type' => 'product',
            'object_id' => $variation_id,
            'country_code' => 'UK',
            'adjustment_type' => 'percentage',
            'adjustment_value' => 10.00,
            'is_active' => 1
        ]);

        // Mock country detection
        $_SESSION['pbc_test_country'] = 'UK';

        // Get variation and test price
        $variation = wc_get_product($variation_id);
        $original_price = 150.00;
        $modified_price = apply_filters('woocommerce_product_get_price', $original_price, $variation);

        // Price should be increased by 10% (150 + 15 = 165)
        $this->assertEquals(165.00, $modified_price);
    }

    /**
     * Test category-based pricing rules
     */
    public function test_category_based_pricing() {
        // Create product category
        $category_id = wp_insert_term('Test Category', 'product_cat')['term_id'];
        
        // Assign product to category
        wp_set_object_terms($this->test_product_id, $category_id, 'product_cat');

        // Create category pricing rule
        $database = new PBC_Database();
        $database->create_pricing_rule([
            'rule_type' => 'category',
            'object_id' => $category_id,
            'country_code' => 'AU',
            'adjustment_type' => 'percentage',
            'adjustment_value' => -15.00,
            'is_active' => 1
        ]);

        // Mock country detection
        $_SESSION['pbc_test_country'] = 'AU';

        // Test price modification
        $product = wc_get_product($this->test_product_id);
        $original_price = 100.00;
        $modified_price = apply_filters('woocommerce_product_get_price', $original_price, $product);

        // Price should be reduced by 15% (100 - 15 = 85)
        $this->assertEquals(85.00, $modified_price);
    }

    /**
     * Test global pricing rules
     */
    public function test_global_pricing_rules() {
        // Create global pricing rule
        $database = new PBC_Database();
        $database->create_pricing_rule([
            'rule_type' => 'global',
            'object_id' => null,
            'country_code' => 'JP',
            'adjustment_type' => 'fixed',
            'adjustment_value' => 25.00,
            'is_active' => 1
        ]);

        // Mock country detection
        $_SESSION['pbc_test_country'] = 'JP';

        // Test price modification
        $product = wc_get_product($this->test_product_id);
        $original_price = 100.00;
        $modified_price = apply_filters('woocommerce_product_get_price', $original_price, $product);

        // Price should be increased by 25 (100 + 25 = 125)
        $this->assertEquals(125.00, $modified_price);
    }

    /**
     * Test address change during checkout
     */
    public function test_checkout_address_change() {
        // Mock initial country
        $_SESSION['pbc_test_country'] = 'US';

        // Create cart with product
        WC()->cart->add_to_cart($this->test_product_id, 1);

        // Get initial cart total
        WC()->cart->calculate_totals();
        $initial_total = WC()->cart->get_total('raw');
        $this->assertEquals(100.00, $initial_total);

        // Simulate address change to CA
        $_POST['billing_country'] = 'CA';
        $_SESSION['pbc_test_country'] = 'CA';

        // Trigger address change
        do_action('woocommerce_checkout_update_order_review');

        // Recalculate totals
        WC()->cart->calculate_totals();
        $updated_total = WC()->cart->get_total('raw');

        // Total should be updated for CA pricing (80.00)
        $this->assertEquals(80.00, $updated_total);
    }

    /**
     * Test price display in shop loop
     */
    public function test_shop_loop_price_display() {
        // Mock country detection
        $_SESSION['pbc_test_country'] = 'CA';

        // Get product for shop display
        $product = wc_get_product($this->test_product_id);

        // Test price HTML generation
        $price_html = $product->get_price_html();
        
        // Should contain the modified price (80.00)
        $this->assertStringContains('80', $price_html);
    }

    /**
     * Test price caching with country changes
     */
    public function test_price_caching_with_country_changes() {
        $product = wc_get_product($this->test_product_id);
        $original_price = 100.00;

        // First request with CA
        $_SESSION['pbc_test_country'] = 'CA';
        $ca_price1 = apply_filters('woocommerce_product_get_price', $original_price, $product);
        $this->assertEquals(80.00, $ca_price1);

        // Second request with CA (should use cache)
        $ca_price2 = apply_filters('woocommerce_product_get_price', $original_price, $product);
        $this->assertEquals(80.00, $ca_price2);

        // Change to US (should clear cache and recalculate)
        $_SESSION['pbc_test_country'] = 'US';
        $us_price = apply_filters('woocommerce_product_get_price', $original_price, $product);
        $this->assertEquals(100.00, $us_price);
    }

    /**
     * Test mini cart price updates
     */
    public function test_mini_cart_price_updates() {
        // Add product to cart
        WC()->cart->add_to_cart($this->test_product_id, 2);

        // Mock country change
        $_SESSION['pbc_test_country'] = 'CA';

        // Trigger mini cart update
        do_action('woocommerce_add_to_cart_fragments');

        // Verify cart contents reflect new pricing
        $cart_contents = WC()->cart->get_cart();
        foreach ($cart_contents as $cart_item) {
            if ($cart_item['product_id'] == $this->test_product_id) {
                $this->assertEquals(80.00, $cart_item['data']->get_price());
            }
        }
    }

    /**
     * Test order creation with country pricing
     */
    public function test_order_creation_with_country_pricing() {
        // Mock customer country
        $_SESSION['pbc_test_country'] = 'CA';

        // Create order
        $order = wc_create_order([
            'customer_id' => $this->test_user_id
        ]);

        // Add product to order
        $order->add_product(wc_get_product($this->test_product_id), 1);

        // Calculate order totals
        $order->calculate_totals();

        // Verify order total reflects country pricing
        $this->assertEquals(80.00, $order->get_total());

        // Verify order meta contains country information
        $order_country = $order->get_meta('_pbc_pricing_country');
        $this->assertEquals('CA', $order_country);
    }

    /**
     * Test REST API price responses
     */
    public function test_rest_api_price_responses() {
        // Mock REST API request with country header
        $_SERVER['HTTP_X_PBC_COUNTRY'] = 'CA';

        // Create REST request
        $request = new WP_REST_Request('GET', '/wc/v3/products/' . $this->test_product_id);

        // Apply REST API filters
        $product_data = apply_filters('woocommerce_rest_prepare_product_object', [
            'id' => $this->test_product_id,
            'price' => '100.00',
            'regular_price' => '100.00'
        ], wc_get_product($this->test_product_id), $request);

        // Verify price is modified for API response
        $this->assertEquals('80.00', $product_data['price']);
    }

    /**
     * Mock WooCommerce environment
     */
    private function mock_woocommerce_environment() {
        // Mock WC Cart
        if (!class_exists('WC_Cart')) {
            class WC_Cart {
                private $cart_contents = [];
                private $total = 0;

                public function add_to_cart($product_id, $quantity = 1) {
                    $this->cart_contents[] = [
                        'product_id' => $product_id,
                        'quantity' => $quantity,
                        'data' => wc_get_product($product_id)
                    ];
                }

                public function get_cart() {
                    return $this->cart_contents;
                }

                public function calculate_totals() {
                    $this->total = 0;
                    foreach ($this->cart_contents as $item) {
                        $this->total += $item['data']->get_price() * $item['quantity'];
                    }
                }

                public function get_total($context = 'view') {
                    return $this->total;
                }
            }
        }

        // Mock WC main class
        if (!class_exists('WooCommerce')) {
            class WooCommerce {
                public $cart;
                private static $instance;

                public function __construct() {
                    $this->cart = new WC_Cart();
                }

                public static function instance() {
                    if (is_null(self::$instance)) {
                        self::$instance = new self();
                    }
                    return self::$instance;
                }
            }

            function WC() {
                return WooCommerce::instance();
            }
        }

        // Mock order functions
        if (!function_exists('wc_create_order')) {
            function wc_create_order($args = []) {
                return new WC_Order();
            }
        }

        // Mock WC Order
        if (!class_exists('WC_Order')) {
            class WC_Order {
                private $items = [];
                private $total = 0;
                private $meta = [];

                public function add_product($product, $quantity = 1) {
                    $this->items[] = [
                        'product' => $product,
                        'quantity' => $quantity
                    ];
                }

                public function calculate_totals() {
                    $this->total = 0;
                    foreach ($this->items as $item) {
                        $this->total += $item['product']->get_price() * $item['quantity'];
                    }
                }

                public function get_total() {
                    return $this->total;
                }

                public function get_meta($key) {
                    return $this->meta[$key] ?? '';
                }

                public function update_meta_data($key, $value) {
                    $this->meta[$key] = $value;
                }
            }
        }

        // Initialize WooCommerce
        WC();
    }

    /**
     * Clean up after tests
     */
    public function tearDown(): void {
        // Clean up test data
        PBC_Test_Helper::cleanup_test_data();
        
        // Clear session
        unset($_SESSION['pbc_test_country']);
        
        parent::tearDown();
    }
}