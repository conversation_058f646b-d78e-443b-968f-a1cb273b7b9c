<?php
/**
 * Unit tests for PBC_Country_Detector class
 *
 * @package PriceByCountry
 */

class Test_PBC_Country_Detector extends WP_UnitTestCase {

    /**
     * Country detector instance
     *
     * @var PBC_Country_Detector
     */
    private $country_detector;

    /**
     * Test user ID
     *
     * @var int
     */
    private $test_user_id;

    /**
     * Set up test environment
     */
    public function setUp(): void {
        parent::setUp();

        $this->country_detector = new PBC_Country_Detector();

        // Create test user
        $this->test_user_id = $this->factory->user->create([
            'role' => 'customer'
        ]);

        // Set up test user meta
        update_user_meta($this->test_user_id, 'billing_country', 'US');
        update_user_meta($this->test_user_id, 'shipping_country', 'CA');
    }

    /**
     * Test country detection with auto method
     */
    public function test_detect_country_auto() {
        // Mock WooCommerce customer with shipping address
        $this->mock_woocommerce_customer('CA', 'US');

        $country = $this->country_detector->detect_country('auto');

        // Should prioritize shipping address (CA) over billing (US)
        $this->assertEquals('CA', $country);
    }

    /**
     * Test IP-based country detection
     */
    public function test_get_country_from_ip() {
        // Mock WooCommerce geolocation
        $this->mock_wc_geolocation('***********', 'US');

        $country = $this->country_detector->get_country_from_ip('***********');
        $this->assertEquals('US', $country);
    }

    /**
     * Test IP detection with invalid IP
     */
    public function test_get_country_from_ip_invalid() {
        $country = $this->country_detector->get_country_from_ip('invalid-ip');
        $this->assertFalse($country);
    }

    /**
     * Test billing address country detection
     */
    public function test_get_country_from_billing() {
        // Test with user ID
        $country = $this->country_detector->get_country_from_billing($this->test_user_id);
        $this->assertEquals('US', $country);

        // Test with WooCommerce customer
        $this->mock_woocommerce_customer('CA', 'UK');
        $country = $this->country_detector->get_country_from_billing();
        $this->assertEquals('UK', $country);
    }

    /**
     * Test shipping address country detection
     */
    public function test_get_country_from_shipping() {
        // Test with user ID
        $country = $this->country_detector->get_country_from_shipping($this->test_user_id);
        $this->assertEquals('CA', $country);

        // Test with WooCommerce customer
        $this->mock_woocommerce_customer('AU', 'UK');
        $country = $this->country_detector->get_country_from_shipping();
        $this->assertEquals('AU', $country);
    }

    /**
     * Test country detection priority order
     */
    public function test_detection_priority_order() {
        // Mock all detection methods
        $this->mock_woocommerce_customer('AU', 'UK'); // shipping: AU, billing: UK
        $this->mock_wc_geolocation('***********', 'US'); // IP: US

        $country = $this->country_detector->detect_country('auto');

        // Should prioritize shipping (AU) over billing (UK) and IP (US)
        $this->assertEquals('AU', $country);
    }

    /**
     * Test fallback to billing when shipping not available
     */
    public function test_fallback_to_billing() {
        // Mock customer with no shipping address
        $this->mock_woocommerce_customer('', 'UK');
        $this->mock_wc_geolocation('***********', 'US');

        $country = $this->country_detector->detect_country('auto');

        // Should fallback to billing (UK)
        $this->assertEquals('UK', $country);
    }

    /**
     * Test fallback to IP when addresses not available
     */
    public function test_fallback_to_ip() {
        // Mock customer with no addresses
        $this->mock_woocommerce_customer('', '');
        $this->mock_wc_geolocation('***********', 'JP');

        $country = $this->country_detector->detect_country('auto');

        // Should fallback to IP (JP)
        $this->assertEquals('JP', $country);
    }

    /**
     * Test fallback to default country
     */
    public function test_fallback_to_default() {
        // Mock all detection methods to fail
        $this->mock_woocommerce_customer('', '');
        $this->mock_wc_geolocation_failure();

        // Mock WooCommerce default country setting
        update_option('woocommerce_default_country', 'US:CA');

        $country = $this->country_detector->detect_country('auto');

        // Should fallback to default country (US)
        $this->assertEquals('US', $country);
    }

    /**
     * Test country code validation
     */
    public function test_is_valid_country_code() {
        $this->assertTrue($this->country_detector->is_valid_country_code('US'));
        $this->assertTrue($this->country_detector->is_valid_country_code('CA'));
        $this->assertTrue($this->country_detector->is_valid_country_code('uk')); // Should handle lowercase

        $this->assertFalse($this->country_detector->is_valid_country_code('USA')); // Too long
        $this->assertFalse($this->country_detector->is_valid_country_code('U')); // Too short
        $this->assertFalse($this->country_detector->is_valid_country_code('12')); // Numbers
        $this->assertFalse($this->country_detector->is_valid_country_code('')); // Empty
        $this->assertFalse($this->country_detector->is_valid_country_code(null)); // Null
    }

    /**
     * Test country detection caching
     */
    public function test_country_detection_caching() {
        // Start session for caching
        if (!session_id()) {
            session_start();
        }

        // Mock detection methods
        $this->mock_woocommerce_customer('CA', 'US');

        // First detection should cache result
        $country1 = $this->country_detector->detect_country('auto');
        $this->assertEquals('CA', $country1);

        // Change mock data
        $this->mock_woocommerce_customer('UK', 'AU');

        // Second detection should return cached result
        $country2 = $this->country_detector->detect_country('auto');
        $this->assertEquals('CA', $country2); // Should still be cached CA

        // Clear cache and detect again
        $this->country_detector->clear_cache();
        $country3 = $this->country_detector->detect_country('auto');
        $this->assertEquals('UK', $country3); // Should now return new UK
    }

    /**
     * Test detection result with metadata
     */
    public function test_get_detection_result() {
        $this->mock_woocommerce_customer('AU', 'UK');

        $result = $this->country_detector->get_detection_result('auto');

        $this->assertArrayHasKey('country_code', $result);
        $this->assertArrayHasKey('detection_method', $result);
        $this->assertArrayHasKey('confidence_level', $result);
        $this->assertArrayHasKey('cached', $result);
        $this->assertArrayHasKey('detected_at', $result);

        $this->assertEquals('AU', $result['country_code']);
        $this->assertEquals('shipping', $result['detection_method']);
        $this->assertEquals('high', $result['confidence_level']);
    }

    /**
     * Test confidence levels for different methods
     */
    public function test_confidence_levels() {
        // Test shipping address (high confidence)
        $this->mock_woocommerce_customer('CA', '');
        $result = $this->country_detector->get_detection_result('shipping');
        $this->assertEquals('high', $result['confidence_level']);

        // Test billing address (high confidence)
        $this->mock_woocommerce_customer('', 'US');
        $result = $this->country_detector->get_detection_result('billing');
        $this->assertEquals('high', $result['confidence_level']);

        // Test IP detection (medium confidence)
        $this->mock_woocommerce_customer('', '');
        $this->mock_wc_geolocation('***********', 'UK');
        $result = $this->country_detector->get_detection_result('ip');
        $this->assertEquals('medium', $result['confidence_level']);
    }

    /**
     * Test forced country setting
     */
    public function test_forced_country() {
        // Start session
        if (!session_id()) {
            session_start();
        }

        // Set forced country
        $this->assertTrue($this->country_detector->set_forced_country('JP'));

        // Detection should return forced country
        $country = $this->country_detector->detect_country('auto');
        $this->assertEquals('JP', $country);

        // Forced country should be cleared after use
        $country2 = $this->country_detector->detect_country('auto');
        $this->assertNotEquals('JP', $country2); // Should not be JP anymore
    }

    /**
     * Test invalid forced country
     */
    public function test_invalid_forced_country() {
        $this->assertFalse($this->country_detector->set_forced_country('INVALID'));
        $this->assertFalse($this->country_detector->set_forced_country(''));
        $this->assertFalse($this->country_detector->set_forced_country(null));
    }

    /**
     * Test detection method availability
     */
    public function test_detection_method_availability() {
        // Mock WooCommerce availability
        $this->mock_woocommerce_available();

        $this->assertTrue($this->country_detector->test_detection_method('ip'));
        $this->assertTrue($this->country_detector->test_detection_method('billing'));
        $this->assertTrue($this->country_detector->test_detection_method('shipping'));
        $this->assertFalse($this->country_detector->test_detection_method('invalid'));
    }

    /**
     * Test fallback chain generation
     */
    public function test_fallback_chain() {
        $auto_chain = $this->country_detector->get_fallback_chain('auto');
        $this->assertEquals(['shipping', 'billing', 'ip'], $auto_chain);

        $ip_chain = $this->country_detector->get_fallback_chain('ip');
        $this->assertEquals(['ip', 'shipping', 'billing'], $ip_chain);

        $billing_chain = $this->country_detector->get_fallback_chain('billing');
        $this->assertEquals(['billing', 'shipping', 'ip'], $billing_chain);
    }

    /**
     * Test refresh country detection (bypass cache)
     */
    public function test_refresh_country_detection() {
        // Start session
        if (!session_id()) {
            session_start();
        }

        // Initial detection and cache
        $this->mock_woocommerce_customer('CA', 'US');
        $country1 = $this->country_detector->detect_country('auto');
        $this->assertEquals('CA', $country1);

        // Change data and refresh (should bypass cache)
        $this->mock_woocommerce_customer('UK', 'AU');
        $country2 = $this->country_detector->refresh_country_detection('auto');
        $this->assertEquals('UK', $country2);
    }

    /**
     * Test cache status checking
     */
    public function test_is_cached() {
        // Start session
        if (!session_id()) {
            session_start();
        }

        $this->assertFalse($this->country_detector->is_cached());

        // Detect country to create cache
        $this->mock_woocommerce_customer('CA', 'US');
        $this->country_detector->detect_country('auto');

        $this->assertTrue($this->country_detector->is_cached());

        // Clear cache
        $this->country_detector->clear_cache();
        $this->assertFalse($this->country_detector->is_cached());
    }

    /**
     * Test detection statistics
     */
    public function test_get_detection_stats() {
        $this->mock_woocommerce_available();

        $stats = $this->country_detector->get_detection_stats();

        $this->assertArrayHasKey('ip', $stats);
        $this->assertArrayHasKey('billing', $stats);
        $this->assertArrayHasKey('shipping', $stats);

        foreach ($stats as $method => $data) {
            $this->assertArrayHasKey('available', $data);
            $this->assertArrayHasKey('confidence', $data);
        }
    }

    /**
     * Mock WooCommerce customer
     */
    private function mock_woocommerce_customer($shipping_country, $billing_country) {
        if (!class_exists('WC')) {
            $this->mock_wc_class();
        }

        $mock_customer = $this->createMock(WC_Customer::class);
        $mock_customer->method('get_shipping_country')->willReturn($shipping_country);
        $mock_customer->method('get_billing_country')->willReturn($billing_country);

        WC()->customer = $mock_customer;
    }

    /**
     * Mock WooCommerce geolocation
     */
    private function mock_wc_geolocation($ip, $country) {
        if (!class_exists('WC_Geolocation')) {
            class WC_Geolocation {
                public static function get_ip_address() {
                    return $GLOBALS['mock_ip'] ?? '***********';
                }

                public static function geolocate_ip($ip) {
                    return $GLOBALS['mock_geolocation'] ?? ['country' => 'US'];
                }
            }
        }

        $GLOBALS['mock_ip'] = $ip;
        $GLOBALS['mock_geolocation'] = ['country' => $country];
    }

    /**
     * Mock WooCommerce geolocation failure
     */
    private function mock_wc_geolocation_failure() {
        $GLOBALS['mock_ip'] = false;
        $GLOBALS['mock_geolocation'] = false;
    }

    /**
     * Mock WooCommerce availability
     */
    private function mock_woocommerce_available() {
        if (!class_exists('WC')) {
            $this->mock_wc_class();
        }
        if (!class_exists('WC_Geolocation')) {
            $this->mock_wc_geolocation('***********', 'US');
        }
    }

    /**
     * Mock WC class
     */
    private function mock_wc_class() {
        if (!class_exists('WC')) {
            class WC {
                public $customer;
                private static $instance;

                public static function instance() {
                    if (is_null(self::$instance)) {
                        self::$instance = new self();
                    }
                    return self::$instance;
                }
            }

            function WC() {
                return WC::instance();
            }
        }
    }

    /**
     * Clean up after tests
     */
    public function tearDown(): void {
        // Clean up test data
        wp_delete_user($this->test_user_id);
        
        // Clean up globals
        unset($GLOBALS['mock_ip']);
        unset($GLOBALS['mock_geolocation']);
        
        parent::tearDown();
    }
}