/**
 * Setup Wizard JavaScript
 */

(function ($) {
  "use strict";

  var PBC_Setup = {
    init: function () {
      this.bindEvents();
      this.initTooltips();
      this.initFormValidation();
    },

    bindEvents: function () {
      // Handle detection method changes
      $("#detection_method").on("change", this.onDetectionMethodChange);

      // Handle sample type changes
      $("#sample_type").on("change", this.onSampleTypeChange);

      // <PERSON>le create samples checkbox
      $("#create_samples").on("change", this.onCreateSamplesChange);

      // Handle form submissions
      $("form").on("submit", this.onFormSubmit);

      // Handle skip buttons
      $(".button-next").on("click", this.onSkipStep);
    },

    initTooltips: function () {
      // Add tooltips to help text
      $(".description").each(function () {
        var $this = $(this);
        if ($this.text().length > 100) {
          $this.addClass("pbc-tooltip");
        }
      });
    },

    initFormValidation: function () {
      // Basic form validation
      $("input[required], select[required]").on("blur", function () {
        var $field = $(this);
        var value = $field.val();

        if (!value || value.trim() === "") {
          $field.addClass("error");
        } else {
          $field.removeClass("error");
        }
      });
    },

    onDetectionMethodChange: function () {
      var method = $(this).val();
      var $description = $(this).siblings(".description");

      switch (method) {
        case "auto":
          $description.text(
            "Automatic mode uses shipping address first, then billing address, then IP address as fallback."
          );
          break;
        case "ip":
          $description.text(
            "Uses only the customer's IP address to detect their country. Fast but less accurate."
          );
          break;
        case "billing":
          $description.text(
            "Uses only the billing address country. Requires customers to enter billing information."
          );
          break;
        case "shipping":
          $description.text(
            "Uses only the shipping address country. Requires customers to enter shipping information."
          );
          break;
      }
    },

    onSampleTypeChange: function () {
      var type = $(this).val();
      var $preview = $(".pbc-sample-preview tbody");

      if (type === "fixed") {
        $preview.html(
          "<tr><td>United States</td><td>$0 (Base Price)</td><td>$100.00</td></tr>" +
            "<tr><td>United Kingdom</td><td>+$15</td><td>$115.00</td></tr>" +
            "<tr><td>European Union</td><td>+$10</td><td>$110.00</td></tr>" +
            "<tr><td>Canada</td><td>-$5</td><td>$95.00</td></tr>" +
            "<tr><td>Australia</td><td>+$20</td><td>$120.00</td></tr>"
        );
      } else {
        $preview.html(
          "<tr><td>United States</td><td>0% (Base Price)</td><td>$100.00</td></tr>" +
            "<tr><td>United Kingdom</td><td>+15%</td><td>$115.00</td></tr>" +
            "<tr><td>European Union</td><td>+10%</td><td>$110.00</td></tr>" +
            "<tr><td>Canada</td><td>-5%</td><td>$95.00</td></tr>" +
            "<tr><td>Australia</td><td>+20%</td><td>$120.00</td></tr>"
        );
      }
    },

    onCreateSamplesChange: function () {
      var $sampleType = $("#sample_type");
      var $preview = $(".pbc-sample-preview");

      if ($(this).is(":checked")) {
        $sampleType.prop("disabled", false);
        $preview.show();
      } else {
        $sampleType.prop("disabled", true);
        $preview.hide();
      }
    },

    onFormSubmit: function (e) {
      var $form = $(this);
      var $submitButton = $form.find('button[type="submit"]');

      // Disable submit button to prevent double submission
      $submitButton.prop("disabled", true).text("Processing...");

      // Re-enable after a delay in case of errors
      setTimeout(function () {
        $submitButton.prop("disabled", false).text("Continue");
      }, 5000);
    },

    onSkipStep: function (e) {
      // Add confirmation for skip actions
      if ($(this).hasClass("button-next") && !$(this).is('[type="submit"]')) {
        var confirmed = confirm(
          "Are you sure you want to skip this step? You can configure these settings later."
        );
        if (!confirmed) {
          e.preventDefault();
          return false;
        }
      }
    },
  };

  // Initialize when document is ready
  $(document).ready(function () {
    PBC_Setup.init();
  });

  // Add some visual enhancements
  $(window).on("load", function () {
    // Animate step progression
    $(".pbc-setup-steps .active").addClass("animate-in");

    // Add loading states
    $(".button-primary").on("click", function () {
      var $button = $(this);
      if ($button.is('[type="submit"]')) {
        $button.addClass("loading");
      }
    });
  });
})(jQuery);
