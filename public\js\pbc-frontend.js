/**
 * Frontend JavaScript for Price by Country WooCommerce Plugin
 * Handles real-time price updates and country detection
 */

(function ($) {
  "use strict";

  // Plugin namespace
  window.PBC_Frontend = {
    // Configuration
    config: {
      ajaxUrl: pbc_frontend_vars.ajax_url,
      nonce: pbc_frontend_vars.nonce,
      currentCountry: pbc_frontend_vars.current_country,
      updateDelay: 500, // Delay before updating prices (ms)
      batchSize: 20, // Maximum products to process in one batch
      cacheExpiry: 300000, // Cache expiry time (5 minutes)
      priceSelectors: [
        ".price",
        ".woocommerce-Price-amount",
        ".amount",
        "span.price",
        ".price-current",
        ".regular-price",
        ".sale-price",
      ],
    },

    // State management
    state: {
      isUpdating: false,
      updateTimer: null,
      lastCountry: null,
      priceCache: {},
      requestQueue: [],
      lastUpdate: 0,
      visibleProducts: new Set(),
      intersectionObserver: null,
    },

    /**
     * Initialize frontend functionality
     */
    init: function () {
      this.bindEvents();
      this.initCountryDetection();
      this.setupPriceObserver();
      this.setupIntersectionObserver();
      this.initLazyLoading();

      // Store initial country
      this.state.lastCountry = this.config.currentCountry;

      console.log(
        "PBC Frontend initialized for country:",
        this.config.currentCountry
      );
    },

    /**
     * Bind event handlers
     */
    bindEvents: function () {
      var self = this;

      // Handle address changes in checkout
      $(document.body).on("updated_checkout", function () {
        self.handleCheckoutUpdate();
      });

      // Handle country selector changes (if any custom selectors exist)
      $(document).on("change", ".pbc-country-selector", function () {
        var newCountry = $(this).val();
        self.updateCountry(newCountry);
      });

      // Handle billing country changes
      $(document).on("change", "#billing_country", function () {
        self.handleAddressChange("billing", $(this).val());
      });

      // Handle shipping country changes
      $(document).on("change", "#shipping_country", function () {
        self.handleAddressChange("shipping", $(this).val());
      });

      // Handle variation changes for variable products
      $(document).on(
        "found_variation",
        ".variations_form",
        function (event, variation) {
          self.handleVariationChange(variation);
        }
      );

      // Handle cart updates
      $(document.body).on("updated_cart_totals", function () {
        self.debouncedRefreshPrices();
      });

      // Handle fragments update (for cart widget, etc.)
      $(document.body).on("wc_fragments_refreshed", function () {
        self.debouncedRefreshPrices();
      });
    },

    /**
     * Initialize country detection monitoring
     */
    initCountryDetection: function () {
      // Monitor for any changes that might affect country detection
      this.monitorAddressFields();
    },

    /**
     * Monitor address fields for changes
     */
    monitorAddressFields: function () {
      var self = this;
      var addressFields = [
        "#billing_country",
        "#shipping_country",
        'input[name="billing_country"]',
        'input[name="shipping_country"]',
        'select[name="billing_country"]',
        'select[name="shipping_country"]',
      ];

      $(addressFields.join(", ")).on("change", function () {
        var fieldName = $(this).attr("name");
        var newCountry = $(this).val();

        if (newCountry && newCountry !== self.state.lastCountry) {
          self.debouncedCountryUpdate(newCountry);
        }
      });
    },

    /**
     * Setup price observer for dynamic content
     */
    setupPriceObserver: function () {
      if (!window.MutationObserver) {
        return; // Not supported in older browsers
      }

      var self = this;
      var observer = new MutationObserver(function (mutations) {
        var shouldUpdate = false;

        mutations.forEach(function (mutation) {
          if (mutation.type === "childList") {
            // Check if any price elements were added
            $(mutation.addedNodes)
              .find(self.config.priceSelectors.join(", "))
              .each(function () {
                shouldUpdate = true;
              });
          }
        });

        if (shouldUpdate && !self.state.isUpdating) {
          self.debouncedRefreshPrices();
        }
      });

      // Observe the main content area
      var targetNode = document.querySelector(".woocommerce") || document.body;
      observer.observe(targetNode, {
        childList: true,
        subtree: true,
      });
    },

    /**
     * Handle address change with debouncing
     */
    handleAddressChange: function (type, country) {
      if (!country || country === this.state.lastCountry) {
        return;
      }

      console.log("Address change detected:", type, country);
      this.debouncedCountryUpdate(country);
    },

    /**
     * Handle checkout update
     */
    handleCheckoutUpdate: function () {
      console.log("Checkout updated, refreshing prices");

      // Get the new country from checkout fields
      var billingCountry = $("#billing_country").val();
      var shippingCountry = $("#shipping_country").val();

      // Determine which country to use (shipping takes priority)
      var newCountry = shippingCountry || billingCountry;

      if (newCountry && newCountry !== this.config.currentCountry) {
        this.handleCheckoutCountryChange(newCountry);
      } else {
        this.refreshPrices();
      }
    },

    /**
     * Handle country change during checkout
     */
    handleCheckoutCountryChange: function (newCountry) {
      if (!newCountry || newCountry === this.config.currentCountry) {
        return;
      }

      console.log("Checkout country changed to:", newCountry);

      var self = this;
      var oldCountry = this.config.currentCountry;

      // Show checkout updating indicator
      this.showCheckoutUpdating();

      // Update country and refresh prices
      this.updateCountryPromise(newCountry)
        .then(function () {
          // Show price change notification
          self.showPriceChangeNotification(oldCountry, newCountry);

          // Update checkout totals
          self.updateCheckoutTotals();

          self.hideCheckoutUpdating();
        })
        .catch(function (error) {
          console.error("Error updating checkout prices:", error);
          self.hideCheckoutUpdating();
        });
    },

    /**
     * Handle variation change for variable products
     */
    handleVariationChange: function (variation) {
      if (!variation || !variation.variation_id) {
        return;
      }

      console.log("Variation changed:", variation.variation_id);

      // Update prices for this specific variation
      this.updateVariationPrices(variation);
    },

    /**
     * Update country with debouncing
     */
    debouncedCountryUpdate: function (country) {
      var self = this;

      // Clear existing timer
      if (this.state.updateTimer) {
        clearTimeout(this.state.updateTimer);
      }

      // Set new timer
      this.state.updateTimer = setTimeout(function () {
        self.updateCountry(country);
      }, this.config.updateDelay);
    },

    /**
     * Update country and refresh prices
     */
    updateCountry: function (country) {
      if (
        this.state.isUpdating ||
        !country ||
        country === this.state.lastCountry
      ) {
        return;
      }

      console.log(
        "Updating country from",
        this.state.lastCountry,
        "to",
        country
      );

      var self = this;
      this.state.isUpdating = true;

      // Show loading indicator
      this.showLoadingIndicator();

      $.ajax({
        url: this.config.ajaxUrl,
        type: "POST",
        data: {
          action: "pbc_update_country",
          country_code: country,
          nonce: this.config.nonce,
        },
        success: function (response) {
          if (response.success) {
            self.state.lastCountry = country;
            self.config.currentCountry = country;

            // Clear price cache
            self.state.priceCache = {};

            // Refresh all prices
            self.debouncedRefreshPrices();

            // Show success notification
            self.showNotification("Prices updated for " + country, "success");

            console.log("Country updated successfully:", country);
          } else {
            console.error("Failed to update country:", response.data);
            self.showNotification("Failed to update prices", "error");
          }
        },
        error: function (xhr, status, error) {
          console.error("AJAX error updating country:", error);
          self.showNotification("Error updating prices", "error");
        },
        complete: function () {
          self.state.isUpdating = false;
          self.hideLoadingIndicator();
        },
      });
    },

    /**
     * Refresh all prices on the page
     */
    refreshPrices: function () {
      if (this.state.isUpdating) {
        return;
      }

      console.log("Refreshing prices for country:", this.config.currentCountry);

      var self = this;
      var productIds = this.getVisibleProductIds();

      if (productIds.length === 0) {
        return;
      }

      this.state.isUpdating = true;
      this.showLoadingIndicator();

      $.ajax({
        url: this.config.ajaxUrl,
        type: "POST",
        data: {
          action: "pbc_get_updated_prices",
          product_ids: productIds,
          country_code: this.config.currentCountry,
          nonce: this.config.nonce,
        },
        success: function (response) {
          if (response.success && response.data) {
            self.updatePriceElements(response.data);
            console.log("Prices refreshed successfully");
          } else {
            console.error("Failed to refresh prices:", response.data);
          }
        },
        error: function (xhr, status, error) {
          console.error("AJAX error refreshing prices:", error);
        },
        complete: function () {
          self.state.isUpdating = false;
          self.hideLoadingIndicator();
        },
      });
    },

    /**
     * Get visible product IDs on the current page
     */
    getVisibleProductIds: function () {
      var productIds = [];

      // Look for product IDs in various places
      $("[data-product-id]").each(function () {
        var productId = $(this).data("product-id");
        if (productId && productIds.indexOf(productId) === -1) {
          productIds.push(productId);
        }
      });

      // Look for products in cart
      $(".cart_item").each(function () {
        var productId = $(this).find("[data-product_id]").data("product_id");
        if (productId && productIds.indexOf(productId) === -1) {
          productIds.push(productId);
        }
      });

      // Look for single product page
      if ($(".single-product").length) {
        var productId = $(".single-product")
          .find("[data-product-id]")
          .data("product-id");
        if (!productId) {
          // Try to get from form
          productId = $('form.cart input[name="add-to-cart"]').val();
        }
        if (productId && productIds.indexOf(productId) === -1) {
          productIds.push(productId);
        }
      }

      return productIds
        .map(function (id) {
          return parseInt(id);
        })
        .filter(function (id) {
          return !isNaN(id);
        });
    },

    /**
     * Update price elements with new prices
     */
    updatePriceElements: function (priceData) {
      var self = this;

      $.each(priceData, function (productId, prices) {
        // Update product prices
        self.updateProductPrices(productId, prices);

        // Cache the prices
        self.state.priceCache[productId] = prices;
      });

      // Trigger custom event for other scripts
      $(document).trigger("pbc_prices_updated", [priceData]);
    },

    /**
     * Update prices for a specific product
     */
    updateProductPrices: function (productId, prices) {
      var productSelector = '[data-product-id="' + productId + '"]';
      var $product = $(productSelector);

      if ($product.length === 0) {
        // Try alternative selectors
        $product = $(".product-" + productId);
      }

      if ($product.length === 0) {
        return;
      }

      // Update regular price
      if (prices.regular_price) {
        $product
          .find(".regular-price .amount, .price .amount")
          .first()
          .html(prices.regular_price_html);
      }

      // Update sale price
      if (prices.sale_price) {
        $product.find(".sale-price .amount").html(prices.sale_price_html);
      }

      // Update current price
      if (prices.price) {
        $product.find(".price .amount").first().html(prices.price_html);
      }

      // Add visual feedback
      $product.addClass("pbc-price-updated");
      setTimeout(function () {
        $product.removeClass("pbc-price-updated");
      }, 1000);
    },

    /**
     * Update variation prices for variable products
     */
    updateVariationPrices: function (variation) {
      var $form = $(".variations_form");
      var $priceElement = $form.find(".single_variation .price");

      if ($priceElement.length && variation.price_html) {
        $priceElement.html(variation.price_html);
        $priceElement.addClass("pbc-price-updated");

        setTimeout(function () {
          $priceElement.removeClass("pbc-price-updated");
        }, 1000);
      }
    },

    /**
     * Show loading indicator
     */
    showLoadingIndicator: function () {
      if ($(".pbc-loading-overlay").length === 0) {
        $("body").append(
          '<div class="pbc-loading-overlay"><div class="pbc-spinner"></div></div>'
        );
      }
      $(".pbc-loading-overlay").fadeIn(200);
    },

    /**
     * Hide loading indicator
     */
    hideLoadingIndicator: function () {
      $(".pbc-loading-overlay").fadeOut(200);
    },

    /**
     * Show notification to user
     */
    showNotification: function (message, type) {
      type = type || "info";

      var $notification = $(
        '<div class="pbc-notification pbc-notification-' +
          type +
          '">' +
          message +
          "</div>"
      );

      $("body").append($notification);

      $notification
        .fadeIn(300)
        .delay(3000)
        .fadeOut(300, function () {
          $(this).remove();
        });
    },

    /**
     * Get cached price for product
     */
    getCachedPrice: function (productId) {
      var cached = this.state.priceCache[productId];
      if (cached && Date.now() - cached.timestamp < this.config.cacheExpiry) {
        return cached.data;
      }
      return null;
    },

    /**
     * Clear price cache
     */
    clearPriceCache: function () {
      this.state.priceCache = {};
    },

    /**
     * Setup intersection observer for lazy loading
     */
    setupIntersectionObserver: function () {
      if (!window.IntersectionObserver) {
        return; // Not supported
      }

      var self = this;
      this.state.intersectionObserver = new IntersectionObserver(
        function (entries) {
          entries.forEach(function (entry) {
            if (entry.isIntersecting) {
              var productId = entry.target.getAttribute("data-product-id");
              if (productId) {
                self.state.visibleProducts.add(parseInt(productId));
                self.queuePriceUpdate(parseInt(productId));
              }
            } else {
              var productId = entry.target.getAttribute("data-product-id");
              if (productId) {
                self.state.visibleProducts.delete(parseInt(productId));
              }
            }
          });
        },
        {
          rootMargin: "50px",
          threshold: 0.1,
        }
      );
    },

    /**
     * Initialize lazy loading for products
     */
    initLazyLoading: function () {
      if (!this.state.intersectionObserver) {
        return;
      }

      var self = this;
      $("[data-product-id]").each(function () {
        self.state.intersectionObserver.observe(this);
      });
    },

    /**
     * Queue price update for batch processing
     */
    queuePriceUpdate: function (productId) {
      if (this.state.requestQueue.indexOf(productId) === -1) {
        this.state.requestQueue.push(productId);
      }

      // Process queue after delay
      clearTimeout(this.state.queueTimer);
      this.state.queueTimer = setTimeout(
        this.processUpdateQueue.bind(this),
        this.config.updateDelay
      );
    },

    /**
     * Process queued price updates in batches
     */
    processUpdateQueue: function () {
      if (this.state.requestQueue.length === 0 || this.state.isUpdating) {
        return;
      }

      var self = this;
      var batch = this.state.requestQueue.splice(0, this.config.batchSize);
      var uncachedProducts = [];

      // Check cache first
      batch.forEach(function (productId) {
        if (!self.getCachedPrice(productId)) {
          uncachedProducts.push(productId);
        }
      });

      if (uncachedProducts.length === 0) {
        // All products are cached, process remaining queue
        if (this.state.requestQueue.length > 0) {
          setTimeout(this.processUpdateQueue.bind(this), 100);
        }
        return;
      }

      this.fetchPricesForProducts(uncachedProducts);
    },

    /**
     * Fetch prices for specific products
     */
    fetchPricesForProducts: function (productIds) {
      if (productIds.length === 0) {
        return;
      }

      var self = this;
      this.state.isUpdating = true;

      $.ajax({
        url: this.config.ajaxUrl,
        type: "POST",
        data: {
          action: "pbc_get_batch_prices",
          product_ids: productIds,
          country_code: this.config.currentCountry,
          nonce: this.config.nonce,
        },
        success: function (response) {
          if (response.success && response.data) {
            self.updatePriceElements(response.data);

            // Cache the results with timestamp
            $.each(response.data, function (productId, prices) {
              self.state.priceCache[productId] = {
                data: prices,
                timestamp: Date.now(),
              };
            });

            console.log(
              "Batch prices fetched for",
              productIds.length,
              "products"
            );
          }
        },
        error: function (xhr, status, error) {
          console.error("Error fetching batch prices:", error);
        },
        complete: function () {
          self.state.isUpdating = false;

          // Process remaining queue
          if (self.state.requestQueue.length > 0) {
            setTimeout(self.processUpdateQueue.bind(self), 100);
          }
        },
      });
    },

    /**
     * Optimized refresh prices method
     */
    refreshPricesOptimized: function () {
      if (this.state.isUpdating) {
        return;
      }

      // Only update visible products
      var visibleProductIds = Array.from(this.state.visibleProducts);
      if (visibleProductIds.length === 0) {
        return;
      }

      // Check if we need to update (throttle updates)
      var now = Date.now();
      if (now - this.state.lastUpdate < 1000) {
        return; // Throttle to max 1 update per second
      }
      this.state.lastUpdate = now;

      console.log(
        "Refreshing prices for",
        visibleProductIds.length,
        "visible products"
      );

      // Add to queue for batch processing
      var self = this;
      visibleProductIds.forEach(function (productId) {
        self.queuePriceUpdate(productId);
      });
    },

    /**
     * Debounced version of refresh prices
     */
    debouncedRefreshPrices: function () {
      clearTimeout(this.state.refreshTimer);
      this.state.refreshTimer = setTimeout(
        this.refreshPricesOptimized.bind(this),
        this.config.updateDelay
      );
    },

    /**
     * Update country with Promise support for checkout
     */
    updateCountryPromise: function (country) {
      var self = this;

      return new Promise(function (resolve, reject) {
        if (
          self.state.isUpdating ||
          !country ||
          country === self.state.lastCountry
        ) {
          resolve();
          return;
        }

        console.log(
          "Updating country from",
          self.state.lastCountry,
          "to",
          country
        );

        self.state.isUpdating = true;

        $.ajax({
          url: self.config.ajaxUrl,
          type: "POST",
          data: {
            action: "pbc_update_country",
            country_code: country,
            nonce: self.config.nonce,
          },
          success: function (response) {
            if (response.success) {
              self.state.lastCountry = country;
              self.config.currentCountry = country;

              // Clear price cache
              self.state.priceCache = {};

              // Refresh all prices
              self.refreshPrices();

              console.log("Country updated successfully:", country);
              resolve(response);
            } else {
              console.error("Failed to update country:", response.data);
              reject(new Error(response.data));
            }
          },
          error: function (xhr, status, error) {
            console.error("AJAX error updating country:", error);
            reject(new Error(error));
          },
          complete: function () {
            self.state.isUpdating = false;
          },
        });
      });
    },

    /**
     * Show checkout updating indicator
     */
    showCheckoutUpdating: function () {
      $(".woocommerce-checkout").addClass("pbc-updating");
      $(".checkout-review-order-table").addClass("pbc-price-updating");

      // Add updating message to order total
      if ($(".order-total .pbc-updating-message").length === 0) {
        $(".order-total").append(
          '<span class="pbc-updating-message"> (updating prices...)</span>'
        );
      }
    },

    /**
     * Hide checkout updating indicator
     */
    hideCheckoutUpdating: function () {
      $(".woocommerce-checkout").removeClass("pbc-updating");
      $(".checkout-review-order-table").removeClass("pbc-price-updating");
      $(".pbc-updating-message").remove();
    },

    /**
     * Show price change notification during checkout
     */
    showPriceChangeNotification: function (oldCountry, newCountry) {
      var message = "Prices updated for " + newCountry;
      if (oldCountry && oldCountry !== newCountry) {
        message = "Prices updated from " + oldCountry + " to " + newCountry;
      }

      this.showNotification(message, "info");

      // Also show inline notification in checkout
      this.showCheckoutInlineNotification(message);
    },

    /**
     * Show inline notification in checkout
     */
    showCheckoutInlineNotification: function (message) {
      // Remove existing notifications
      $(".pbc-checkout-notification").remove();

      var $notification = $(
        '<div class="pbc-checkout-notification">' + message + "</div>"
      );

      // Insert after checkout billing/shipping section
      var $insertAfter = $(".woocommerce-checkout-review-order-table").first();
      if ($insertAfter.length === 0) {
        $insertAfter = $("#order_review");
      }

      if ($insertAfter.length > 0) {
        $insertAfter.before($notification);

        // Auto-hide after 5 seconds
        setTimeout(function () {
          $notification.fadeOut(300, function () {
            $(this).remove();
          });
        }, 5000);
      }
    },

    /**
     * Update checkout totals after price changes
     */
    updateCheckoutTotals: function () {
      var self = this;

      // Get current address data
      var billingCountry = $("#billing_country").val();
      var shippingCountry = $("#shipping_country").val();

      // Use checkout-specific AJAX handler for more detailed updates
      $.ajax({
        url: this.config.ajaxUrl,
        type: "POST",
        data: {
          action: "pbc_update_checkout_prices",
          billing_country: billingCountry,
          shipping_country: shippingCountry,
          nonce: this.config.nonce,
        },
        success: function (response) {
          if (response.success && response.data) {
            // Show total change if significant
            if (Math.abs(response.data.total_change) > 0.01) {
              self.showTotalChangeNotification(response.data);
            }

            // Trigger WooCommerce checkout update
            if (typeof wc_checkout_params !== "undefined") {
              $("body").trigger("update_checkout");
            }

            console.log("Checkout totals updated:", response.data);
          }
        },
        error: function (xhr, status, error) {
          console.error("Error updating checkout totals:", error);

          // Fallback to standard WooCommerce update
          if (typeof wc_checkout_params !== "undefined") {
            $("body").trigger("update_checkout");
          }
        },
      });

      // Also trigger cart calculation as fallback
      if (window.wc_cart_fragments_params) {
        $(document.body).trigger("wc_fragment_refresh");
      }
    },

    /**
     * Show notification about total price change
     */
    showTotalChangeNotification: function (data) {
      var changeAmount = data.formatted_total_change;
      var changeType = data.total_change > 0 ? "increase" : "decrease";
      var message =
        "Order total " +
        changeType +
        " by " +
        changeAmount +
        " for " +
        data.country_code;

      this.showNotification(
        message,
        changeType === "increase" ? "warning" : "success"
      );

      // Also add visual indicator to order total
      var $orderTotal = $(".order-total .woocommerce-Price-amount");
      if ($orderTotal.length > 0) {
        var $indicator = $(
          '<span class="pbc-price-change">' + changeAmount + "</span>"
        );
        $orderTotal.after($indicator);

        // Remove indicator after animation
        setTimeout(function () {
          $indicator.fadeOut(300, function () {
            $(this).remove();
          });
        }, 3000);
      }
    },

    /**
     * Debug function to get current state
     */
    getDebugInfo: function () {
      return {
        config: this.config,
        state: this.state,
        visibleProducts: this.getVisibleProductIds(),
      };
    },
  };

  // Initialize when document is ready
  $(document).ready(function () {
    // Only initialize if we have the required variables
    if (typeof pbc_frontend_vars !== "undefined") {
      PBC_Frontend.init();
    } else {
      console.warn("PBC Frontend: Required variables not found");
    }
  });
})(jQuery);
